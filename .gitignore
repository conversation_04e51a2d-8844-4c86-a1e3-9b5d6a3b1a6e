# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# ide
/.idea
.vscode

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# scripts
optimize-images.js
update-image-imports.js
update-scss-imports.js
public/js/snippet-dev.js
public/js/ai-calculator-snippet-dev.js

# misc
.DS_Store
.env.local
.env.development
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

.env.production
