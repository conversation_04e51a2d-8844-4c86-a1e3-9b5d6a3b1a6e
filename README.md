## Project Naming Convention and Rules:

| For                                                | Case               |
|----------------------------------------------------|--------------------|
| Project root folder                                | kebab-case         |
| Normal ts, tsx, scss, json, images and other files | camelCase (lower)  |
| Component tsx and scss files                       | Capitalized        |
| Directory names                                    | kebab-case         |
| Components & pages directory name                  | Capitalized        |
| Ts & tsx variable & function naming                | camelCase (lower)  |
| Ts & tsx class, interface & similar names          | CamelCase (pascal) |
| SCSS class names                                   | kebab-case         |


## Style libraries

We are using bulma (https://bulma.io/documentation/overview/) throughout the project.


## Fonts & Usage:

> **IMPORTANT:** Always use the scss variables to mention the font family. Never hardcode it. If needed, create additional scss variables for new fonts.

| Usage     | Font Family | SCSS Variable   |
|-----------|-------------|-----------------|
| Primary   | Nunito      | $primary-font   |
| Secondary | Archivo     | $secondary-font |


## Icons:

We are using icons from [streamlinehq plump line icons](https://www.streamlinehq.com/icons/plump-line-pro) as downloaded image files. Since there is no CDN available, these icons are loaded, managed and used through our `Icon` react component. Whenever you add a new icon into the pool, please make sure to update this component.

Steps to add new icon image file:

1. Download 48px x 48px image from streamlinehq and save it into  `src/assets/images/icons` folder with a concise and appropriate name.
2. Edit `Icon.tsx` to import and use the new file.
3. Use this new icon by calling the `Icon` component and passing the `iconName` (and optionally width and height) props.


## API Requests to Backend & Authentication:

We use Tanstack Query (https://tanstack.com/query/latest) + axios to make api requests to our backend.
We have both authenticated and unauthenticated APIs. Authentication uses JWT model. Access and refresh tokens are stored in browser localstorage.


## Production / Staging:

> Image Build Command: `docker buildx build --platform linux/amd64 -f Dockerfile.staging -t registry.gitlab.com/aminmemon/abun-react-frontend/abun-react-frontend:<version> .`
> Image Push Command: `docker push registry.gitlab.com/aminmemon/abun-react-frontend/abun-react-frontend:<version> .`

Make sure to run the above command from root of project directory.

---------------

# Getting Started with Create React App

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

# To setup the project follow below instructions:

## Create a directory & Clone the repository

- `cd path/you/want-to-setup-the-project/ && mkdir dir-name`

- `git clone https://gitlab.com/aminmemon/abun-react-frontend.git .`

## Setup env for Development

once cloned create a .env.development ⁠file in root of project and add the following:

```code
REACT_APP_DRF_DOMAIN=https://<your-name>-drf.abun.com
REACT_APP_STRIPE_CHECKOUT_SUCCESS_URL=https://<your-name>-frontend.abun.com/checkout/success?session_id={CHECKOUT_SESSION_ID}
REACT_APP_STRIPE_CHECKOUT_CANCEL_URL=https://<your-name>-frontend.abun.com/show-articles
REACT_APP_LOGO_URL=https://static.abun.com/dev/logo
```

## Install the packages & Start server

run `npm install` and `npm start`

just check if the login/signup page is showing up after that

## Other Commands

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can’t go back!**

If you aren’t satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you’re on your own.

You don’t have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn’t feel obligated to use this feature. However we understand that this tool wouldn’t be useful if you couldn’t customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).
