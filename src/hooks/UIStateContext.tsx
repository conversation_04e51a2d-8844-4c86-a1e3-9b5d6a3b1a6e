import React, { createContext, useContext, useState } from "react";

interface UIStateContextType {
  hamburgerActive: boolean;
  setHamburgerActive: (value: boolean) => void;
}

const defaultContext: UIStateContextType = {
  hamburgerActive: false,
  setHamburgerActive: () => {},
};

export const UIStateContext = createContext<UIStateContextType>(defaultContext);

export const UIStateProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [hamburgerActive, setHamburgerActive] = useState(false);

  return (
    <UIStateContext.Provider value={{ hamburgerActive, setHamburgerActive }}>
      {children}
    </UIStateContext.Provider>
  );
};

export const useUIState = () => useContext(UIStateContext);

export {};
