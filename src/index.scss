@import "./assets/fonts/customFonts";
@import "./assets/custom-global-styles/customGlobablStyles";


body {
  margin: 0;
  font-family: $primary-font;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  text-rendering: auto !important;
}

input, button{
  font-family: $secondary-font !important;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}


/* For WebKit browsers (Chrome, Safari) */
::-webkit-scrollbar {
  width: 0.4rem; /* Set the width of the scrollbar */
}

::-webkit-scrollbar-thumb {
  background-color: #a0a0a09a; /* Set the color of the thumb */
  border-radius: 4px; 
}

::-webkit-scrollbar-track {
  background-color: #f0f0f0; /* Set the color of the track */
}
