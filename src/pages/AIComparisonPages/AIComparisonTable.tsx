import { useState, useRef, useEffect } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import { useQuery, useMutation } from '@tanstack/react-query';
import { getAIComparisonPagesQuery, getAIComparisonPageDataQuery } from "../../utils/api";
import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import AbunTable from "../../components/AbunTable/AbunTable";
import Icon from "../../components/Icon/Icon";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { withAdminAndProductionCheck } from "../../utils/adminAndProductionCheck";
import { pageURL } from "../routes";
import './ComparisonPageTable.min.css'; 
import AbunLoader from "../../components/AbunLoader/AbunLoader";

interface AIComparisonPage {
    id: number;
    comp_id: string;
    comparison_type: string;
    comparison_topic: string;
    is_verified: boolean;
    version_count: number;
    created_on: string;
    created_on_relative: string;
    original_keyword?: string;
    original_ideas?: string[];
    selected_idea_index?: number;
}

function AIComparisonTable() {
    // --------------------------- CONSTANTS ---------------------------
    const pageSizes = [5, 10, 15, 30, 50, 100, 500];

    // --------------------------- STATES ---------------------------
    const [comparisonPages, setComparisonPages] = useState<AIComparisonPage[]>([]);
    const [loadingRowId, setLoadingRowId] = useState<string | null>(null);
    const [selectedCompId, setSelectedCompId] = useState<string | null>(null);

    // --------------------------- REFS ---------------------------
    const errorAlertRef = useRef<any>(null);
    const successAlertRef = useRef<any>(null);

    // --------------------------- HOOKS ---------------------------
    const navigate = useNavigate();
    const { compId, taskId } = useParams<{ compId?: string; taskId?: string }>();
    const location = useLocation();
    const [currentCompId, setCurrentCompId] = useState<string | null>(null);

    // --------------------------- QUERIES ---------------------------
    
    // Query for getting all comparison pages
    const {
        isLoading: isLoadingPages,
        error: pagesError,
        data: pagesData
    } = useQuery({
        ...getAIComparisonPagesQuery()
    });

    // Query for getting specific comparison page data (only runs when selectedCompId is set)
    const {
        isLoading: isLoadingComparisonData,
        error: comparisonDataError,
        data: comparisonData,
        refetch: refetchComparisonData
    } = useQuery({
        ...getAIComparisonPageDataQuery(selectedCompId || ''),
        enabled: !!selectedCompId, // Only run when we have a compId
    });

    // Handle pages data processing
    useEffect(() => {
        if (pagesData) {
            let pages: AIComparisonPage[] = [];
            
            if ((pagesData as any)?.status === 'success' && (pagesData as any)?.data?.pages) {
                pages = (pagesData as any).data.pages;
            } else if ((pagesData as any)?.data?.status === 'success' && (pagesData as any)?.data?.data?.pages) {
                pages = (pagesData as any).data.data.pages;
            } else if (Array.isArray((pagesData as any)?.data)) {
                pages = (pagesData as any).data;
            } else if (Array.isArray((pagesData as any)?.pages)) {
                pages = (pagesData as any).pages;
            }
            
            setComparisonPages(pages);
        }
    }, [pagesData]);

    // Handle pages error
    useEffect(() => {
        if (pagesError) {
            console.error('Error loading comparison pages:', pagesError);
            errorAlertRef.current?.show("Failed to load comparison pages");
        }
    }, [pagesError]);

    // Handle comparison data success
    useEffect(() => {
        if (comparisonData && selectedCompId) {
            setLoadingRowId(null);
            
            if ((comparisonData as any)?.data?.status === 'success' && (comparisonData as any)?.data?.comparison_data) {
                const comparisonDataObj = (comparisonData as any).data.comparison_data;
                successAlertRef.current?.show(`Loaded comparison page: ${comparisonDataObj.comparison_topic || comparisonDataObj.comparison_type}`);
                
                const navigationUrl = (taskId && taskId !== 'undefined') 
                    ? `${pageURL['AIComparisonGenerator']}/${comparisonDataObj.comp_id}/${taskId}`
                    : `${pageURL['AIComparisonGenerator']}/${comparisonDataObj.comp_id}`;
                
                navigate(navigationUrl, {
                    state: {
                        compId: comparisonDataObj.comp_id,
                        comparisonType: comparisonDataObj.comparison_type,
                        comparisonTopic: comparisonDataObj.comparison_topic,
                        comparisonData: comparisonDataObj,
                        fromExisting: true,
                        navigationTimestamp: Date.now()
                    },
                    replace: false
                });
                
                // Reset the selected comp ID after navigation
                setSelectedCompId(null);
            } else {
                errorAlertRef.current?.show("Invalid response format");
                setSelectedCompId(null);
            }
        }
    }, [comparisonData, selectedCompId, navigate, taskId]);

    // Handle comparison data error
    useEffect(() => {
        if (comparisonDataError && selectedCompId) {
            setLoadingRowId(null);
            setSelectedCompId(null);
            
            console.error('API Error:', comparisonDataError);
            let errorMessage = "Failed to load comparison page data";
            
            if ((comparisonDataError as any).response?.data?.message) {
                errorMessage = (comparisonDataError as any).response.data.message;
            }
            
            errorAlertRef.current?.show(errorMessage);
        }
    }, [comparisonDataError, selectedCompId]);

    // Handle URL params on component mount
    useEffect(() => {
        if (compId) {
            setCurrentCompId(compId);
            if (!location.state?.comparisonData) {
                fetchComparisonPageData(compId);
            }
        } else if (location.state?.compId) {
            setCurrentCompId(location.state.compId);
        }
    }, [compId, location.state]);

    // --------------------------- HANDLERS ---------------------------
    const handleCreateNewComparisonPage = () => {
        navigate(pageURL['AIComparisonGenerator']);
    };

    const handleViewComparisonPage = (comparisonPage: AIComparisonPage) => {
        if (!comparisonPage.comp_id) {
            errorAlertRef.current?.show("Invalid comparison page data");
            return;
        }
        
        setLoadingRowId(comparisonPage.comp_id);
        fetchComparisonPageData(comparisonPage.comp_id.trim());
    };

    const fetchComparisonPageData = (id: string) => {
        setSelectedCompId(id);
        // The query will automatically run due to the enabled condition
    };

    // --------------------------- UTILITY FUNCTIONS ---------------------------
    const formatRelativeTime = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMs = now.getTime() - date.getTime();
        const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
        const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
        const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

        if (diffInMinutes < 5) {
            return "few minutes ago";
        }
        
        if (diffInMinutes < 60) {
            const roundedMinutes = Math.floor(diffInMinutes / 10) * 10;
            return `${roundedMinutes} mins ago`;
        }
        
        if (diffInHours === 1) {
            return "an hour ago";
        }
        
        if (diffInHours < 24) {
            return `${diffInHours} hours ago`;
        }
        
        if (diffInDays < 30) {
            return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;
        }
        
        const day = date.getDate();
        const monthNames = [
            "Jan", "Feb", "Mar", "Apr", "May", "Jun",
            "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
        ];
        const month = monthNames[date.getMonth()];
        const year = date.getFullYear().toString().slice(-2);
        
        return `${day}, ${month} ${year}`;
    };

    const truncateText = (text: string, maxLength: number = 80) => {
        if (text.length <= maxLength) {
            return text;
        }
        
        const truncated = text.substring(0, maxLength - 3);
        return truncated + '...';
    };

    // --------------------------- TABLE CONFIG ---------------------------
    const columnHelper = createColumnHelper<AIComparisonPage>();

    const columnDefs: ColumnDef<AIComparisonPage, any>[] = [
        columnHelper.accessor('comparison_type', {
            header: "Comparison Page Title",
            cell: (info) => {
                const fullText = info.getValue();
                const row = info.row.original;
                const isRowLoading = loadingRowId === row.comp_id;
                const displayText = truncateText(fullText, 80);
                
                return (
                    <div 
                        className={`comp-title-cell ${isRowLoading ? 'loading' : ''}`}
                        style={{
                            maxWidth: '600px',
                            display: 'flex',
                            alignItems: 'center',
                            opacity: isRowLoading ? 0.6 : 1,
                            pointerEvents: 'none',
                            width: '100%',
                            height: '100%'
                        }}
                        title={fullText}
                    >
                        {isRowLoading && (
                            <Icon iconName="spinner" marginClass="mr-2" />
                        )}
                        <span style={{ pointerEvents: 'none' }}>{displayText}</span>
                    </div>
                );
            }
        }),
        columnHelper.accessor('created_on', {
            header: "Created On",
            cell: (info) => {
                const row = info.row.original;
                const isRowLoading = loadingRowId === row.comp_id;
                
                return (
                    <div style={{ 
                        opacity: isRowLoading ? 0.6 : 1,
                        pointerEvents: 'none',
                        width: '100%',
                        height: '100%'
                    }}>
                        {formatRelativeTime(info.getValue())}
                    </div>
                );
            }
        })
    ];

    return (
        <div className="ai-comp-table-tp-container">
            <div className="seo-project-header"></div>
            <h1>Create AI Comparison Page</h1>
            
            <div className="menu-btns AI-keyword-research-btn" onClick={handleCreateNewComparisonPage}>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                    <path d="m9 9 5 12 1.774-5.226L21 14 9 9z"></path>
                </svg>
                <span className="menu-btn-text">Create New AI Comparison Page</span>
            </div>
            
            <hr className="horizontal-rule" />
            
            <div className="seo-project-abun-table">
                <h1>Your AI Comparison Pages</h1>
                
                <div className="table-container">
                    {isLoadingPages ? (
                        <AbunLoader show={isLoadingPages} height="400px" />
                    ) : (
                        <AbunTable
                            tableContentName="AI Comparison Pages"
                            tableData={comparisonPages}
                            columnDefs={columnDefs}
                            pageSizes={pageSizes}
                            initialPageSize={pageSizes[1]}
                            noDataText="No Comparison Pages Found. Create your first comparison page to get started!"
                            searchboxPlaceholderText="Search comparison pages..."
                            handleRowClick={(row) => handleViewComparisonPage(row)}
                        />
                    )}
                </div>
            </div>
            
            <ErrorAlert ref={errorAlertRef} />
            <SuccessAlert ref={successAlertRef} />
        </div>
    );
}

export default withAdminAndProductionCheck(AIComparisonTable);