@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";
@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/elements/container";
@import "bulma/sass/form/_all";
@import "bulma/sass/components/tabs";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";
@import "../../assets/bulma-overrides/bulmaOverrides";

.ai-comparison-page-container {
  font-family: $primary-font !important;
  background-color: #ffffff;
  
  &.full-comparison-view {
    margin: 0;
    padding: 0;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh; 
    overflow: hidden;
    z-index: 99;
    background-color: #fff;
  }

  .ai-comparison-page-header {
    position: relative;
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &.comparison-view {
      position: sticky;
      top: 0;
      z-index: 99;
      background-color: #fff;
      border-bottom: none;
      box-shadow: none;
      height: 6rem;
      padding: 2rem 1.5rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .left-header-section {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: nowrap;
      margin-left: 0;
      flex: 1;
      min-width: 0;

      .back-btn {
        cursor: pointer;
        margin-right: 1rem;
        flex-shrink: 0;
        
        svg {
          width: 30px;
          height: 24px;
        }
      }

      .abun-logo {
        margin: 0 1.25rem;
        width: 52px;
        height: 48px;
        flex-shrink: 0;
      }

      .Tabs {
        margin: 0 0 0 2.75rem;
        flex-shrink: 0;

        .Tab {
          &.active {
            font-size: 1.25rem;
            font-weight: 600;
            color: #3F77F8;
            border-bottom: 3px solid #3F77F8;
            opacity: 1;
            white-space: nowrap;
          }
        }
      }
    }

    .right-header-section {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
      flex-wrap: nowrap;
      gap: 0.75rem;
      flex-shrink: 0;
      min-width: fit-content;

      .editor-controls {
        display: flex;
        gap: 0.75rem;
        align-items: center;
        white-space: nowrap;

        .edit-mode-toggle {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          white-space: nowrap;
          flex-shrink: 0;

          .edit-mode-label {
            margin-right: 75px;
            font-size: 0.875rem;
            font-weight: 500;
            color: #333;
          }

          .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background-color: #e2e8f0;
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            border: 1px solid #cbd5e1;

            &.active {
              background-color: #3F77F8;
              border-color: #3F77F8;
            }

            .toggle-slider {
              position: absolute;
              top: 2px;
              left: 2px;
              width: 18px;
              height: 18px;
              background-color: white;
              border-radius: 50%;
              transition: transform 0.3s ease;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            &.active .toggle-slider {
              transform: translateX(20px);
            }

            &:hover:not(.active) {
              background-color: #f1f5f9;
            }
          }
        }

        .sidebar-button.save-btn {
          padding: 0.625rem 1.25rem;
          border: 1px solid #3F77F8;
          border-radius: 0.25rem;
          background-color: #3F77F8;
          color: #fff;
          cursor: pointer;
          font-size: 0.875rem;
          font-weight: 500;
          transition: all 0.3s ease;
          white-space: nowrap;
          flex-shrink: 0;

          &:hover:not(:disabled) {
            background-color: #2563eb;
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            background-color: #6c757d;
          }
        }

        svg.collapse-button {
          cursor: pointer;
          flex-shrink: 0;
          width: 20px;
          height: 20px;

          &:hover path {
            fill: #3F77F8 !important;
            fill-opacity: 0.8;
          }

          &.collapsed {
            background: linear-gradient(to left, #000 29%, white 25%) !important;
          }
        }
      }
    }

    @media (max-width: 1024px) {
      .right-header-section {
        gap: 0.5rem;

        .editor-controls {
          gap: 0.5rem;

          .edit-mode-toggle {
            gap: 0.375rem;

            .edit-mode-label {
              font-size: 0.8rem;
            }

            .toggle-switch {
              width: 40px;
              height: 22px;

              .toggle-slider {
                width: 16px;
                height: 16px;
              }

              &.active .toggle-slider {
                transform: translateX(18px);
              }
            }
          }

          .sidebar-button.save-btn {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
          }
        }
      }
    }

    @media (max-width: 768px) {
      .right-header-section {
        gap: 0.375rem;

        .editor-controls {
          gap: 0.375rem;

          .edit-mode-toggle {
            gap: 0.25rem;

            .edit-mode-label {
              font-size: 0.75rem;
            }

            .toggle-switch {
              width: 36px;
              height: 20px;

              .toggle-slider {
                width: 14px;
                height: 14px;
              }

              &.active .toggle-slider {
                transform: translateX(16px);
              }
            }
          }

          .sidebar-button.save-btn {
            padding: 0.5rem 0.75rem;
            font-size: 0.75rem;
          }

          svg.collapse-button {
            width: 18px;
            height: 18px;
          }
        }
      }

      &.comparison-view {
        padding: 1rem 0.75rem;
        height: 5rem;
      }

      .left-header-section {
        .back-btn {
          margin-right: 0.5rem;
          
          svg {
            width: 24px;
            height: 20px;
          }
        }

        .abun-logo {
          margin: 0 0.75rem;
          width: 40px;
          height: 36px;
        }

        .Tabs {
          margin: 0 0 0 1rem;
          
          .Tab {
            &.active {
              font-size: 1rem;
              font-weight: 600;
            }
          }
        }
      }

      .right-header-section {
        .editor-controls {
          .edit-mode-toggle {
            .edit-mode-label {
              margin-right: 0.5rem !important;
              font-size: 0.8rem;
            }
          }
        }
      }
    }

    @media (max-width: 480px) {
      .right-header-section {
        .editor-controls {
          .edit-mode-toggle {
            .edit-mode-label {
              font-size: 0.7rem;
            }

            .toggle-switch {
              width: 32px;
              height: 18px;

              .toggle-slider {
                width: 12px;
                height: 12px;
              }

              &.active .toggle-slider {
                transform: translateX(14px);
              }
            }
          }

          .sidebar-button.save-btn {
            padding: 0.4rem 0.6rem;
            font-size: 0.7rem;
          }

          svg.collapse-button {
            width: 16px;
            height: 16px;
          }
        }
      }

      &.comparison-view {
        padding: 0.75rem 0.5rem;
        height: 4rem;
      }

      .left-header-section {
        .back-btn {
          margin-right: 0.25rem;
          
          svg {
            width: 20px;
            height: 18px;
          }
        }

        .abun-logo {
          margin: 0 0.5rem;
          width: 32px;
          height: 28px;
        }

        .Tabs {
          margin: 0 0 0 0.5rem;
          
          .Tab {
            &.active {
              font-size: 0.9rem;
              font-weight: 600;
            }
          }
        }
      }

      .right-header-section {
        .editor-controls {
          .edit-mode-toggle {
            .edit-mode-label {
              margin-right: 0.25rem !important;
              font-size: 0.75rem;
            }
          }
        }
      }
    }

    @media (max-width: 360px) {
      &.comparison-view {
        padding: 0.5rem 0.25rem;
        height: 3.5rem;
      }

      .left-header-section {
        .back-btn {
          margin-right: 0.125rem;
          
          svg {
            width: 18px;
            height: 16px;
          }
        }

        .abun-logo {
          margin: 0 0.25rem;
          width: 28px;
          height: 24px;
        }

        .Tabs {
          margin: 0 0 0 0.25rem;
          
          .Tab {
            &.active {
              font-size: 0.8rem;
              font-weight: 600;
            }
          }
        }
      }

      .right-header-section {
        .editor-controls {
          .edit-mode-toggle {
            .edit-mode-label {
              display: none;
            }
          }
        }
      }
    }
  }

  .ai-comparison-page-content {
    &.comparison-view {
      padding: 0;
      height: calc(100vh - 6rem); 
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
  }

  .comparison-result-section-new {
    display: flex;
    gap: 0;
    height: 100%; 
    flex: 1;
    overflow: hidden;

    .comparison-preview-main {
      flex: 1;
      min-width: 0;
      background-color: #fff;
      border-radius: 0;
      border: none;
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .ai-preview-container {
        padding: 16px;
        height: 100%;
        overflow: hidden;
        flex: 1;
        display: flex;
        flex-direction: column;

        .ai-preview-content {
          width: 100%;
          min-height: 100%;
          height: auto; 
          
          &:focus {
            outline: none !important;
            border: none !important;
          }
          
          &:focus-visible {
            outline: none !important;
            border: none !important;
          }
          
          &:focus {
            box-shadow: none !important;
          }
        }
      }
    }

    .iframe-container {
      border: 1px solid #d1d5db;
      border-radius: 8px;
      background-color: white;
      width: 100%;
      height: 100%; 
      display: flex;
      flex-direction: column;
      position: relative;
      overflow: hidden;
      flex: 1; 
    }

    .comparison-iframe {
      border-radius: 8px;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      overflow: auto;
      background-color: #fff;
      min-height: 100%;
      height: auto;
    }

    .comparison-iframe,
    iframe {
      width: 100%;
      height: 100%; 
      border: none;
      background-color: white;
      display: block;
      flex: 1;
    }

    .comparison-sidebar {
      width: 380px;
      flex-shrink: 0;
      background-color: #fff;
      border-radius: 0;
      border: none;
      border-left: 1px solid #e5e7eb;
      height: 100%;
      overflow-y: auto;
      margin-top: 0;

      .sidebar-section {
        border-bottom: 1px solid #e5e7eb;

        .sidebar-dropdown-header {
          padding: 16px 20px 0px 20px;
          cursor: pointer;
          background-color: #fff;
          font-weight: 400;
          font-size: 15px;
          color: #4b5563;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          transition: all 0.2s ease;
          border: none;
          margin-bottom: 16px; 

          h6 {
            font-size: 1.125rem;
            font-weight: 400;
            color: #3F77F8;
            width: 100%;
            font-family: Inter;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-start;
            cursor: pointer;
          }

          &::before {
            content: "";
            width: 12px;
            height: 7px;
            background-image: url("data:image/svg+xml,%3Csvg width='12' height='7' viewBox='0 0 12 7' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 5.25L6.5 0L13 5.25L12 6.5L6.5 2.5L1 6.5L0 5.25Z' fill='%236B7280' /%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
            transition: transform 0.2s ease;
            flex-shrink: 0;
          }

          &.active {
            margin-bottom: 0; 
            
            &::before {
              transform: rotate(180deg);
            }
          }

          &:hover {
            background-color: #f9fafb;
          }

          &.version-header {
            justify-content: flex-start;
            gap: 12px;

            .version-count {
              background-color: #3b82f6;
              color: #fff;
              padding: 3px 10px;
              border-radius: 14px;
              font-size: 13px;
              min-width: 22px;
              text-align: center;
              font-weight: 500;
              margin-left: auto;
            }
          }

          span {
            font-size: 15px;
            color: #4b5563;
            font-weight: 400;
            line-height: 1.4;
          }
        }

        .sidebar-dropdown-content {
          display: none;
          padding: 20px 24px;
          background-color: #fff;

          .sidebar-textarea {
            width: 100%;
            min-height: 100px;
            padding: 14px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            resize: vertical;
            margin-bottom: 20px;
            font-size: 14px;
            font-family: inherit;
            box-sizing: border-box;
            color: #374151;
            line-height: 1.5;

            &:focus {
              border-color: #3F77F8;
            }

            &.embed-code {
              background-color: #f9fafb;
              font-family: 'Courier New', monospace;
              font-size: 13px;
              min-height: 80px;
              margin-bottom: 5px;
            }

            &::placeholder {
              color: #9ca3af;
            }
          }

          .sidebar-button {
            width: 100%;
            padding: 12px 18px;
            color: #fff;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;

            &.update-btn {         
              background-color: #3b82f6;          

              &:hover:not(:disabled) {             
                background-color: #2563eb;         
              }          

              &:disabled {             
                opacity: 0.6;             
                cursor: not-allowed;         
              }     
            }      

            .button-content {
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 8px;
              width: 100%;
            }

            .spinner {         
              width: 12px;         
              height: 12px;         
              border: 2px solid #ffffff;         
              border-top: 2px solid transparent;         
              border-radius: 50%;         
              animation: spin 1s linear infinite;
              flex-shrink: 0; 
            }      

            @keyframes spin {         
              0% { transform: rotate(0deg); }         
              100% { transform: rotate(360deg); }     
            } 

            &.copy-btn {
              background-color: #007bff;
              width: auto;
              padding: 6px 12px; 
              font-size: 12px; 
              margin-left: auto;
              margin-top:0px; 
              margin-bottom:0px; 
              display: block; 

              &:hover {
                background-color: #2a7ad0;
              }
            }

            &.small {
              width: auto;
              padding: 6px 10px;
              font-size: 9px;

              &.switch-btn {
                background-color: #3b82f6;

                &:hover {
                  background-color: #2563eb;
                }
              }

              &.danger {
                background-color: #ef4444;

                &:hover {
                  background-color: #dc2626;
                }
              }
            }
          }

          .versions-list-sidebar {
            max-height: 400px;
            overflow-y: auto;

            .version-item-sidebar {
              padding: 16px;
              border: 1px solid #e5e7eb;
              border-radius: 8px;
              margin-bottom: 12px;
              background-color: #fff;
              transition: all 0.2s ease;

              &:hover {
                border-color: #d1d5db;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
              }

              &.current-version {
                background-color: #eff6ff;
                border-color: #3b82f6;
              }

              .version-header-sidebar {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                width: 100%;

                .version-info-sidebar {
                  flex: 1;
                  min-width: 0;
                }

                .version-actions-sidebar {
                  display: flex;
                  gap: 8px;
                  flex-shrink: 0;
                  margin-left: auto;
                  align-items: flex-start;
                  min-width: fit-content;
                  justify-content: flex-end;
                  width: auto; 
                }
              }

              .version-description-sidebar {
                font-size: 13px;
                color: #6b7280;
                line-height: 1.5;
              }
            }

            .empty-versions {
              text-align: center;
              color: #6b7280;
              font-style: italic;
              font-size: 14px;
              margin: 0;
              padding: 24px;
            }
          }

          .embed-description {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 20px;
            margin: 0 0 20px 0;
            line-height: 1.5;
          }
        }

        &:has(.sidebar-dropdown-header.active) .sidebar-dropdown-content {
          display: block;
        }
      }
    }
  }
      
  .sidebar-button {
    &.save-btn {
      padding: 0.75rem 1.5rem;
      border: 1px solid #3F77F8;
      border-radius: 0.25rem;
      background-color: #3F77F8;
      color: #fff;
      cursor: pointer;
      font-size: 0.9rem;
      font-weight: 500;
      transition: all 0.3s ease;
      white-space: nowrap;

      &:hover:not(:disabled) {
        background-color: #2563eb;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background-color: #6c757d;
      }
    }
  }

  svg.collapse-button {
    cursor: pointer;
    margin-right: 5px;

    &:hover path {
      fill: #3F77F8 !important;
      fill-opacity: 0.8;
    }

    &.collapsed {
      background: linear-gradient(to left, #000 29%, white 25%) !important;
    }
  }

  .comparison-sidebar{
    transition: all 0.3s ease;

    &.collapsed {
      width: 0;
      overflow: hidden;
      border-left: none;
    }
  }
  
  @media (max-width: 768px) {
    .right-header-section {
      flex-wrap: nowrap;
      gap: 0.5rem;
      
      .editor-controls {
        gap: 0.5rem;
        
        .save-btn {
          padding: 0.5rem 1rem;
          font-size: 0.85rem;
          white-space: nowrap;
        }
      }
    }
    
    .comparison-sidebar {
      &:not(.collapsed) {
        position: fixed;
        top: 60px; 
        left: 0;
        width: 100vw;
        height: calc(100vh - 60px);
        z-index: 1001;
        background-color: white;
      }
    }
  }

  .modal, .modal-background {
    z-index: 1050 !important;
  }

  .modal-card, .modal-content {
    z-index: 1051 !important;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes bounce {
    0%, 80%, 100% {
      transform: scale(0);
    }
    40% {
      transform: scale(1);
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #f8f9fa;
    padding: 40px;
    flex: 1;
  }

  .container{
    display:flex;
    margin-left: 700px;
  }

  .button{
    font-family: "Inter", Sans-serif;
    font-size: 14px;
    display: flex;
    border-style: solid;
    border-style: solid;
    border-width: 1px 1px 1px 1px;
    border-color: #D2D2EB;
    border-radius: 200px;
    padding: 15px;
    transition: all .09s ease !important;
    box-shadow: 0px 3px 3px 0px rgba(19.000000000000007, 48.00000000000005, 66, 0.07058823529411765);
  }

  .button:hover{
    transform: scale(1.02);
    cursor: auto !important;
    box-shadow: -2px 5px 3px 0px rgba(19.000000000000007, 48.00000000000005, 66, 0.07058823529411765);
  }

  .button:hover span{
    color:#2942ff!important;  
  }

  @media screen and (min-resolution: 1.5dppx) {
  .ai-comparison-page-container.full-comparison-view {
    height: 100vh;
  }
  
  .ai-comparison-page-content.comparison-view {
    height: calc(100vh - 6rem);
  }
}

@media screen and (min-resolution: 2dppx) {
  .ai-comparison-page-container.full-comparison-view {
    height: 100vh;
  }
  
  .ai-comparison-page-content.comparison-view {
    height: calc(100vh - 6rem);
  }
}

  @media (max-width: 768px) {
  .ai-comparison-page-content.comparison-view {
    height: calc(100vh - 5rem); 
  }
}

@media (max-width: 480px) {
  .ai-comparison-page-content.comparison-view {
    height: calc(100vh - 4rem);
  }
}
}


.ai-comparison-generator-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: $primary-font !important;
  overflow: visible;
  width: 100%;

  .ai-comparison-generator-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;

    .ai-comparison-generator-title {
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 8px;
      text-align: center;
      font-family: $primary-font;
    }

    .ai-comparison-generator-description {
      text-align: center;
      color: rgba(0,0,0,.698);
      font-family: $secondary-font !important;
      font-size: 1.125rem!important;
    }

    .ai-comparison-input-card {
      width: 100%;
      max-width: 800px;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 40px;
      gap: 30px;

      .ai-comparison-urls-section {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 25px;

        .ai-comparison-input-group {
          width: 100%;
          display: flex;
          flex-direction: column;
          gap: 8px;

          label {
            font-weight: 600;
            font-size: 1.1rem;
            color: #333333;
            font-family: $primary-font;
          }

          .ai-comparison-url-input {
            width: 100%;
            padding: 16px 20px;
            font-size: 1.1rem;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            background-color: #fff;
            transition: all 0.2s ease;
            font-family: $secondary-font;
            min-height: 56px;

            &:focus {
              outline: none;
              border-color: $primary;
              box-shadow: 0 0 0 3px rgba($primary, 0.1);
            }

            &::placeholder {
              color: #999;
            }

            &:disabled {
              background-color: #f5f5f5;
              cursor: not-allowed;
            }
          }
        }
      }

      .ai-comparison-instructions-section {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 12px;

        .ai-comparison-instructions-label {
          font-weight: 600;
          font-size: 1.1rem;
          color: #333333;
          font-family: $primary-font;
        }

        .ai-comparison-textarea {
          width: 100%;
          min-height: 140px;
          padding: 20px;
          font-size: 1.1rem;
          border: 2px solid #e0e0e0;
          border-radius: 12px;
          background-color: #fff;
          resize: vertical;
          font-family: $secondary-font;
          line-height: 1.5;
          transition: all 0.2s ease;

          &:focus {
            outline: none;
            border-color: $primary;
            box-shadow: 0 0 0 3px rgba($primary, 0.1);
          }

          &::placeholder {
            color: #999;
            line-height: 1.4;
          }

          &:disabled {
            background-color: #f5f5f5;
            cursor: not-allowed;
          }
        }
      }

      .ai-generate-comparison-button {
        background-color: $primary;
        color: white;
        border: none;
        border-radius: 12px;
        padding: 16px 40px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;
        min-height: 56px;
        font-family: $primary-font;

        &:hover:not(:disabled) {
          background-color: darken($primary, 5%);
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba($primary, 0.25);
        }

        &:active:not(:disabled) {
          transform: translateY(0);
        }

        &:disabled {
          background-color: lighten($primary, 20%);
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }
      }
    }

    .horizontal-line {
      width: 100%;
      height: 1px;
      margin-bottom: 0;
      background-color: #dedbdb;
    }
  }

  @media (max-width: 768px) {
  .ai-comparison-generator-container {
    .ai-comparison-generator-content {
      .ai-comparison-generator-title {
        font-size: 1.75rem;
      }

      .ai-comparison-generator-description {
        font-size: 1rem !important;
      }

      .ai-comparison-input-card {
        max-width: 100%;
        margin-top: 30px;
        gap: 25px;

        .ai-comparison-urls-section {
          gap: 20px;
        }

        .ai-comparison-instructions-section {
          .ai-comparison-textarea {
            padding: 16px;
            font-size: 1rem;
            min-height: 120px;
          }
        }

        .ai-generate-comparison-button {
          padding: 14px 32px;
          font-size: 1rem;
          width: 100%;
          max-width: 300px;
        }
      }
    }
  }
  }

  @media (max-width: 480px) {
    .ai-comparison-generator-container {
      .ai-comparison-generator-content {
        .ai-comparison-generator-title {
          font-size: 1.5rem;
        }

        .ai-comparison-input-card {
          gap: 20px;

          .ai-comparison-urls-section {
            gap: 15px;
          }
        }
      }
    }
  }
}