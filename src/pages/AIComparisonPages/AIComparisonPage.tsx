import React, { useState, useEffect, useRef, useCallback } from 'react';
import {useLocation, useParams, useNavigate, useRouteLoaderData } from 'react-router-dom';
import { useQuery, useMutation } from '@tanstack/react-query';
import { generateAIComparisonPageMutation,
        getTaskProgress,
        modifyAIComparisonPageMutation, 
        updatedComparisonPageMutation,
        deleteComparisonPageVersionMutation,
        setCurrentComparisonVersionFn,
        getAIComparisonPagesQuery,
        getAIComparisonPageDataQuery,
        makeApiRequest,
       } from "../../utils/api";
import './AIComparisonPage.min.css';
import { BasePageData } from "../../pages/Base/Base";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { Player } from "@lottiefiles/react-lottie-player";
import AbunModal from "../../components/AbunModal/AbunModal";

interface ComparisonDataType {
    comp_id: string;
    html_content: string;
    url1: string;
    url2: string;
    current_version_id?: number;
}

interface LocationState { 
    compId?: string;
    comparisonType?: string;
    url1?: string;
    url2?: string;
    fromOtherComparison?: boolean;
    fromExisting?: boolean;
    fromCustomUrls?: boolean;
    userModifications?: string[];
}

interface ComparisonPageListApiResponse {
  status: "success" | "error";
  data: {  
    pages: any[];
    comparison_pages_generated: number;
    max_comparison_pages_allowed: number;
  };
  message?: string;
}

interface ComparisonPageApiResponse {
    data: {
        comparison_data: {
            comp_id: string;
            html_content: string;
            url1: string;
            url2: string;
            versions: ComparisonVersion[];
            current_version_id?: number;
        };
    };
}

interface ComparisonVersion {
    id: number;
    version_name: string;
    html_code: string;
    changes_summary?: string;
    created_at: string;
    created_at_relative: string;
    code_length: number;
    code_preview: string;
}


declare global {
    interface Window {
        currentEditableContent?: string;
    }
}

function AIComparisonPage() {
    const basePageData = useRouteLoaderData("base") as BasePageData;  
    const { active_website_domain } = basePageData;
    const { currentPlanName } = basePageData;

    const { comp_id, taskId } = useParams<{ comp_id?: string; taskId?: string }>();
    const navigate = useNavigate();
    const location = useLocation();
    const state = location.state as LocationState | null;

    // Refs 
    const errorAlertRef = useRef<any>(null);
    const successAlertRef = useRef<any>(null);
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const isUpdatingRef = useRef(false);

    // Generator states
    const [url1, setUrl1] = useState<string>(active_website_domain || '');
    const [url2, setUrl2] = useState<string>('');
    const [scriptTag, setScriptTag] = useState('');
    const [divTag, setDivTag] = useState('');
    const [scriptGenerated, setScriptGenerated] = useState(false);
    const [htmlContent, setHtmlContent] = useState<string>('');
    const [compId, setCompId] = useState<string>('');
    const [comparisonType, setComparisonType] = useState<string>('');
    const [comparisonData, setComparisonData] = useState<ComparisonDataType | null>(null);
    const [isGenerating, setIsGenerating] = useState(false);
    const [showComparisonPage, setShowComparisonPage] = useState(false);
    const [versions, setVersions] = useState<ComparisonVersion[]>([]);
    const [currentVersionId, setCurrentVersionId] = useState<number | null>(null);
    const [currentTaskId, setCurrentTaskId] = useState<string | null>(taskId || null);
    const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
    const [versionToDelete, setVersionToDelete] = useState<number | null>(null);
    const [generationProgress, setGenerationProgress] = useState('Initializing comparison...');
    const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
    const [updateTabOpen, setUpdateTabOpen] = useState(true);
    const [versionsTabOpen, setVersionsTabOpen] = useState(false);
    const [embedTabOpen, setEmbedTabOpen] = useState(false);
    const [userInput, setUserInput] = useState('');
    const [isUpdating, setIsUpdating] = useState(false);
    const [isEditMode, setIsEditMode] = useState(false);

    // Mutations
    const generateComparisonMutation = useMutation(generateAIComparisonPageMutation);
    
    const modifyPageMutation = useMutation({
        ...modifyAIComparisonPageMutation
    });

    // Queries
    const {
        data: comparisonPageData,
        isLoading: isLoadingComparisonPage,
        error: comparisonPageError
    } = useQuery({
        ...getAIComparisonPageDataQuery(state?.compId || ''),
        enabled: !!state?.compId && state.compId.trim() !== '', 
    });

    const { 
        data: comparisonPageListData,
        isLoading: isLoadingComparisonList,
        error: comparisonListError
    } = useQuery<ComparisonPageListApiResponse>(getAIComparisonPagesQuery()); 

    const [comparisonPageLimits, setComparisonPageLimits] = useState({
        generated: 0,
        maxAllowed: 0
    });

    const isLoading = (state?.compId && isLoadingComparisonPage) || generateComparisonMutation.isLoading || modifyPageMutation.isLoading || isGenerating || isUpdating;

    const canGenerateMore = (comparisonPageListData?.data?.comparison_pages_generated || 0) < (comparisonPageListData?.data?.max_comparison_pages_allowed || 5);
const pollTaskProgress = (taskId: string, compId: string, isForGeneration: boolean = true) => {
    const interval = setInterval(() => {
        getTaskProgress(taskId)
            .then((res) => {
                const status = res.data.status;
                console.log('Task status:', status);
                
                if (status === "success") {
                    console.log("Task completed successfully");
                    clearInterval(interval);
                    
                    fetchComparisonData(compId, isForGeneration);
                    
                } else if (status === "failure") {
                    clearInterval(interval);
                    
                    const errorMessage = res.data.progress_info?.error_message || 
                                       res.data.progress_info?.message || 
                                       "Task failed. Please try again.";
                    
                    handleTaskFailure({ 
                        message: errorMessage,
                        error_code: res.data.progress_info?.error_code || "TASK_FAILED"
                    }, isForGeneration);
                    
                } else if (status === "processing") {
                    const progressInfo = res.data.progress_info;
                    if (progressInfo?.current && progressInfo?.total) {
                        setGenerationProgress(`Processing... ${progressInfo.current}/${progressInfo.total}`);
                    } else if (progressInfo?.status) {
                        setGenerationProgress(progressInfo.status);
                    }
                }
            })
            .catch((err) => {
                console.error("Error fetching task progress:", err);
                clearInterval(interval);
                
                const isNetworkError = !err.response;
                const errorMessage = isNetworkError 
                    ? "Network error - please check your connection"
                    : err.response?.data?.message || "Failed to fetch task progress";
                
                handleTaskFailure({ 
                    message: errorMessage,
                    error_code: isNetworkError ? "NETWORK_ERROR" : "API_ERROR"
                }, isForGeneration);
            });
    }, 2000);

    return interval;
};

const fetchComparisonData = async (compId: string, isForGeneration: boolean) => {
    try {
        const response = await makeApiRequest(`/api/frontend/get-comparison-page-data/?comp_id=${compId}`, 'get');
        const data = response.data;
        
        if (data.status === 'success') {
            if (isForGeneration) {
                handleGenerationSuccess(data);
            }
        } else {
            handleTaskFailure({
                message: data.message || "Failed to fetch comparison data",
                error_code: "DATA_FETCH_ERROR"
            }, isForGeneration);
        }
    } catch (error) {
        console.error("Error fetching comparison data:", error);
        handleTaskFailure({
            message: "Failed to fetch comparison data after task completion",
            error_code: "DATA_FETCH_ERROR"
        }, isForGeneration);
    }
};

const handleGenerate = useCallback(() => {
    if (!canGenerateMore) {
        errorAlertRef.current?.show(
            `You have reached your monthly limit of ${comparisonPageListData?.data?.max_comparison_pages_allowed || 5} comparison pages. Please upgrade your plan to generate more.`
        );
        return;
    }

    errorAlertRef.current?.close();
    successAlertRef.current?.close();
    
    if (!url1.trim() || !url2.trim()) {
        errorAlertRef.current?.show('Please enter both URLs');
        return;
    }

    const normalizeUrl = (url) => {
        url = url.trim();
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            return 'https://' + url;
        }
        return url;
    };

    const normalizedUrl1 = normalizeUrl(url1);
    const normalizedUrl2 = normalizeUrl(url2);

    setIsGenerating(true);
    setGenerationProgress('Starting generation...');

    generateComparisonMutation.mutate(
        {
            url1: normalizedUrl1,
            url2: normalizedUrl2,
        },
        {
            onSuccess: (response) => {
                const data = response.data;
                console.log('Generation response:', data);

                if (data && (data.status === 'success' || data.status === 'processing')) {
                    if (data.task_id && data.comp_id) {
                        setCurrentTaskId(data.task_id);
                        setCompId(data.comp_id);
                        
                        navigate(`/ai-comparison-generator/${data.comp_id}`, { replace: true });
                        
                        successAlertRef.current?.show("Comparison page generation started...");
                        setGenerationProgress('Processing your comparison...');
                        
                    } else {
                        console.error('Missing task_id or comp_id in response:', data);
                        setIsGenerating(false);
                        errorAlertRef.current?.show("Server response missing required data");
                    }
                } else {
                    console.error('Unexpected response format:', data);
                    setIsGenerating(false);
                    errorAlertRef.current?.show("Unexpected response from server");
                }
            },
            onError: (error) => {
                console.error('Generation failed:', error);
                
                setIsGenerating(false);
                setCurrentTaskId(null);
                setShowComparisonPage(false);
                
                navigate('/ai-comparison-generator', { replace: true });
                
                let errorMessage = "Failed to generate comparison page. Please try again.";
                if (error?.response?.data?.message) {
                    errorMessage = error.response.data.message;
                }
                errorAlertRef.current?.show(errorMessage);
            }
        }
    );
}, [canGenerateMore, comparisonPageListData, url1, url2, generateComparisonMutation, navigate, errorAlertRef, successAlertRef]);

const handleGenerationSuccess = (data: any) => {
    console.log('handleGenerationSuccess called with:', data);
    
    setIsGenerating(false);
    setCurrentTaskId(null);

    if (!data?.comp_id) {
        console.error('Missing comp_id in success data:', data);
        errorAlertRef.current?.show("Incomplete data received from server: missing comp_id");
        navigate('/ai-comparison-generator', { replace: true });
        return;
    }

    if (!data?.html_content) {
        console.error('Missing html_content in success data:', data);
        errorAlertRef.current?.show("Incomplete data received from server: missing html_content");
        navigate('/ai-comparison-generator', { replace: true });
        return;
    }
    
    setCompId(data.comp_id);
    setHtmlContent(data.html_content);
    setComparisonType(data.comparison_type || '');

    const comparisonDataObj: ComparisonDataType = {
        comp_id: data.comp_id,
        html_content: data.html_content,
        url1: data.url1 || url1,
        url2: data.url2 || url2
    };

    setComparisonData(comparisonDataObj);
    setShowComparisonPage(true);

    navigate(`/ai-comparison-generator/${data.comp_id}`, { replace: true });

    if (data.version_id) {
        const initialVersion: ComparisonVersion = {
            id: data.version_id,
            version_name: data.version_name || `${data.comparison_type || 'comparison'}-v1`,
            html_code: data.html_content,
            changes_summary: "Initial version created",
            created_at: new Date().toISOString(),
            created_at_relative: "Just now",
            code_length: data.html_content.length,
            code_preview: data.html_content.substring(0, 200)
        };

        setVersions([initialVersion]);
        setCurrentVersionId(data.version_id);
    }

    successAlertRef.current?.show("Comparison page generated successfully!");
};

const handleTaskFailure = (data: any, isForGeneration: boolean) => {
    console.error('Task failed with data:', data);
    
    if (isForGeneration) {
        setIsGenerating(false);
        navigate('/ai-comparison-generator', { replace: true });
    } else {
        setIsUpdating(false);
    }
    
    setCurrentTaskId(null);

    let errorMessage = "Task failed. Please try again.";
    
    if (data?.error_message) {
        errorMessage = data.error_message;
    } else if (data?.message) {
        errorMessage = data.message;
    } else if (data?.error_code) {
        switch (data.error_code) {
            case 'COMPARISON_PAGE_NOT_FOUND':
                errorMessage = "The comparison page record was not found. Please try generating again.";
                break;
            case 'USER_NOT_FOUND':
                errorMessage = "User session expired. Please refresh the page and try again.";
                break;
            case 'WEBSITE_NOT_FOUND':
                errorMessage = "Website configuration error. Please contact support.";
                break;
            case 'AI_PROCESSING_ERROR':
                errorMessage = "AI processing failed. Please try again with different URLs.";
                break;
            case 'NETWORK_ERROR':
                errorMessage = "Network connection issue. Please check your internet and try again.";
                break;
            default:
                errorMessage = `Error: ${data.error_code}`;
        }
    }
    
    console.error('Showing error:', errorMessage);
    errorAlertRef.current?.show(errorMessage);
};

useEffect(() => {
    if (currentTaskId && compId) {
        console.log('Starting polling for task:', currentTaskId, 'comp_id:', compId);
        const interval = pollTaskProgress(currentTaskId, compId, true);
        
        return () => {
            if (interval) {
                clearInterval(interval);
            }
        };
    }
}, [currentTaskId, compId]);

const handleUpdateComparisonPage = () => {
    errorAlertRef.current?.close();
    successAlertRef.current?.close();

    if (!userInput.trim() || !compId) {
        errorAlertRef.current?.show("Please enter modifications and ensure comparison page exists");
        return;
    }

    setIsUpdating(true);

    let currentHtmlContent = htmlContent;
    
    if (iframeRef.current) {
        const iframe = iframeRef.current;
        const doc = iframe.contentDocument || iframe.contentWindow?.document;
        if (doc) {
            currentHtmlContent = doc.documentElement.outerHTML;
            setHtmlContent(currentHtmlContent);
        }
    }

    modifyPageMutation.mutate(
        { 
            comp_id: compId,
            modifications: userInput.trim(),
            html_content: currentHtmlContent
        },
        {
            onSuccess: (response) => {
                const data = response.data;
                
                if (data && data.status === 'processing' && data.task_id) {
                    pollTaskCompletion(data.task_id);
                } else {
                    setIsUpdating(false);
                    errorAlertRef.current?.show("Failed to start modification task");
                }
            },
            onError: (error: any) => {
                console.error('Update failed:', error);
                setIsUpdating(false);
                
                let errorMessage = "Failed to update comparison page.";
                
                if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
                    errorMessage = "Request timed out. The server might still be processing. Please wait a moment and check if the update was applied.";
                } else if (error.message?.includes('broken pipe')) {
                    errorMessage = "Connection was interrupted. Please try again.";
                } else if (error.response?.status === 404) {
                    errorMessage = "API endpoint not found. Make sure DEBUG mode is enabled.";
                } else if (error.response?.data?.err_id === 'NO_COMPARISON_PAGE_FOUND') {
                    errorMessage = "Comparison page not found. Please generate a new one.";
                } else if (error.response?.data?.message) {
                    errorMessage = error.response.data.message;
                }
                
                errorAlertRef.current?.show(errorMessage);
            }
        }
    );
};

const handleSaveDirectHtmlChanges = () => {
    errorAlertRef.current?.close();
    successAlertRef.current?.close();

    if (!compId) {
        errorAlertRef.current?.show("Comparison page not found");
        return;
    }

    let currentHtmlContent = htmlContent;
    
    if (iframeRef.current) {
        const iframe = iframeRef.current;
        const doc = iframe.contentDocument || iframe.contentWindow?.document;
        if (doc) {
            currentHtmlContent = doc.documentElement.outerHTML;
        }
    }

    if (!currentHtmlContent.trim()) {
        errorAlertRef.current?.show("No content to save");
        return;
    }

    modifyPageMutation.mutate(
        {
            comp_id: compId,
            modifications: "Direct HTML content update",
            html_content: currentHtmlContent.trim()
        },
        {
            onSuccess: (response) => {
                const data = response.data;

                if (data && data.status === 'processing' && data.task_id) {
                    pollTaskCompletion(data.task_id);
                } else {
                    errorAlertRef.current?.show("Failed to start modification task");
                }
            },
            onError: (error) => {
                console.error('Direct HTML save failed:', error);
                errorAlertRef.current?.show("Failed to save changes");
            }
        }
    );
};

const handleTaskSuccess = (data) => {
    console.log('Task success data:', data);
    
    setIsUpdating(false);
    setUserInput('');
    
    if (data.html_content) {
        console.log('Updating HTML content with new version');
        setHtmlContent(data.html_content);
        lastKnownContent.current = data.html_content;
    }
    
    if (data.version_id && data.version_name) {
        const newVersion: ComparisonVersion = {
            id: data.version_id,
            version_name: data.version_name,
            html_code: data.html_content,
            changes_summary: data.changes_summary || data.modifications_applied || 'Modifications applied',
            created_at: new Date().toISOString(),
            created_at_relative: "Just now",
            code_length: data.html_content ? data.html_content.length : 0,
            code_preview: data.html_content ? data.html_content.substring(0, 200) : ''
        };
        
        setVersions(prev => {
            const filteredVersions = prev.filter(v => v.id !== data.version_id);
            return [newVersion, ...filteredVersions];
        });
        
        setCurrentVersionId(data.version_id);
        
        if (comparisonData) {
            const updatedComparisonData: ComparisonDataType = {
                ...comparisonData,
                html_content: data.html_content,
                current_version_id: data.version_id
            };
            setComparisonData(updatedComparisonData);
            console.log('Updated comparisonData with new content');
        }
    }
    
    successAlertRef.current?.show(
        `Comparison page updated successfully! ${data.changes_summary || data.modifications_applied || 'Modifications applied.'}`
    );
};

const handleTaskError = (data) => {
    console.error('Task failed:', data);
    setIsUpdating(false);
    errorAlertRef.current?.show(
        data.error_message || "Failed to update comparison page"
    );
};

const pollTaskMut = useMutation({
    ...updatedComparisonPageMutation,
    retry: false, 
});

const pollTaskCompletion = (taskId: string) => {
    const pollInterval = setInterval(() => {
        pollTaskMut.mutate(
            { task_id: taskId },
            {
                onSuccess: (response) => {
                    const data = response.data;

                    if (data.status === 'success') {
                        clearInterval(pollInterval);
                        handleTaskSuccess(data);
                    } else if (data.status === 'error') {
                        clearInterval(pollInterval);
                        handleTaskError(data);
                    } else if (data.status === 'processing') {
                        console.log('Task still processing...', data.progress || '');
                    }
                },
                onError: (error) => {
                    console.error('Polling error:', error);
                    clearInterval(pollInterval);
                    setIsUpdating(false);
                    errorAlertRef.current?.show("Failed to check task status");
                }
            }
        );
    }, 2000);

    setTimeout(() => {
        clearInterval(pollInterval);
        setIsUpdating(false);
    }, 300000);
};

useEffect(() => {
    if (comparisonPageListData?.data) {
        setComparisonPageLimits({
            generated: comparisonPageListData.data.comparison_pages_generated || 0,
            maxAllowed: comparisonPageListData.data.max_comparison_pages_allowed || 0
        });
    }
}, [comparisonPageListData]);

useEffect(() => {
    if (state?.fromOtherComparison && state?.compId) {
        setCompId(state.compId);
        setComparisonType(state.comparisonType || '');
        setUrl1(state.url1 || '');
        setUrl2(state.url2 || '');
        
    } else if (state?.fromExisting && state?.compId) {
        setCompId(state.compId);
        setComparisonType(state.comparisonType || '');
        setUrl1(state.url1 || '');
        setUrl2(state.url2 || '');
        
    } else if (state?.fromCustomUrls && state?.url1 && state?.url2) {
        setUrl1(state.url1);
        setUrl2(state.url2);
        
        setTimeout(() => {
            handleGenerate();
        }, 500);
    }
}, [state, handleGenerate]);

useEffect(() => {
    if (taskId && !currentTaskId) {
        setCurrentTaskId(taskId);
    }
}, [taskId, currentTaskId]);

const lastKnownContent = useRef('');

useEffect(() => {
    if (iframeRef.current && htmlContent && !isUpdatingRef.current) {
        const iframe = iframeRef.current;
        const doc = iframe.contentDocument || iframe.contentWindow?.document;
        
        if (doc) {
            const currentDocContent = doc.documentElement.outerHTML;
            
            if (currentDocContent !== htmlContent && lastKnownContent.current !== htmlContent) {
                console.log('Updating iframe with new content');
                doc.open();
                doc.write(htmlContent);
                doc.close();
                lastKnownContent.current = htmlContent;
            }
            
            if (doc.body && !isUpdating && !modifyPageMutation.isLoading) {
                doc.body.contentEditable = isEditMode ? 'true' : 'false';
                
                if (isEditMode) {
                    doc.body.style.cursor = 'text';
                } else {
                    doc.body.style.cursor = 'default';
                }
                
                const existingListener = doc.body.getAttribute('data-listener-added');
                if (!existingListener && isEditMode) {
                    doc.body.setAttribute('data-listener-added', 'true');
                    
                    let updateTimeout;
                    doc.body.addEventListener('input', (e) => {
                        clearTimeout(updateTimeout);
                        
                        updateTimeout = setTimeout(() => {
                            if (!isUpdatingRef.current) {
                                isUpdatingRef.current = true;
                                const newContent = doc.documentElement.outerHTML;
                                
                                if (newContent !== lastKnownContent.current) {
                                    setHtmlContent(newContent);
                                    lastKnownContent.current = newContent;
                                    console.log('Content updated from iframe editing');
                                }
                                
                                setTimeout(() => {
                                    isUpdatingRef.current = false;
                                }, 100);
                            }
                        }, 500);
                    });
                }
            } else if (doc.body && !isEditMode) {
                doc.body.contentEditable = 'false';
                doc.body.style.cursor = 'default';
            }
        }
    }
}, [htmlContent, isUpdating, modifyPageMutation.isLoading, isEditMode]); 

const switchToVersion = async (versionId: number) => {
    const selectedVersion = versions.find(v => v.id === versionId);
    if (!selectedVersion || !comparisonData) {
        console.error('Selected version or comparisonData not found');
        return;
    }

    try {
        console.log('Switching to version:', versionId, 'with content length:', selectedVersion.html_code.length);
        
        setCurrentVersionId(versionId);
        setHtmlContent(selectedVersion.html_code);
        lastKnownContent.current = selectedVersion.html_code;
        
        const updatedComparisonData: ComparisonDataType = {
            ...comparisonData,
            html_content: selectedVersion.html_code,
            current_version_id: versionId
        };
        setComparisonData(updatedComparisonData);
        
        const response = await setCurrentComparisonVersionFn(comparisonData.comp_id, versionId);
        
        if (response.status !== 200 && response.data?.status !== 'success') {
            console.error('Backend update failed:', response);
        }
    } catch (error) {
        console.error('Failed to switch version:', error);
    }
};

useEffect(() => {
    if (currentVersionId && versions.length > 0) {
        const savedVersion = versions.find(v => v.id === currentVersionId);
        if (savedVersion && savedVersion.html_code !== htmlContent) {
            setHtmlContent(savedVersion.html_code);
        } else if (!savedVersion) {
            console.warn(`Version ${currentVersionId} not found in versions array`);
        }
    }
}, [currentVersionId, versions]);

useEffect(() => {
    if (comparisonPageData) {
        const response = comparisonPageData as ComparisonPageApiResponse;
        
        if (response?.data?.comparison_data) {
            const { 
                comp_id, 
                html_content, 
                url1, 
                url2, 
                versions, 
                current_version_id 
            } = response.data.comparison_data;
            
            const comparisonDataObj: ComparisonDataType = {
                comp_id,
                html_content,
                url1,
                url2,
                current_version_id
            };
            
            setCompId(comp_id);
            setComparisonData(comparisonDataObj);
            setUrl1(url1);
            setUrl2(url2);
            setShowComparisonPage(true);
            
            if (versions && Array.isArray(versions) && versions.length > 0) {
                setVersions(versions);
                
                const versionToSet = current_version_id || versions[0].id;
                setCurrentVersionId(versionToSet);
                
                const initialVersion = versions.find(v => v.id === versionToSet);
                if (initialVersion) {
                    setHtmlContent(initialVersion.html_code);
                } else {
                    setHtmlContent(html_content);
                }
            } else {
                console.warn('No versions found in response');
                setVersions([]);
                setCurrentVersionId(null);
                setHtmlContent(html_content);
            }
            
            if (state?.fromOtherComparison) {
                setTimeout(() => {
                    successAlertRef.current?.show("New comparison page created and loaded successfully!");
                }, 500);
            } else if (state?.fromExisting) {
                setTimeout(() => {
                    successAlertRef.current?.show("Comparison page loaded successfully!");
                }, 500);
            }
        } else {
            console.error('Invalid data structure received:', response);
            errorAlertRef.current?.show("Invalid data structure received from server");
        }
    }
}, [comparisonPageData, state?.fromOtherComparison, state?.fromExisting]);

const injectTrialFooter = (htmlContent) => {
    return htmlContent;
};

const injectFooterWithJS = (): void => {
    const iframe = iframeRef.current;
    if (!iframe || !iframe.contentDocument) return;
    
    const doc = iframe.contentDocument;
    
    if (doc.body) {
        doc.body.setAttribute('spellcheck', 'false');
    }
    if (doc.documentElement) {
        doc.documentElement.setAttribute('spellcheck', 'false');
    }
    
    const waitForContentToLoad = (): Promise<void> => {
        return new Promise<void>((resolve) => {
            let attempts = 0;
            const maxAttempts = 50;
            
            const checkContent = () => {
                attempts++;
                
                const bodyContent = doc.body?.textContent?.trim() || '';
                const hasImages = doc.querySelectorAll('img').length > 0;
                const hasElements = doc.querySelectorAll('div, section, article, main, header, footer, nav, p, h1, h2, h3, h4, h5, h6').length > 0;
                
                const hasSubstantialContent = bodyContent.length > 100;
                const hasStructure = hasElements;
                
                if (hasSubstantialContent && hasStructure) {
                    if (hasImages) {
                        const images = doc.querySelectorAll('img');
                        const imagePromises = Array.from(images).map((img: HTMLImageElement) => {
                            return new Promise<void>((imgResolve) => {
                                if (img.complete) {
                                    imgResolve();
                                } else {
                                    img.onload = () => imgResolve();
                                    img.onerror = () => imgResolve();
                                    setTimeout(() => imgResolve(), 3000);
                                }
                            });
                        });
                        
                        Promise.all(imagePromises).then(() => {
                            resolve();
                        });
                    } else {
                        resolve();
                    }
                } else if (attempts >= maxAttempts) {
                    resolve();
                } else {
                    setTimeout(checkContent, 200);
                }
            };
            
            checkContent();
        });
    };
    
    waitForContentToLoad().then(() => {
        setTimeout(() => {
            if (doc.querySelector('.custom-banner')) {
                return;
            }
            
            const footer = doc.createElement('div');
            footer.className = 'custom-banner';
            footer.contentEditable = 'false';
            footer.setAttribute('data-non-editable', 'true');
            footer.setAttribute('spellcheck', 'false');
            
            footer.innerHTML = `
                <div class="container">
                    <div class='button'>
                        <img src="https://abun.com/wp-content/uploads/2025/06/Ai-icon.svg" alt="AI Icon">
                        <span>Made with</span>
                        <a href="https://abun.com" target="_blank">Abun.com</a>
                    </div>
                </div>
            `;
            
            const style = doc.createElement('style');
            style.textContent = `
                /* Reset and spellcheck disable */
                * {
                    -webkit-spellcheck: false !important;
                    spellcheck: false !important;
                }
                
                /* Body adjustments for footer */
                body {
                    position: relative !important;
                    min-height: 100vh !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    padding-bottom: 80px !important;
                    box-sizing: border-box !important;
                }
                
                /* Main footer container with maximum specificity */
                .custom-banner {
                    all: initial !important;
                    position: absolute !important;
                    bottom: 0 !important;
                    left: 0 !important;
                    right: 0 !important;
                    top: auto !important;
                    width: 100% !important;
                    height: auto !important;
                    
                    background: #f3f4f6 !important;
                    background-color: #f3f4f6 !important;
                    border: none !important;
                    border-top: 1px solid #d1d5db !important;
                    border-radius: 0 !important;
                    padding: 20px 16px !important;
                    margin: 0 !important;
                    box-sizing: border-box !important;
                    z-index: 2147483647 !important;
                    
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    flex-direction: row !important;
                    flex-wrap: nowrap !important;
                    min-height: 60px !important;
                    max-height: none !important;
                    
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                    font-size: 14px !important;
                    font-weight: 400 !important;
                    line-height: 1.4 !important;
                    color: #374151 !important;
                    text-align: center !important;
                    text-decoration: none !important;
                    text-transform: none !important;
                    
                    -webkit-user-select: none !important;
                    -moz-user-select: none !important;
                    -ms-user-select: none !important;
                    user-select: none !important;
                    pointer-events: auto !important;
                    
                    visibility: visible !important;
                    opacity: 1 !important;
                    overflow: visible !important;
                    
                    box-shadow: none !important;
                    outline: none !important;
                    transform: none !important;
                    transition: none !important;
                    animation: none !important;
                    
                    float: none !important;
                    clear: both !important;
                    vertical-align: baseline !important;
                }
                
                /* Container within footer */
                .custom-banner .container {
                    all: initial !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    flex-direction: row !important;
                    flex-wrap: nowrap !important;
                    width: 100% !important;
                    height: auto !important;
                    max-width: none !important;
                    min-width: 0 !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    border: none !important;
                    background: none !important;
                    box-sizing: border-box !important;
                    position: relative !important;
                    z-index: auto !important;
                    overflow: visible !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                    float: none !important;
                    clear: none !important;
                    vertical-align: baseline !important;
                }
                
                /* Button styling */
                .custom-banner .button {
                    all: initial !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    flex-direction: row !important;
                    flex-wrap: nowrap !important;
                    gap: 8px !important;
                    
                    background: white !important;
                    background-color: white !important;
                    border: 1px solid #D2D2EB !important;
                    border-radius: 50px !important;
                    padding: 12px 18px !important;
                    margin: 0 !important;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
                    box-sizing: border-box !important;
                    
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                    font-size: 14px !important;
                    font-weight: 400 !important;
                    line-height: 1.4 !important;
                    color: #000000 !important;
                    text-decoration: none !important;
                    text-align: center !important;
                    text-transform: none !important;
                    
                    cursor: pointer !important;
                    pointer-events: auto !important;
                    transition: all 0.2s ease !important;
                    
                    white-space: nowrap !important;
                    flex-shrink: 0 !important;
                    
                    position: relative !important;
                    z-index: auto !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    overflow: visible !important;
                    
                    width: auto !important;
                    height: auto !important;
                    min-width: 0 !important;
                    min-height: 0 !important;
                    max-width: none !important;
                    max-height: none !important;
                    
                    float: none !important;
                    clear: none !important;
                    vertical-align: baseline !important;
                    
                    outline: none !important;
                    transform: none !important;
                    animation: none !important;
                }
                
                /* Button hover state */
                .custom-banner .button:hover {
                    transform: translateY(-1px) !important;
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
                    background: white !important;
                    background-color: white !important;
                    border: 1px solid #D2D2EB !important;
                }
                
                /* Button image styling */
                .custom-banner .button img {
                    all: initial !important;
                    width: 16px !important;
                    height: 16px !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    border: none !important;
                    background: none !important;
                    background-color: transparent !important;
                    vertical-align: middle !important;
                    flex-shrink: 0 !important;
                    display: inline-block !important;
                    
                    position: relative !important;
                    z-index: auto !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    overflow: visible !important;
                    
                    min-width: 16px !important;
                    min-height: 16px !important;
                    max-width: 16px !important;
                    max-height: 16px !important;
                    
                    float: none !important;
                    clear: none !important;
                    
                    outline: none !important;
                    transform: none !important;
                    animation: none !important;
                    transition: none !important;
                    
                    box-sizing: border-box !important;
                    object-fit: contain !important;
                    object-position: center !important;
                }
                
                /* Button span styling */
                .custom-banner .button span {
                    all: initial !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    border: none !important;
                    background: none !important;
                    
                    color: #000000 !important;
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                    font-size: 14px !important;
                    font-weight: 400 !important;
                    line-height: 1.4 !important;
                    text-decoration: none !important;
                    text-align: center !important;
                    text-transform: none !important;
                    
                    display: inline !important;
                    white-space: nowrap !important;
                    
                    position: relative !important;
                    z-index: auto !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    overflow: visible !important;
                    
                    width: auto !important;
                    height: auto !important;
                    min-width: 0 !important;
                    min-height: 0 !important;
                    max-width: none !important;
                    max-height: none !important;
                    
                    float: none !important;
                    clear: none !important;
                    vertical-align: baseline !important;
                    
                    outline: none !important;
                    transform: none !important;
                    animation: none !important;
                    transition: color 0.2s ease !important;
                    
                    box-sizing: border-box !important;
                }
                
                /* Button link styling */
                .custom-banner .button a {
                    all: initial !important;
                    color: #000000 !important;
                    text-decoration: underline !important;
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                    font-size: 14px !important;
                    font-weight: 400 !important;
                    line-height: 1.4 !important;
                    text-align: center !important;
                    text-transform: none !important;
                    
                    margin: 0 !important;
                    padding: 0 !important;
                    border: none !important;
                    background: none !important;
                    background-color: transparent !important;
                    
                    display: inline !important;
                    white-space: nowrap !important;
                    cursor: pointer !important;
                    pointer-events: auto !important;
                    transition: color 0.2s ease !important;
                    
                    position: relative !important;
                    z-index: auto !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    overflow: visible !important;
                    
                    width: auto !important;
                    height: auto !important;
                    min-width: 0 !important;
                    min-height: 0 !important;
                    max-width: none !important;
                    max-height: none !important;
                    
                    float: none !important;
                    clear: none !important;
                    vertical-align: baseline !important;
                    
                    outline: none !important;
                    transform: none !important;
                    animation: none !important;
                    
                    box-sizing: border-box !important;
                }
                
                /* Hover states */
                .custom-banner .button:hover a {
                    color: #2563eb !important;
                    text-decoration: underline !important;
                }
                
                .custom-banner .button:hover span {
                    color: #2563eb !important;
                }
                
                /* Mobile responsiveness */
                @media (max-width: 768px) {
                    body {
                        padding-bottom: 70px !important;
                    }
                    
                    .custom-banner {
                        padding: 16px 12px !important;
                        min-height: 50px !important;
                    }
                    
                    .custom-banner .button {
                        padding: 10px 16px !important;
                        font-size: 13px !important;
                    }
                    
                    .custom-banner .button span {
                        font-size: 13px !important;
                    }
                    
                    .custom-banner .button a {
                        font-size: 13px !important;
                    }
                    
                    .custom-banner .button img {
                        width: 14px !important;
                        height: 14px !important;
                        min-width: 14px !important;
                        min-height: 14px !important;
                        max-width: 14px !important;
                        max-height: 14px !important;
                    }
                }
                
                /* Ensure all elements have proper box-sizing */
                .custom-banner * {
                    box-sizing: border-box !important;
                }
                
                /* Force visibility on all footer elements */
                .custom-banner,
                .custom-banner *,
                .custom-banner .container,
                .custom-banner .button {
                    visibility: visible !important;
                    opacity: 1 !important;
                }
                
                .custom-banner .container {
                    display: flex !important;
                }
                
                .custom-banner .button {
                    display: flex !important;
                }
                
                .custom-banner .button span,
                .custom-banner .button a {
                    display: inline !important;
                }
                
                /* Ensure content doesn't overlap footer */
                body > *:not(.custom-banner) {
                    position: relative !important;
                    z-index: 1 !important;
                }
                
                body > div:not(.custom-banner),
                body > section:not(.custom-banner),
                body > main:not(.custom-banner),
                body > article:not(.custom-banner) {
                    padding-bottom: 20px !important;
                }
                
                /* Additional isolation rules */
                .custom-banner {
                    contain: layout style !important;
                    isolation: isolate !important;
                }
                
                /* Prevent any external styles from affecting footer */
                .custom-banner,
                .custom-banner * {
                    font-style: normal !important;
                    font-variant: normal !important;
                    text-indent: 0 !important;
                    text-shadow: none !important;
                    letter-spacing: normal !important;
                    word-spacing: normal !important;
                    direction: ltr !important;
                    writing-mode: horizontal-tb !important;
                    unicode-bidi: normal !important;
                }
            `;
            
            doc.head.appendChild(style);
            doc.body.appendChild(footer);
            
            // Event listeners to prevent editing
            footer.addEventListener('keydown', (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
            
            footer.addEventListener('input', (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
            
            footer.addEventListener('paste', (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
            
            // Handle resize
            const handleResize = () => {
                if (footer.parentNode) {
                    footer.style.setProperty('width', '100%', 'important');
                }
            };
            
            doc.defaultView?.addEventListener('resize', handleResize);
            
        }, 300);
    });
};

const getIframeContent = () => {
    if (!comparisonData?.html_content) return '';
    return injectTrialFooter(comparisonData.html_content);
};

const handleIframeLoad = () => {
    const iframeDoc = iframeRef.current?.contentDocument;
    if (!iframeDoc) return;
    
    // Check if this is a trial plan
    if (currentPlanName?.toLowerCase().trim() === "trial") {
        // Wait for document ready state
        const checkReadyState = () => {
            if (iframeDoc.readyState === 'complete') {
                injectFooterWithJS();
            } else {
                setTimeout(checkReadyState, 100);
            }
        };
        
        checkReadyState();
    }
};
    

const backToList = () => {
    navigate('/ai-comparison-table');
};

const copyToClipboard = (text: string) => {
navigator.clipboard.writeText(text).then(() => {
    successAlertRef.current?.show("Copied to clipboard!");
}).catch(err => {
    errorAlertRef.current?.show("Failed to copy to clipboard.");
});
};


// Delete comparison page version mutation
const deleteComparisonPageVersionMut = useMutation({
    ...deleteComparisonPageVersionMutation,
    onSuccess: (data, variables) => {
        // Handle successful deletion
        const deletedVersionId = variables.version_id;
        
        // Remove from frontend state
        setVersions(prev => prev.filter(v => v.id !== deletedVersionId));
        
        // If current version is deleted, switch to the most recent one
        if (currentVersionId === deletedVersionId) {
            const remainingVersions = versions.filter(v => v.id !== deletedVersionId);
            if (remainingVersions.length > 0) {
                switchToVersion(remainingVersions[0].id);
            }
        }
        
        successAlertRef.current?.show("Version deleted successfully!");
    },
    onError: (error, variables) => {
        console.error('Error deleting version:', error);
        
        // Handle specific error cases
        if (error?.response?.data?.err_id === 'CANNOT_DELETE_LAST_VERSION') {
            errorAlertRef.current?.show("Cannot delete the last remaining version");
        } else if (error?.response?.data?.err_id === 'VERSION_NOT_FOUND') {
            errorAlertRef.current?.show("Version not found or access denied");
        } else {
            errorAlertRef.current?.show("Failed to delete version. Please try again.");
        }
    }
});

// Delete version function
const deleteVersion = (versionId: number) => {
    // Check if this is the last version
    if (versions.length <= 1) {
        errorAlertRef.current?.show("Cannot delete the last remaining version");
        return;
    }
    
    // Show the confirmation modal instead of window.confirm
    setVersionToDelete(versionId);
    setShowDeleteModal(true);
};

// Handle delete confirmation
const handleConfirmDelete = async () => {
    if (versionToDelete !== null) {
        // Trigger the mutation
        deleteComparisonPageVersionMut.mutate({ version_id: versionToDelete });
        
        // Reset state
        setShowDeleteModal(false);
        setVersionToDelete(null);
    }
};

// Handle delete cancellation
const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setVersionToDelete(null);
};


const handleGenerateScript = () => {
    if (!compId) {
        errorAlertRef.current?.show("No comparison page selected");
        return;
    }
    
    const serverUrl = process.env.REACT_APP_DRF_DOMAIN || 'http://localhost:8000';
    
    // Pass currentPlanName as a query parameter without timestamp
    const universalScript = `<script src="${serverUrl}/api/frontend/get-universal-comparison-script/?plan=${currentPlanName}" defer></script>`;
    
    // Enhanced div tag with better isolation and chart support
    const divTag = `<div data-comp-id="${compId}" style="width: 100%; min-height: 400px; display: block; position: relative; margin: 0; padding: 0; box-sizing: border-box; background: transparent; border: none; outline: none;"></div>`;
    
    setScriptTag(universalScript);
    setDivTag(divTag);
    setScriptGenerated(true);
};

     
         const showGeneratorView = !comp_id;
    const showPageView = !!comp_id;

         return (
             <>
                  {/* Generator View - Direct JSX instead of component */}
            {showGeneratorView && (
                <div className="ai-comparison-generator-container">
                    <div className="ai-comparison-generator-content">
                        <div className="is-flex is-align-items-center is-justify-content-space-between w-100">
                            <svg 
                                onClick={backToList} 
                                width="30" 
                                height="24" 
                                viewBox="0 0 30 24" 
                                fill="none" 
                                xmlns="http://www.w3.org/2000/svg" 
                                style={{cursor:'pointer'}}
                            >
                                <path 
                                    d="M26.0435 12.0003H2.82031M2.82031 12.0003L12.8382 1.98242M2.82031 12.0003L12.8382 22.0181" 
                                    stroke="black" 
                                    strokeOpacity="0.5" 
                                    strokeWidth="3" 
                                />
                            </svg>
                            <div>
                                <h2 className="ai-comparison-generator-title">AI Competitor Comparison Page Generator</h2>
                                <p className="ai-comparison-generator-description">
                                    Compare websites and generate detailed comparison content
                                </p>
                            </div>
                            <svg  
                                width="30" 
                                height="24" 
                                viewBox="0 0 30 24" 
                                fill="none" 
                                xmlns="http://www.w3.org/2000/svg" 
                                style={{opacity: 0}}
                            >
                                <path 
                                    d="M26.0435 12.0003H2.82031M2.82031 12.0003L12.8382 1.98242M2.82031 12.0003L12.8382 22.0181" 
                                    stroke="black" 
                                    strokeOpacity="0.5" 
                                    strokeWidth="3" 
                                />
                            </svg>
                        </div>
                        <hr className="horizontal-line" />

                        <div className="ai-comparison-input-card">
                            <div className="ai-comparison-urls-section">
                                <div className="ai-comparison-input-group">
    <label htmlFor="url1">Your Website</label>
    <input
        id="url1"
        type="url"
        value={url1}
        onChange={(e) => setUrl1(e.target.value)}
        placeholder={url1 === '' ? "Draftss or https://draftss.com" : ""}
        className="ai-comparison-url-input"
        disabled={isLoading}
    />
</div>
                                
                                <div className="ai-comparison-input-group">
                                    <label htmlFor="url2">Your Competitors Website or Brand</label>
                                    <input
                                        id="url2"
                                        type="url"
                                        value={url2}
                                        onChange={(e) => setUrl2(e.target.value)}
                                        placeholder="Draftss or https://draftss.com"
                                        className="ai-comparison-url-input"
                                        disabled={isLoading}
                                    />
                                </div>
                            </div>

                            <button
                                className="ai-generate-comparison-button"
                                onClick={handleGenerate}
                                disabled={isLoading || !url1.trim() || !url2.trim()}
                            >
                                {isLoading ? "Generating..." : "Generate ➜"}
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {showPageView && (
            <div className={`ai-comparison-page-container full-comparison-view`}>
                {/* Header section - Always visible */}
                <div className={`card ai-comparison-page-header comparison-view`}>
                    <div className="left-header-section">
                        <span className="back-btn" onClick={backToList}>
                            <svg
                                className="back-btn"
                                width="30"
                                height="24"
                                viewBox="0 0 30 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path d="M26.0435 12.0003H2.82031M2.82031 12.0003L12.8382 1.98242M2.82031 12.0003L12.8382 22.0181" stroke="black" strokeOpacity="0.5" strokeWidth="3" />
                            </svg>
                        </span>
                                            
                        <a href="/" onClick={(e) => { e.preventDefault(); navigate("/"); }}>
                            <svg className="abun-logo" width="52" height="48" viewBox="0 0 52 48">
                                <rect x="2.125" y="4.41016" width="47.9091" height="42.0909" rx="6.5" fill="black" stroke="black" strokeWidth="3" />
                                <rect x="0.5" y="0.5" width="49.9091" height="44.0909" rx="7.5" fill="white" stroke="black" />
                                <path d="M40 37.3373H29.7561V34.7968C28.2195 36.6746 24.8618 38 21.4472 38C17.3496 38 12 35.2939 12 29.2189C12 22.5917 17.3496 20.714 21.4472 20.714C25.0325 20.714 28.2764 21.8185 29.7561 23.641V20.8797C29.7561 19.002 27.9919 17.5661 24.6341 17.5661C22.0732 17.5661 19.1707 18.5602 17.0081 20.1617L13.5366 14.0316C17.2358 11.1598 22.3577 10 26.5122 10C33.3415 10 40 12.3195 40 21.211V37.3373ZM25.7154 31.5385C27.3089 31.5385 29.0732 31.0414 29.7561 30.1026V28.6114C29.0732 27.6726 27.3089 27.1755 25.7154 27.1755C24.0081 27.1755 22.1301 27.7278 22.1301 29.3846C22.1301 31.0414 24.0081 31.5385 25.7154 31.5385Z" fill="black" />
                            </svg>
                        </a>
                                            
                        <div className="Tabs">
                            <div className="Tab active">
                                Comparison Page
                            </div>
                        </div>
                    </div>
                    
                    <div className="right-header-section">
                    <div className="editor-controls">
                        {/* Edit Mode Toggle */}
                        <div className="edit-mode-toggle">
                            <div 
                                className={`toggle-switch ${isEditMode ? 'active' : ''}`}
                                onClick={() => setIsEditMode(!isEditMode)}
                            >
                                <div className="toggle-slider"></div>
                            </div>
                            <span className="edit-mode-label">Edit Mode</span>
                        </div>

                        <button 
                            className="sidebar-button save-btn"
                            onClick={handleSaveDirectHtmlChanges}
                            disabled={isGenerating || isUpdating}
                            style={{
                                opacity: (isGenerating || isUpdating) ? 0.6 : 1,
                                cursor: (isGenerating || isUpdating) ? 'not-allowed' : 'pointer',
                            }}
                        >
                            {isUpdating ? "Saving..." : "Save"}
                        </button>
                                                                
                        <svg
                            className={`collapse-button ${isSidebarCollapsed ? "" : "collapsed"}`}
                            onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
                            width="20"
                            height="20"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            style={{ cursor: 'pointer', marginLeft: '8px' }}
                        >
                            <path fillRule="evenodd" clipRule="evenodd" d="M14 0H2C0.9 0 0 0.9 0 2V14C0 15.1 0.9 16 2 16H14C15.1 16 16 15.1 16 14V2C16 0.9 15.1 0 14 0ZM10 14.5H2C1.7 14.5 1.5 14.3 1.5 14V2C1.5 1.7 1.7 1.5 2 1.5H10V14.5ZM14.5 14C14.5 14.3 14.3 14.5 14 14.5H11.5V1.5H14C14.3 1.5 14.5 1.7 14.5 2V14Z" fill="#666" />
                        </svg>
                        </div>
                    </div>
                </div>

                {/* Content Area */}
                <div className="ai-comparison-page-content comparison-view">
                    <div className="comparison-result-section-new">
                        <div className="comparison-preview-main">
                            <div className="ai-preview-container">
                                {isGenerating && !htmlContent ? (
                                    <div className="loading-container" style={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        height: '88vh',
                                        backgroundColor: '#f8f9fa',
                                        padding: '40px',
                                        border: '1px solid #d1d5db',
                                        borderRadius: '8px',
                                        width: '100%'
                                    }}>
                                        <Player
                                            autoplay
                                            loop
                                            src="https://lottie.host/91a433df-05fa-4ab3-94b2-2c2a0a16a67f/2SoIqH8Kh3.json"
                                            style={{ height: '300px', width: '300px' }}
                                        />
                                        <h1 style={{ color: '#666', marginBottom: '10px', fontWeight: 'bolder' }}>
                                            An Amazing Comparison Page is being cooked for your site!
                                        </h1>
                                        <p style={{ color: '#888', textAlign: 'center', maxWidth: '300px' }}>
                                            Creating your custom comparison page 
                                            This may take a few moments.
                                        </p>
                                    </div>
                                ) : (
                            <div className="iframe-container">
                                <div 
                                    style={{
                                        backgroundColor: '#f3f4f6',
                                        padding: '8px 12px',
                                        borderBottom: '1px solid #d1d5db',
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '8px',
                                        minHeight: '40px',
                                        position: 'sticky',
                                        top: 0,
                                        zIndex: 10,
                                        flexShrink: 0
                                    }}
                                >
                                    <div style={{ display: 'flex', gap: '6px' }}>
                                        <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ff5f57' }} />
                                        <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ffbd2e' }} />
                                        <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#28ca42' }} />
                                    </div>
                                    
                                    <div 
                                        style={{
                                            flex: 1,
                                            backgroundColor: 'white',
                                            border: '1px solid #d1d5db',
                                            borderRadius: '4px',
                                            padding: '6px 12px',
                                            fontSize: '14px',
                                            color: '#6b7280',
                                            marginLeft: '8px'
                                        }}
                                    >
                                        https://<span>{active_website_domain}</span>/<span>{comp_id?.replace(/-[a-f0-9]+$/, '') || 'comparison'}</span>
                                    </div>
                                </div>
                                <div 
                                    style={{
                                        backgroundColor: '#e5e7eb',
                                        padding: '20px 16px',
                                        borderBottom: '1px solid #d1d5db',
                                        fontSize: '14px',
                                        color: '#374151',
                                        textAlign: 'center',
                                        minHeight: '60px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        position: 'sticky',
                                        top: '40px',
                                        zIndex: 9,
                                        flexShrink: 0
                                    }}
                                >
                                    Your Existing Website Navbar
                                </div>
                                
                                {/* Iframe Content */}
                                <iframe                  
                                    ref={iframeRef}                  
                                    srcDoc={getIframeContent()} 
                                    spellCheck="false"                 
                                    style={{                     
                                        width: '100%',                     
                                        height: '100%',
                                        border: 'none',                     
                                        backgroundColor: 'white',                     
                                        display: 'block',
                                        flex: 1
                                    }}                  
                                    title="Comparison Page Preview"                  
                                    onLoad={handleIframeLoad}              
                                />
                            </div>
                                )}
                            </div>
                        </div>
                    <div className={`comparison-sidebar ${isSidebarCollapsed ? 'collapsed' : ''}`}>
                        {/* Update Comparison Dropdown */}
                        <div className="sidebar-section">
                            <div 
                                className={`sidebar-dropdown-header version-header ${updateTabOpen ? "active" : ""}`}
                                onClick={() => setUpdateTabOpen(!updateTabOpen)}
                            >
                                <span><h6>What changes do you want in the Comparison Page?</h6></span>
                            </div>
                            
                            {updateTabOpen && (
                                <div className="sidebar-dropdown-content">
                                    
                                    
                                    <textarea
                                        className="sidebar-textarea"
                                        placeholder="Describe what changes you want to make..."
                                        value={userInput}
                                        onChange={(e) => setUserInput(e.target.value)}
                                        disabled={isUpdating}
                                    />
                                    <button 
                                        className="sidebar-button update-btn"
                                        onClick={handleUpdateComparisonPage}
                                        disabled={isUpdating || !userInput.trim() || modifyPageMutation.isLoading}
                                        style={{
                                            opacity: (isUpdating || !userInput.trim() || modifyPageMutation.isLoading) ? 0.6 : 1,
                                            cursor: (isUpdating || !userInput.trim() || modifyPageMutation.isLoading) ? 'not-allowed' : 'pointer',
                                            position: 'relative'
                                        }}
                                    >
                                        {(isUpdating || modifyPageMutation.isLoading) ? (
                                            <span className="button-content">
                                                <span className="spinner"></span>
                                                {modifyPageMutation.isLoading ? 'Starting...' : 'Updating...'}
                                            </span>
                                        ) : (
                                            "Update Comparison Page"
                                        )}
                                    </button>
                                </div>
                            )}
                        </div>
                    
                        {/* Version History Dropdown */}
                        <div className="sidebar-section">
                            <div 
                                className={`sidebar-dropdown-header version-header ${versionsTabOpen ? "active" : ""}`}
                                onClick={() => setVersionsTabOpen(!versionsTabOpen)}
                            >
                                <span><h6>Version History</h6></span>
                                <span className="version-count">
                                    {comparisonData && versions.length === 0 ? 1 : versions.length}
                                </span>
                            </div>
                            
                            {versionsTabOpen && (
                                <>
                                    <div className="sidebar-dropdown-content">
                                        {comparisonData && versions.length === 0 ? (
                                            <div className="versions-list-sidebar">
                                                <div className="version-item-sidebar current-version">
                                                    <div className="version-header-sidebar">
                                                        <div className="version-info-sidebar">
                                                            <div className="version-number-sidebar">
                                                                <span>Original</span>
                                                            </div>
                                                        </div>
                                                        <div className="version-actions-sidebar" style={{
                                                            display: 'flex',
                                                            justifyContent: 'center',
                                                            alignItems: 'center',
                                                            gap: '8px'
                                                        }}>
                                                            <button className="sidebar-button small switch-btn" style={{backgroundColor: '#10b981'}}>
                                                                Current
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div className="version-description-sidebar">
                                                        Initial comparison version
                                                    </div>
                                                </div>
                                            </div>
                                        ) : versions.length === 0 ? (
                                            <p className="empty-versions">
                                                No versions available for this comparison.
                                            </p>
                                        ) : (
                                            <div className="versions-list-sidebar">
                                                {versions.map((version, index) => (
                                                    <div
                                                        key={version.id}
                                                        className={`version-item-sidebar ${currentVersionId === version.id ? 'current-version' : ''}`}
                                                    >
                                                        <div className="version-header-sidebar">
                                                            <div className="version-info-sidebar">
                                                                <div className="version-number-sidebar">
                                                                    <span>
                                                                        {index === versions.length - 1 
                                                                            ? "Original" 
                                                                            : `v${versions.length - index}`
                                                                        }
                                                                    </span>
                                                                </div>
                                                            </div>
                                                            <div className="version-actions-sidebar" style={{
                                                                display: 'flex',
                                                                justifyContent: 'center',
                                                                alignItems: 'center',
                                                                gap: '8px'
                                                            }}>
                                                                {currentVersionId === version.id ? (
                                                                    <button className="sidebar-button small switch-btn" style={{backgroundColor: '#10b981'}}>
                                                                        Current
                                                                    </button>
                                                                ) : (
                                                                    <>
                    
                                                                        <button
                                                                            className="sidebar-button small switch-btn"
                                                                            onClick={() => switchToVersion(version.id)}
                                                                        >
                                                                            Switch
                                                                        </button>
                                                                        {versions.length > 1 && index !== versions.length - 1 && (
                                                                            <button
                                                                                className="sidebar-button small danger"
                                                                                onClick={() => deleteVersion(version.id)}
                                                                            >
                                                                                Delete
                                                                            </button>
                                                                        )}
                                                                    </>
                                                                )}
                                                            </div>
                                                        </div>
                                                        <div className="version-description-sidebar">
                                                            {version.changes_summary || 'No description available'}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                    
                                    {/* Delete Confirmation Modal */}
                                    <AbunModal
                                        active={showDeleteModal}
                                        headerText="Confirm Delete"
                                        closeable={true}
                                        closeableKey={true}
                                        closeOnOutsideClick={false}
                                        hideModal={handleCancelDelete}
                                    >
                                        <div>
                                            <p>Are you sure you want to delete this version? This action cannot be undone.</p>
                                            <div style={{ 
                                                display: 'flex', 
                                                justifyContent: 'flex-end', 
                                                gap: '10px', 
                                                marginTop: '20px' 
                                            }}>
                                                <button 
                                                    className="button"
                                                    onClick={handleCancelDelete}
                                                >
                                                    Cancel
                                                </button>
                                                <button 
                                                    className="button is-danger"
                                                    onClick={handleConfirmDelete}
                                                >
                                                    OK
                                                </button>
                                            </div>
                                        </div>
                                    </AbunModal>
                                </>
                            )}
                        </div>
                    
                        {/* Get Embed Code Dropdown */}
                        {!isGenerating && !isUpdating && comparisonData && htmlContent && currentVersionId && (
                        <div className="sidebar-section">
                            <div 
                                className={`sidebar-dropdown-header version-header ${embedTabOpen ? "active" : ""}`}
                                onClick={() => {
                                    setEmbedTabOpen(!embedTabOpen);
                                    handleGenerateScript();
                                    }}
                            >
                                <span><h6>Get Embed Code</h6></span>
                            </div>
                        
                            
                                <div className="sidebar-dropdown-content">
                                    <div>
                                        <div style={{marginBottom: '15px'}}>
                                            <label style={{display: 'block', marginBottom: '5px', fontWeight: '500'}}>
                                                Add this Script Tag to your head:
                                            </label>
                                            <textarea
                                                className="sidebar-textarea embed-code"
                                                readOnly
                                                value={scriptTag || '<!-- Script will be generated here -->'}
                                                style={{minHeight: '90px', fontSize: '12px'}}
                                            />
                                            <button
                                                className="sidebar-button copy-btn"
                                                onClick={() => copyToClipboard(scriptTag)}
                                                disabled={!scriptTag}
                                            >
                                                Copy
                                            </button>
                                        </div>
                    
                                        <div style={{marginBottom: '15px'}}>
                                            <label style={{display: 'block', marginBottom: '5px', fontWeight: '500'}}>
                                                Add this Div Tag in the body where you want to load:
                                            </label>
                                            <textarea
                                                className="sidebar-textarea embed-code"
                                                readOnly
                                                value={divTag || '<!-- Div tag will be generated here -->'}
                                                style={{minHeight: '110px', fontSize: '12px'}}
                                            />
                                            <button
                                                className="sidebar-button copy-btn"
                                                onClick={() => copyToClipboard(divTag)}
                                                disabled={!divTag}
                                            >
                                                Copy
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            
                        </div>
                        )}
                    </div>
                    </div>
                </div>
        </div>
        )}
                 
                 <ErrorAlert ref={errorAlertRef} />
                 <SuccessAlert ref={successAlertRef} />
             </>
         );
}

export default AIComparisonPage;