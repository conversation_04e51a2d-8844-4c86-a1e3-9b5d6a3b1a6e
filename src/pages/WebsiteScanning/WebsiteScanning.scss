@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/helpers/all";
// @import "bulma/sass/elements/all";
// @import "bulma/sass/components/tabs";


@import "../../assets/bulma-overrides/bulmaOverrides";

.website-scanning-container {

    .stats-card-container{
        gap: 16px;

        .stats-card{
            background: #fff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 1px 2px #0000000d;
            transition: box-shadow .2s ease;

            .stats-card-header{
                display: flex;
                gap: 8px;
                padding: 0.75rem;
            }

            hr{
                margin: 0;
                background: #e5e7eb;
                height: 1px;
            }

            .stats-card-number{
                padding: 0.75rem;
            }
        }

    }

    .abun-table-responsive {
        margin-top: -40px;

        @media (max-width:1210px) {
            margin-top: 0;
            
        }

        .abun-table-content {
            justify-content: end !important;

            @media (max-width:1210px) {
                justify-content: start !important;
                margin-top: 1rem;
            }

            input{
              margin-left: 0;
            }
    
            .balancer{
              display: none;
            }
         }

        tbody {
            tr td {
                text-decoration: none;
            }
        }
    }

    .website-scanning-box {
        background: #ffffff;
        border-radius: 12px;
        border: 1px solid #e5e7eb;
        box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
        padding: 24px;
        max-width: 800px;
        margin: 20px auto;

        h2 {
            font-size: 22px;
            font-weight: 500;
            color: #333;
        }

        p {
            font-size: 18px;
            color: #6b7280;
            margin-bottom: 12px;
        }

        .website-scanning-benefits {
            background: #eff6ff;
            border-radius: 8px;
            padding: 16px;
            font-size: 16px;
            color: #2563eb;
            border: 1px solid #dbeafe;
            margin: 16px 0;
            font-weight: 500;

            .benefits-title {
                font-weight: 500;
                margin-bottom: 8px;
                font-family: $primary-font !important;
            }

            ul {
                list-style-type: disc;
                padding-left: 20px;
                margin: 0;

                li {
                    font-family: $secondary-font;
                    margin-bottom: 4px;
                }
            }
        }

        .website-scanning-privacy {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: #f9fafb;
            border-radius: 8px;
            margin-top: 16px;

            .privacy-icon {
                font-size: 30px;
            }

            .privacy-text {
                display: flex;
                flex-direction: column;

                .privacy-title {
                    font-size: 18px;
                    font-weight: 500;
                    color: #333;
                }

                p {
                    font-size: 14px;
                    color: #6b7280;
                    margin: 0;
                }
            }
        }

        .website-scanning-status {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: #fef3c7;
            border-radius: 8px;
            margin-top: 16px;
            border: 1px solid #fbbf24;

            .status-icon {
                font-size: 30px;
            }

            .status-text {
                display: flex;
                flex-direction: column;

                .status-title {
                    font-size: 18px;
                    font-weight: 500;
                    color: #92400e;
                }

                p {
                    font-size: 14px;
                    color: #92400e;
                    margin: 0;
                }
            }
        }
    }

}

.summary-content-tooltip {
    max-width: 600px;
    white-space: normal;
    border-radius: 10px !important;
    font-family: $secondary-font !important;
}

.website-scanning-summary-cell {
    position: relative;
    max-width: 300px;
    cursor: pointer;

    .summary-content {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #000;
    }

    .summary-tooltip {
        display: none;
        position: absolute;
        left: 0;
        top: 100%;
        background: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 8px;
        z-index: 1000;
        width: max-content;
        max-width: 400px;
        white-space: normal;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &:hover .summary-tooltip {
        display: block;
    }
}

.analysis-progress-container {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    min-height: 100vh;
    padding: 2rem;
    gap: 1.5rem;
    background: #fff;
    max-width: 100%;
    width: 100vw;

    .analysis-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        width: 100%;
        max-width: 800px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        .analysis-title {
            font-family: $secondary-font;
            font-size: 2.5rem;
            font-weight: 600;
            color: #000;
            margin-bottom: 0.75rem;
        }

        .analysis-subtitle {
            font-family: $secondary-font;
            font-size: 1.125rem;
            color: #666;
            margin-bottom: 3rem;
        }

        .progress-bar {
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            margin: 2rem 0;
            overflow: hidden;

            .progress-fill {
                height: 100%;
                background: #4a90e2;
                border-radius: 4px;
                transition: width 0.3s ease;
            }
        }

        .analysis-stats {
            display: flex;
            justify-content: space-between;
            margin: 2rem 0;
            text-align: left;

            .stat-item {
                h2 {
                    font-family: $secondary-font;
                    font-size: 2rem;
                    font-weight: 600;
                    color: #000;
                    margin-bottom: 0.5rem;
                }

                p {
                    font-family: $secondary-font;
                    font-size: 1rem;
                    color: #666;
                }
            }
        }
    }

    .analysis-steps-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        width: 100%;
        max-width: 800px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        h3 {
            font-family: $secondary-font;
            font-size: 1.25rem;
            font-weight: 600;
            color: #000;
            margin-bottom: 1.5rem;
        }

        ul {
            list-style: none;
            padding: 0;
            margin: 0;

            li {
                font-family: $secondary-font;
                font-size: 1rem;
                color: #666;
                margin-bottom: 1rem;
                padding-left: 2rem;
                position: relative;

                &:before {
                    content: "";
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 12px;
                    height: 12px;
                    background: #ccc;
                    border-radius: 50%;
                    transition: background 0.3s ease;
                }

                &.completed {
                    color: #2ecc71;

                    &:before {
                        content: "✓";
                        background: #2ecc71;
                        color: white;
                        text-align: center;
                        line-height: 12px;
                        font-size: 8px;
                    }
                }

                &.active {
                    color: #2ecc71;

                    &:before {
                        content: "-";
                        background: #2ecc71;
                        color: white;
                        text-align: center;
                        line-height: 12px;
                        font-size: 8px;
                    }
                }

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }

    .next-steps-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        width: 100%;
        max-width: 800px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        h3 {
            font-family: $secondary-font;
            font-size: 1.25rem;
            font-weight: 600;
            color: #000;
            margin-bottom: 1rem;
        }

        p {
            font-family: $secondary-font;
            font-size: 1rem;
            line-height: 1.5;
            color: #666;
        }
    }
}

.website-scanning-summary-cell {
    display: flex;
    align-items: center;
    gap: 4px;
    position: relative;


    .summary-content {
        flex: 1;
        font-family: $secondary-font;
    }

    .website-scanning-edit-button {
        opacity: 0;
        visibility: hidden;
        border: none;
        background: none;
        cursor: pointer;
        padding: 2px 6px;
        transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
        text-decoration: none;
    }

    &:hover .website-scanning-edit-button {
        opacity: 1;
        visibility: visible;
        text-decoration: none;
    }
}