import { useEffect } from "react";
import { useNavigate, useRouteError } from "react-router-dom";
import GenericButton from '../components/GenericButton/GenericButton';
import SuccessNavbar from "../components/SuccessNavbar/SuccessNavbar";
import { pageURL } from "./routes";

import './errorPage.min.css';

interface ReactRouterError {
    message: string
}

export default function AppSumoErrorPage() {
    const navigate = useNavigate();

    const error: ReactRouterError = useRouteError() as ReactRouterError;
    console.log(`################## ${error} #################`);

    useEffect(() => {
        document.title = "Processing... | Abun";

        // Set up auto-refresh every 5 seconds
        // const intervalId = setInterval(() => {
        //     window.location.reload();
        // }, 5000);

        // Clean up the interval on component unmount
        // return () => clearInterval(intervalId);
    }, []);

    const RedirectCreateArticle = () => {
        navigate(pageURL['createArticle'])
    }

    return (
        <section className={"error-page"}>
            <SuccessNavbar />
            <div className={"card px-6 has-text-centered"}>
                <div className={"error-icon"}>🚀</div>
                <h1 className={"is-size-3 has-text-weight-bold"}>Just a moment...</h1>
                <p className={"has-text-centered"}>
                    Your request is processing! Please stay on this page or tap the button below.
                    <br />
                    If anything seems off, ping us on live chat.
                </p>
                <GenericButton
                    text={"Proceed"}
                    type={"secondary"}
                    width={"219px"}
                    height={"40px"}
                    left={"7px"}
                    outlined={true}
                    additionalClassList={["is-small", "more-rounded-borders"]}
                    clickHandler={RedirectCreateArticle}
                    style={{ fontSize: "1rem", backgroundColor: "#2E64FE", color: "white", marginTop: "10px" }}
                />
            </div>
        </section>
    );
}
