@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/elements/container";
@import 'bulma/sass/form/_all';
@import "bulma/sass/components/tabs";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";

@import "../../assets/bulma-overrides/bulmaOverrides";

.content-schedule {
    width: 100%;
    font-family: $primary-font;

    .content-header {
        display: flex;
        justify-content: space-between;

        @media(max-width:767px) {
            flex-direction: column;
        }
    }

    h2 {
        font-family: $primary-font !important;
        font-size: 2rem !important;
        font-weight: 600;
        margin-bottom: 20px;
        text-align: center;
        margin-left: 30px;

        @media(max-width:767px) {
            margin-bottom: 30px;
        }
    }

    .container {
        display: flex;
        flex: 1;
        gap: 1.5rem;
    }

    .content-container {
        display: flex;

        @media(max-width:1200px) {
            flex-direction: column;
            gap: 5px;
        }

        .sidebar {
            width: 320px;
            background: #ffffff;
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid #d1d5db;

            @media(max-width:1200px) {
                width: 100%;
                margin-bottom: 10px;
            }

            h3 {
                margin-bottom: 1rem;
                color: #333;
                font-family: $secondary-font;
            }

            .filters {
                display: flex;
                justify-content: space-between;
                margin-bottom: 1rem;

                button {
                    flex: 1;
                    padding: 10px;
                    background: #007bff;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    cursor: pointer;
                    transition: background 0.3s ease;

                    &:hover {
                        background: #0056b3;
                    }

                    &:not(:last-child) {
                        margin-right: 5px;
                    }
                }
            }

            .article {
                background: rgb(239, 246, 255);
                padding: 10px;
                border-radius: 8px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                cursor: grab;
                color: rgba(30, 64, 175, 1);

                &.dragging {
                    opacity: 0.5;
                }

                .article-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                }

                span {
                    flex: 1;
                    font-family: $secondary-font;
                }

                button {
                    background: transparent;
                    border: none;
                    cursor: pointer;
                    font-size: 18px;
                    color: #dc3545;

                    &:hover {
                        color: #b02a37;
                    }
                }

                .article-info {
                    margin-top: 4px;
                    display: flex;
                    align-items: flex-start;
                    gap: 8px;
                }

                .article-date,
                .article-status {
                    font-size: 14px;
                    color: gray;
                }

                .icon {
                    color: gray;
                }

                .icon.scheduled {
                    color: green;
                }

                .icon.draft {
                    color: orange;
                }

            }
        }

        .calendar-container {
            flex: 2;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;

            .fc {
                height: 100% !important;
                overflow: hidden;

                @media(max-width:767px) {
                    height: 100vh;
                    width: 100%;
                }

            }

            .fc-scrollgrid {
                height: 100% !important;
                background: white !important;
            }

            .fc-timeDropdown-button {
                background: none !important;
                border: none !important;
                box-shadow: none !important;
                padding: 0 !important;
            }

            .fc-toolbar {
                display: flex;
                justify-content: space-between;
                align-items: center;
                gap: 5px;
                flex-wrap: nowrap;
            }

            .fc-toolbar-chunk {
                display: flex;
                align-items: center;
            }

            .fc-button {
                padding: 5px 10px;
                font-size: 14px;
                background: none !important;
                box-shadow: none !important;
                color: #333;
            }

            .fc-today-button {
                display: none !important;
                height: 35px;
                padding: 6px 12px;
                font-size: 14px;
                border-radius: 6px;
                cursor: pointer;
                color: #333;
            }
            .fc-dayGridMonth-view {
                height: 100% !important;

                table tbody {
                    .fc-scroller-harness, .fc-scroller, .fc-daygrid-body, table {
                        height: 100% !important;
                    } 
                }
            }

            .fc-dayGridMonth-button {
                font-family: $primary-font !important;
                height: 35px;
                padding: 6px 12px;
                font-size: 14px;
                border-radius: 6px;
                cursor: pointer;
                color: #333;
            }

            .fc-toolbar-title {
                padding-right: 12px;
            }

            .fc-daygrid-event:focus,
            .fc-daygrid-event:hover {
                box-shadow: none !important;
                outline: none !important;
                border: none !important;
                background: inherit !important;
            }

            .fc-daygrid-day-number {
                font-size: 18px !important;
            }

            .fc-daygrid-day.past {
                background-color: #D9D9D9 !important;
            }

            .fc-daygrid-day.past .fc-daygrid-day-number {
                font-size: 18px !important;
                color: #ffffff !important;
            }

            .fc-more-link {
                background: transparent !important;
                box-shadow: none !important;
            }

            .fc-more-link:hover {
                background: transparent !important;
            }

        }
    }

    .card-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: .75rem;
        padding-bottom: 0;

        .radio-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;

            .form-control-label {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 1rem;
            }

            .text-muted-foreground {
                font-size: 0.875rem;
                color: #6c757d;
            }
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;

            .form-group {
                display: flex;
                flex-direction: column;
                gap: 0.25rem;

                label {
                    font-size: 0.875rem;
                    font-weight: 500;
                }

                .form-control {
                    padding: 0.5rem;
                    border: 1px solid #ced4da;
                    border-radius: 0.25rem;
                    font-size: 1rem;
                }
            }
        }

        .space-y-2 {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;

            label {
                font-size: 0.875rem;
                font-weight: 500;
            }

            .form-control {
                padding: 0.5rem;
                border: 1px solid #ced4da;
                border-radius: 0.25rem;
                font-size: 1rem;
            }
        }
    }

    .article-card {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 12px 16px;
        margin-bottom: 10px;
        cursor: pointer;
        transition: box-shadow 0.2s ease;
        display: flex;
        justify-content: space-between;

        &:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .article-title {
            font-weight: 600;
            font-size: 16px;
            color: #333333;
        }
    }
}

.custom-event {
    box-shadow: none !important;
    overflow: auto;
    scrollbar-width: none;

    .event-title-box {
        background: rgb(239, 246, 255);
        color: rgba(30, 64, 175, 1);
        font-size: 12px;
        font-weight: bold;
        padding: 6px 10px;
        border-radius: 6px;
        display: inline-block;
        max-width: 160px;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        box-sizing: border-box;
        box-shadow: none !important;
        outline: none !important;
        border: none !important;
        font-family: $secondary-font;

        @media(max-width:1800px) {
            white-space: nowrap;
            width: fit-content;
        }

        @media(min-width:2000px) {
            white-space: normal;
            width: fit-content;
            max-width: 250px;
        }
    }

    .event-title-box:hover {
        box-shadow: none !important;
        outline: none !important;
        border: none !important;
    }

    .event-title-box.published:hover {
        cursor: pointer;
    }

    .event-title-box:not(.published):focus,
    .event-title-box:not(.published):hover {
        cursor: grab;
    }

    .event-title-box:hover .delete-event-btn {
       display: flex;
    }

    .event-time {
        font-size: 12px;
        padding: 3px 6px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        gap: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        background: orange;
        color: white;
        width: fit-content;
        font-family: $secondary-font;
    }

    .event-time.published {
        background-color: rgba(30, 64, 175, 1);
        width: fit-content;
    }


    .event-time .icon {
        color: white;
    }

    .delete-event-btn {
        position: absolute;
        top: 2px;
        right: 0px;
        background: #f87171;
        color: white;
        border: none;
        width: 14px;
        height: 14px;
        font-size: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        outline: none;
        box-shadow: none;
        display: none;
        font-family: $secondary-font;
    }

    .delete-event-btn:hover {
        background: #dc2626;
        box-shadow: none;
    }

    .delete-event-btn:focus {
        outline: none;
        box-shadow: none;
    }
}