import { Link, useLoaderData } from "react-router-dom";
import Success<PERSON><PERSON><PERSON> from "../../components/SuccessNavbar/SuccessNavbar";
import { pageURL } from "../routes";

import './GoogleSuccess.min.css';

interface serverResponse {
    success: boolean
    message: string
}

export default function GoogleSuccess() {
    const pageData: serverResponse = useLoaderData() as serverResponse;

    function getIntegrationType() {
        let integrationType = localStorage.getItem('integration-type');

        if (integrationType) {
            // Split the string
            let integrationsTypeArray = integrationType.split("-");

            // Capitalize the 1st char of each word
            integrationsTypeArray = integrationsTypeArray.map((word) => {
                return word.charAt(0).toUpperCase() + word.slice(1);
            })

            // Join the array
            integrationType = integrationsTypeArray.join(" ");
        }

        return integrationType;
    }

    if (pageData.success) {
        const integrationType = getIntegrationType();

        return (
        <div className={"google-success-container"}>
                <SuccessNavbar/>
            <div className={"card card-contain"}>
                <div className={"check-icon"}>✅</div>
                <h2 className={"has-text-centered has-text-weight-bold"}>{integrationType ? integrationType : "Google"} Integration Successful!</h2>
                {/* <p className={"has-text-centered mt-3"}>
                        You can now start posting generated articles on your site through Abun individually<br/>
                        or by using our Auto Publish feature.
			    </p> */}
                <div className={"btns"}>
                    <Link to={pageURL['GSCAnalytics']} className={"button is-primary mt-5 "}>Proceed</Link>
                </div>
            </div>
        </div>
        )
    } else {
        return (
            <div className={"google-error-container"}>
                <div className={"card"}>
                    <div className={"card-content"}>
                        <div className={"content success-card-content"}>
                            <p className={"has-text-centered"}>Oops! Something went wrong :(</p>
                            <p className={"has-text-danger has-text-centered mt-4"}>{pageData.message}</p>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}
