import './NewAuthBase.min.css';

import { useNavigation } from "react-router-dom";
import ProgressBar from "../../components/ProgressBar/ProgressBar";
import TransitionOutlet from "../../components/TransitionOutlet/TransitionOutlet";

export default function NewAuthBase() {
	const navigation = useNavigation();

	return (
		<div className={"authbase-container"}>
			<ProgressBar show={navigation.state === "loading"} />
			{/* ----------------------- NAVBAR ----------------------- */}
			{/* <NewAuthPageNavbar /> */}

			{/* ----------------------- MAIN BODY ----------------------- */}
			<div className="container">
				<TransitionOutlet />
			</div>
		</div>
	)
}
