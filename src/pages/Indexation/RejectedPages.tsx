import { useQuery } from '@tanstack/react-query';
import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import { useEffect, useRef, useState } from "react";
import { useLoaderData } from "react-router-dom";
import AbunTable from "../../components/AbunTable/AbunTable";
import BookLoader from "../../components/BookLoader/BookLoader";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import Icon from "../../components/Icon/Icon";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { loadRejectedPagesQuery } from "../../utils/api";
import './Indexation.min.css';

type AllPages = {
    url: string,
    status: string,
    reason: string,
}

interface ServerData {
    user_verified: boolean
    has_active_website: boolean,
    integration_done: boolean,
    indexation_done: boolean,
    plan_name: string
}

export default function AllPages() {
    // ----------------------- NON STATE CONSTANTS -----------------------
    const pageSizes = [50, 100, 500, 1000];

    // ----------------------------- LOADER -----------------------------
    const pageData: ServerData = useLoaderData() as ServerData;

    // ----------------------- STATES -----------------------
    const [tableData, setTableData] = useState<Array<AllPages>>([]);
    const [searchText, setSearchText] = useState("");

    // ----------------------- EFFECTS -----------------------
    const { isLoading, error, data, refetch } = useQuery(loadRejectedPagesQuery());

    useEffect(() => {
        if (data) {
            setTableData((data as any)['data']['all_pages']);
        }
    }, [data]);

    useEffect(() => {
        if (window.location.search) {
            const searchParams = new URLSearchParams(window.location.search);
            const url = searchParams.get("url");
            setSearchText(url?.toLowerCase() || "");
        }
    }, []);

    // ----------------------- REFS -----------------------
    const successAlertRef = useRef<any>(null);
    const failAlertRef = useRef<any>(null);

    if (!pageData.indexation_done) {
        return (
            <BookLoader words={["posts", "articles", "blogs", "pages", "sitemap"]} />
        )
    } else if (isLoading) {
        return (
            <p style={{ textAlign: "center", fontSize: "1.3rem" }}>
                Loading Data...<Icon iconName={"spinner"} marginClass={"ml-5"} />
            </p>
        )
    } else {
        // ================== Generate table data and render AbunTable component ==================
        const columnHelper = createColumnHelper<AllPages>();

        const columnDefs: ColumnDef<any, any>[] = [
            columnHelper.accessor((row: AllPages) => row.url, {
                id: 'pageUrl',
                header: "URL",
                cell: info => info.getValue(),
                enableGlobalFilter: true,
                enableSorting: false,
            }),
            columnHelper.accessor((row: AllPages) => row.status, {
                id: 'status',
                header: "Status",
                cell: info => {
                    let displayText: string;
                    let btnColor: string;

                    // if (info.row.original.index){
                    displayText = 'Rejected';
                    btnColor = 'danger';
                    // }else

                    return <button
                        className={`button is-small more-rounded-borders ${`is-${btnColor}`} custom-btn`} disabled
                        style={{ cursor: 'pointer', opacity: "1.5", borderRadius: "4px" }}>{displayText}</button>
                }, meta: {
                    align: 'center',
                }
            }),
            columnHelper.accessor((row: AllPages) => row.reason, {
                id: 'reason',
                header: "Reason",
                cell: info => info.getValue(),
                meta: {
                    align: 'center',
                },
                enableGlobalFilter: true,
                enableSorting: false,
            }),
            // columnHelper.accessor((row: AllPages) => row.indexed_on, {
            //     id: 'indexed_on',
            //     header: "Indexed On",
            //     cell: info => {
            //         const value = info.getValue()
            //         if (value === null){
            //             return "---"
            //         }else{
            //             return value
            //         }                    
            //     },
            //     meta: {
            //         align: 'center',
            //     },
            //     enableGlobalFilter: true,
            //     enableSorting: false,
            // }),
        ]

        return (
            <>      <div className="mt-4">
                <div className="send-page">
                    <div className="content is-flex is-flex-direction-column">
                        <AbunTable
                            tableContentName={"Rejected Pages"}
                            id={"pages-sent-for-indexing"}
                            searchText={searchText}
                            tableData={tableData}
                            columnDefs={columnDefs}
                            pageSizes={pageSizes}
                            initialPageSize={pageSizes[1]}
                            enableSorting={true}
                            noDataText={"No Rejected Pages Found"}
                            searchboxPlaceholderText={"Search for Pages..."}
                        />
                    </div>
                    <div className="alerts">
                        <SuccessAlert ref={successAlertRef} />
                        <ErrorAlert ref={failAlertRef} />
                    </div>
                </div>
            </div>
            </>
        )
    }
}