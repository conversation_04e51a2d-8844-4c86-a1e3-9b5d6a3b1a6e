@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/elements/container";
@import "bulma/sass/elements/form";
@import "bulma/sass/components/tabs";

@import "../../assets/bulma-overrides/bulmaOverrides";

.custom-tabs {
    justify-content: space-evenly !important;
}

.custom-tabs li {
    width: 100% !important;
}

.alerts {
    width: 100%;
    display: flex;
    justify-content: center;
}

.indexation-report-container {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: space-around;

    .card {
        margin-left: 1.5rem;
        margin-bottom: 2rem;
        width: 17%;
        border-radius: 24px;
    }
    .card-content{
        padding: 1em;        
    }

    .usage-stats-item--title{
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e5e7eb;
        font-size: 1.2rem;

        @media (max-width: 2000px) {
            font-size: 1rem;
        }

        @media (max-width: 1646px) {
            font-size: 0.8rem;
        }
    }

    .card-number{
        margin-bottom: 0 !important;
    }

   .p-hour-text {
        font-size: 18px;
    }

        @media (min-width: 1541px) {
        .p-hour-text {
            font-size: 20px;
        }
    }

      .tooltip-question {
        display: inline-block;
        width: 20px;
        height: 20px;
        line-height: 20px;
        border-radius: 50%;
        background-color: #fff;
        color: #333;
        text-align: center;
        font-weight: bold;
        margin-left: 8px;
        font-size: 12px;
        border: 2px solid #ccc;
        cursor: pointer;
    }
}

.all-page, .abun-page {
    .abun-table-container th .sortable-column {
        display: flex !important;
        .sort-icon-container span{
            display: block;
        }
    }
}

.index-header {
        align-self: center;
        justify-self: center;
        font-family: $primary-font !important;
        font-size: 2rem !important;
        font-weight: 600;
    }

.index-subtext{
    color: rgba(0, 0, 0, .698);
    font-family: $secondary-font !important;
    font-size: 1.125rem !important;
    align-self: center;
    justify-self: center;
}