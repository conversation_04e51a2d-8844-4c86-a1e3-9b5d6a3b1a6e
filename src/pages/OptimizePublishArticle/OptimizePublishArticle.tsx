import { useMutation, useQuery } from "@tanstack/react-query";
import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import { useEffect, useRef, useState } from "react";
import { Helmet } from 'react-helmet';
import { useNavigate, useLoaderData } from 'react-router-dom';
import AbunLoader from '../../components/AbunLoader/AbunLoader';
import AbunModal from '../../components/AbunModal/AbunModal';
import AbunTable from "../../components/AbunTable/AbunTable";
import ErrorAlert from '../../components/ErrorAlert/ErrorAlert';
import LinkButton from "../../components/LinkButton/LinkButton";
import SuccessAlert from '../../components/SuccessAlert/SuccessAlert';
import { withAdminAndProductionCheck } from "../../utils/adminAndProductionCheck";
import { getPublishedArticle, getTaskProgress, retryFn, wpPublishedArticleMutation } from "../../utils/api";
import { PageData } from "../KeywordsResearchV2/KeywordResearch";
import { Tooltip } from 'react-tooltip';
import './OptimizePublishArticle.min.css';


export interface PublishedArticle {
  id: number;
  post_id: number;
  media_id: number | null;
  title: string;
  slug: string;
  url: string;
  published_date: string;
  gsc_position: string;
  article_uid: string;
  created_on: string;
  word_count: number | null;
  internal_link_count: number | null;
  external_link_count: number | null;
  post_link: string
  posted_on: string
}

const OptimizePublishedArticle = () => {
  const navigate = useNavigate()
  const pageSizes = [5, 10, 15, 30, 50, 100, 500];
  const { pageData } = useLoaderData() as { pageData: PageData };
  const successAlertRef = useRef<any>(null);
  const errorAlertRef = useRef<any>(null);
  const tableRef = useRef<{ refetchData: () => Promise<void> }>(null);

  const [tableData, setTableData] = useState<PublishedArticle[]>([]);
  const [requestModalActive, setRequestModalActive] = useState(false);
  const [modalText, setModalText] = useState("");

  // ----------------------- NON STATE CONSTANTS -----------------------
  const wpPublishedArticleFetch = useMutation(wpPublishedArticleMutation);

  const PublishedArticle = useQuery({
    queryKey: ['getPublishedArticle'],
    queryFn: getPublishedArticle,
    cacheTime: 0,
    refetchOnWindowFocus: false,
    retry: retryFn,
  });

  useEffect(() => {
    if (PublishedArticle.isSuccess && (PublishedArticle.data as any).data.articles?.length > 0) {
      setTableData((PublishedArticle.data as any).data.articles);  // Use data.articles from response
    }
  }, [PublishedArticle.isSuccess, PublishedArticle.data]);

  useEffect(() => {
    const articles = PublishedArticle.data?.data?.articles || [];
    const isDataFetched = PublishedArticle.isSuccess;
    const isTableEmpty = articles.length === 0;
    const canFetch = !wpPublishedArticleFetch.isLoading && !wpPublishedArticleFetch.isSuccess && !wpPublishedArticleFetch.isError;

    if (isDataFetched && isTableEmpty && canFetch) {
      setRequestModalActive(true);
      wpPublishedArticleFetch.mutate(undefined, {
        onSuccess: (data) => {
          const taskId = data?.data?.task_id;
          if (taskId) {
            pollTaskProgress(taskId);
          } else {
            setRequestModalActive(false);
            errorAlertRef.current?.show("Failed to get task ID.");
            setTimeout(() => errorAlertRef.current?.close(), 5000);
          }
        },
        onError: (error) => {
          setRequestModalActive(false);
          if (error?.response.data.err_id === 'NO_WP_INTEGRATION') {
            errorAlertRef.current?.show("No Wordpress integration found.")
          } else if (error?.response.data.err_id === 'NO_GSC_INTEGRATION') {
            errorAlertRef.current?.show("No GSC integration found.");
          } else {
            errorAlertRef.current?.show("Failed to start task.")
          }
          setTimeout(() => errorAlertRef.current?.close(), 5000);
        }
      });
    }
  }, [PublishedArticle.isSuccess, tableData]);

  function openInNewTab(post_link) {
    window.open(post_link, "_blank");
  }

  function goBack() {
    navigate('/create-article');
  }

  const pollTaskProgress = (taskId) => {
    const interval = setInterval(() => {
      getTaskProgress(taskId)
        .then((res) => {
          const status = res.data.status;


          if (status === "success") {
            clearInterval(interval);
            setRequestModalActive(false);
            successAlertRef.current?.show("Successfully fetched published article.");
            setTimeout(() => successAlertRef.current?.close(), 5000);

            tableRef.current?.refetchData();
          } else if (status === "failure" || status === "FAILURE") {
            clearInterval(interval);
            setRequestModalActive(false);
            errorAlertRef.current?.show("Task failed. Please try again.");
            setTimeout(() => errorAlertRef.current?.close(), 5000);
          }
        })
        .catch((err) => {
          clearInterval(interval);
          setRequestModalActive(false);
          errorAlertRef.current?.show("Error fetching task progress. Please try again.");
          setTimeout(() => errorAlertRef.current?.close(), 5000);
        });
    }, 2000); // Poll every 2 seconds
  };


  const columnHelper = createColumnHelper<PublishedArticle>();

  // Define columns
  const columnDefs: ColumnDef<PublishedArticle, any>[] = [
    columnHelper.accessor((row) => row.title, {
      id: 'articleTitle',
      header: "Article Title",
      cell: (props) => (
        <div onClick={() => openInNewTab(props.row.original.post_link)} style={{ cursor: "pointer" }}>
          <span>{props.row.original.title}<svg width="24" height="12" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M432 320H400a16 16 0 0 0 -16 16V448H64V128H208a16 16 0 0 0 16-16V80a16 16 0 0 0 -16-16H48A48 48 0 0 0 0 112V464a48 48 0 0 0 48 48H400a48 48 0 0 0 48-48V336A16 16 0 0 0 432 320zM488 0h-128c-21.4 0-32.1 25.9-17 41l35.7 35.7L135 320.4a24 24 0 0 0 0 34L157.7 377a24 24 0 0 0 34 0L435.3 133.3 471 169c15 15 41 4.5 41-17V24A24 24 0 0 0 488 0z" /></svg></span>
        </div>
      ),
      enableGlobalFilter: true,
      enableSorting: false,
    }),
    columnHelper.accessor((row) => row.word_count, {
      id: 'word_count',
      header: () => "Words",
      cell: (info) => {

        if (info.getValue() === null || info.getValue() === 0) {
          return <span data-nonclickable="true" style={{ cursor: "default" }}>---</span>;
        } else {
          return <span data-nonclickable="true" style={{ cursor: "default", color: "#000" }}>{info.getValue()}</span>;
        }
      },
      // enableGlobalFilter: false,
      meta: { align: 'center' }
    }),
    columnHelper.accessor((row) => row.internal_link_count, {
      id: 'internal_link_count',
      header: "Internal Links",
      cell: info => {
        const value = info.getValue();
        if (value === null) {
          return 0;
        }
        return value;
      },
      // enableGlobalFilter: true,
      meta: {
        align: 'center'
      }
    }),
    columnHelper.accessor((row) => row.external_link_count, {
      id: 'external_link_count',
      header: "External Links",
      cell: info => {
        const value = info.getValue();
        if (value === null) {
          return 0;
        }
        return value;
      },
      // enableGlobalFilter: true,
      meta: {
        align: 'center'
      }
    }),
    columnHelper.accessor((row) => row.created_on, {
      id: 'create_date',
      header: "Created On",
      cell: (props) => {
        const selectedDate = props.row.original.published_date
        if (!selectedDate) return <span data-nonclickable="true" style={{ cursor: "default" }}>---</span>;

        const getRelativeTime = (dateString: string) => {
          const createdDateObj = new Date(dateString);
          const now = new Date();
          const timeDiff = now.getTime() - createdDateObj.getTime();

          const seconds = Math.floor(Math.abs(timeDiff) / 1000);
          const minutes = Math.floor(seconds / 60);
          const hours = Math.floor(minutes / 60);
          const days = Math.floor(hours / 24);


          // PAST TIME
          if (seconds < 60) return "just now";
          if (minutes < 60) return minutes === 1 ? "a minute ago" : `${minutes} minutes ago`;
          if (hours < 24) return hours === 1 ? "an hour ago" : `${hours} hours ago`;
          if (days > 30) {
            const day = createdDateObj.getDate();
            const month = createdDateObj.toLocaleString('default', { month: 'short' });
            const year = createdDateObj.getFullYear().toString().slice(-2);
            return `${day} ${month}, ${year}`;
          }
          return days === 1 ? "a day ago" : `${days} days ago`;
        };

        return <span data-nonclickable="true" style={{ cursor: "default", color: "#000" }}>{getRelativeTime(selectedDate)}</span>;
      },

    }),
    columnHelper.accessor((row) => row.published_date, {
      id: 'published_date',
      header: "Updated On",
      cell: (props) => {
        const selectedDate = props.row.original.posted_on
        if (!selectedDate) return <span data-nonclickable="true" style={{ cursor: "default" }}>---</span>;

        const getRelativeTime = (dateString: string) => {
          const createdDateObj = new Date(dateString);
          const now = new Date();
          const timeDiff = now.getTime() - createdDateObj.getTime();

          const seconds = Math.floor(Math.abs(timeDiff) / 1000);
          const minutes = Math.floor(seconds / 60);
          const hours = Math.floor(minutes / 60);
          const days = Math.floor(hours / 24);


          // PAST TIME
          if (seconds < 60) return "just now";
          if (minutes < 60) return minutes === 1 ? "a minute ago" : `${minutes} minutes ago`;
          if (hours < 24) return hours === 1 ? "an hour ago" : `${hours} hours ago`;
          if (days > 30) {
            const day = createdDateObj.getDate();
            const month = createdDateObj.toLocaleString('default', { month: 'short' });
            const year = createdDateObj.getFullYear().toString().slice(-2);
            return `${day} ${month}, ${year}`;
          }
          return days === 1 ? "a day ago" : `${days} days ago`;
        };

        return <span data-nonclickable="true" style={{ cursor: "default", color: "#000" }}>{getRelativeTime(selectedDate)}</span>;
      },

    }),
    columnHelper.accessor((row) => row.gsc_position, {
      id: 'ranking_position',
      header: () => (
        <div data-tooltip-id="rankingTip" data-tooltip-content={pageData.has_gsc_integration
          ? "This data is pulled from your Google Search Console."
          : "This data is pulled from your Google Search Console. Connect your GSC from Integration."
        }>
          Ranking Position
          <Tooltip id="rankingTip" place="top" />
        </div>
      ),
      cell: (props) => {
        if (props.row.original.gsc_position) {
          return <span>{props.row.original.gsc_position}</span>
        } else {
          return <span>-</span>
        }
      },
      enableGlobalFilter: false,
    }),
    columnHelper.display({
      id: 'Action',
      header: "Action",
      cell: (props) => (
        <span><LinkButton linkTo={`/articles/edit/${props.row.original.article_uid}/`}
          text={"Open In Editor"}
          type={"success"}
          width={"100px"}
          outlined={true}
          additionalClassList={["is-small", "more-rounded-borders"]} />
        </span>
      ),
    }),
  ];

  if (requestModalActive) {
    return (
      <>
        <div className="card w-100" style={{ minHeight: '95vh' }}>
          <div className={"card-content"} style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '95vh',
          }}
          >
            <AbunLoader show={requestModalActive} height="50vh" />
          </div>
        </div>
      </>
    );
  }
  else {
    return (
      <>
        <div className="wp-pub-art-topic-card">
          <Helmet>
            <title>Optimize Published Article | Abun.com</title>
            <meta
              name="description"
              content="Improve existing content using AI for better ranking and freshness."
            />
          </Helmet>
          {/* <span className={"back-btn"} onClick={goBack}>
        <svg className="back-btn" width="30" height="24" viewBox="0 0 30 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M26.0435 12.0003H2.82031M2.82031 12.0003L12.8382 1.98242M2.82031 12.0003L12.8382 22.0181" stroke="black" stroke-opacity="0.5" stroke-width="3" />
        </svg>
      </span> */}
          <h1 className={"is-size-4 has-text-centered"} style={{ fontWeight: "800", fontSize: "2rem", fontFamily: "Bricolage Grotesque" }}>Optimize Your Published Blogs</h1>
          <p className={"is-size-6 mt-2 optimize-p"}>Pull in your published posts and improve their Google rankings with a click.</p>
          <div className={`table-container`}>
            <AbunTable
              ref={tableRef}
              serverSide={true}
              apiUrl="/api/frontend/get-all-published-article/"
              id="article-titles-table"
              tableContentName={"Wordpress Published Articles"}
              // tableData={tableData}
              columnDefs={columnDefs}
              pageSizes={pageSizes}
              initialPageSize={pageSizes[6]}
              enableSorting={true}
              noDataText={"No Projects Found."}
              searchboxPlaceholderText={"Search Optimize Published Articles..."}
              transformResponse={(rawData) => ({
                data: rawData.articles,
                total: rawData.total,
              })}

            />
          </div>

          <AbunModal active={requestModalActive}
            headerText={""}
            closeable={false}
            hideModal={() => {
              setRequestModalActive(false)
            }}>
            <div className={"loadingData w-100 is-flex is-justify-content-center is-align-items-center"}>
              <AbunLoader show={requestModalActive} height="20vh" />
            </div>
            <p className={"is-size-4 has-text-centered mb-4"}>{modalText}</p>
          </AbunModal>

        </div >
        <SuccessAlert ref={successAlertRef} />
        <ErrorAlert ref={errorAlertRef} />
      </>
    )
  }
}
// Export with admin check
export default withAdminAndProductionCheck(OptimizePublishedArticle);