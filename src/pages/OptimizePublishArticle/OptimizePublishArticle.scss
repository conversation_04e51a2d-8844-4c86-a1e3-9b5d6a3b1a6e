// Glossary.scss
@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/elements/container";
@import 'bulma/sass/form/_all';
@import "bulma/sass/components/tabs";

@import "../../assets/bulma-overrides/bulmaOverrides";

$primary-color: #00c853;
$border-color: #e0e0e0;
$text-color: #000000;

.wp-pub-art-topic-card {
  text-align: center;
  font-family: Arial, sans-serif;
  position: relative;

  h1{
       font-family: $primary-font !important;
       font-size: 2rem !important;
       font-weight: 600 !important;
       margin-bottom: 8px;
   }

   p {
       color: rgba(0,0,0, .698);
       font-family: $secondary-font !important;
       font-size: 1.125rem!important;
   }


  .back-btn {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    cursor: pointer;

    svg {
      @media (max-width: 480px) {
        width: 15px;
        height: 12px;
      }
    }
  }

  h2 {
    color: black;
    text-align: center;
    font-size: 1.5rem !important;
    font-weight: 500;
    display: inline-block;
  }

  .glossary-topic {
    color: $primary;
    text-align: center;
    font-size: 1.5rem !important;
    font-weight: 500;
  }

  .custom_button {
    padding: 10px 20px;
    font-size: 16px;
    color: #fff;
    border: 1px solid #ccc;
    border-radius: 20px;
    background-color: $primary;
    cursor: pointer;
    margin: 0;
    transition: background-color 0.2s ease, color 0.2s ease;
    margin-left: 10px;
  }

  .glossary-content-term {
    font-size: 24px;
    font-weight: 600;
    color: #000000;
    margin-bottom: 16px;
    font-family: $primary-font !important;
    cursor: pointer;
  }

  .count-glossary {
    font-size: 1rem !important;
    margin-top: 20px;
    margin-bottom: 10px;
    // font-weight: bold;
  }

  .create-content-btn {
    margin: auto;
    border-radius: 8px !important;
  }

  .table-container {
    // margin-top: 3rem;
    width: 96.5%;
    min-height: 20vh;


    .abun-table-no-data {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 30vh;
      font-size: 1.5rem;
      color: #9f9f9f;
    }

    .abun-table-responsive {
      overflow-x: auto;

      h1.abun-table-title {
        font-family: $primary-font;
        font-size: 1.5rem !important;
        font-weight: 400;
        line-height: 3rem !important;
        text-align: left;
        color: $primary;
      }

      tbody {
        color: #000;


        td:first-child {
          width: 50%;
          text-align: start !important;
        }

        td {
          text-align: center;
        }
      }

      thead {

        // first th
        th:first-child div {
          margin: 0px !important;
        }

        th div {
          // width: 100%;
          margin: auto;
        }
      }

    }
  }
}

  .opt-pub-container {
    font-family: $secondary-font;

    h2 {
      text-align: center;
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 8px;
      font-family: $primary-font;
    }
  
    .subtitle {
      color: #6b7280;
      margin-bottom: 24px;
      font-family: $secondary-font !important;
      font-size: 1.125rem !important;
      text-align: center;
    }
    
    .subtitle-setup{
      color: #6b7280;      
      font-size: 16px;
      font-family: $secondary-font;
    }

    .setup-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;      
      flex-wrap: wrap;
    }

    .setup-card {
      display: flex;
      gap: 40px;
      background: #fff;
      border: 1px solid #e5e7eb;
      border-radius: 12px;      
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }
  
    .setup-left {
      width: 50%;
      padding: 2rem;
  
      h3 {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 1.5rem;
      }
  
      .desc {
        font-size: 14px;
        color: #6b7280;
        margin-bottom: 24px;
      }
      .icon-work{
        display: flex;
        
        svg{
          width: 20px;
          height: fit-content;
          background-color: #eff6ff;
          border-radius: 10px;
          margin-top: 0.5rem;
        }

      div {
        margin-left: 0.5rem;
      }
    }

    .setup-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;      
      flex-wrap: wrap;
    }

    .setup-card {
      display: flex;
      gap: 40px;
      background: #fff;
      border: 1px solid #e5e7eb;
      border-radius: 12px;      
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }
  
  }
  .setup-left {
    width: 50%;
    padding: 2rem;

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 1.5rem;
    }

    .desc {
      font-size: 14px;
      color: #6b7280;
      margin-bottom: 24px;
    }

    .how-it-works {
      h4 {
        font-size: 16px;
        font-weight: 600;
      }

      ul {
        list-style: none;
        padding-left: 0;

        li {
          margin-bottom: 12px;
          line-height: 1.5;

          strong {
            color: #111827;
          }
        }
      }

      .benefits-box {
        margin-top: 24px;
        background: #eff6ff;
        border: 1px solid #dbeafe;
        border-radius: 8px;
        padding: 16px;
        color: #2563eb;

        svg {
          margin-top: 4px;
          margin-right: 4px;
        }

        h5 {
          font-weight: 600;
          margin-bottom: 8px;
        }

        ul {
          list-style: disc;
          padding-left: 20px;

        }
      }
    }
  }

  .setup-right {
    width: 50%;
    padding: 2rem;

    @media (max-width:1200px) {
      width: 100%;
      
    }

    .right-grey-box {
      background-color: rgb(243 243 243 / 45%);
      border: 1px solid rgb(243 243 243);
      padding: 1rem;
      border-radius: 10px;
    }

      h4 {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 16px;
      }
  
      .connection-box {
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin-bottom: 2rem;
      }
      .connection{
        display: flex;
        padding: 12px;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        background: #ffff;

      .opt-icons {
        background-color: #e6f6e6;
        margin-right: 0.5rem;
        border-radius: 30px;
        margin-top: 0.3rem;
        height: 25px;
      }

        svg{          
          margin-top: 2px;
        }
      }

    .inner-connection {
      flex-direction: column;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;


      .connection-title {
        font-weight: 500;
      }

      .connection-desc {
        font-size: 13px;
        color: #6b7280;
        font-family: $secondary-font;
      }

      .connect-btn {
        background: #111827;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        border: none;
        margin-top: 0.5rem;
      }

      .connect-btn.success {
        background-color: #2dce89;
        /* green */
        color: white;
        border: none;
      }

      .connect-btn.success:hover {
        background-color: #45a049;
      }

    }

    .note {
      font-size: 12px;
      color: #6b7280;
      margin-top: 16px;
    }
  }

  .footer {
    padding-bottom: 1rem;
    padding-right: 2rem;
    display: flex;
    justify-content: end;

    .continue-btn {
      padding: 10px 20px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      border: none;      

      &.enabled {
        background: #2dce89;
        color: #fff;
        cursor: pointer;
      }

      &.disabled {
        background: #d1d5db;
        color: #9ca3af;
        cursor: not-allowed;
      }
    }
  }

  .setup-footer-wrapper {
    width: 100%;
  }

  .setup-head {
    padding: 1rem;
    border-bottom: 2px solid rgb(233, 233, 233);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;

    @media (max-width:500px) {
      flex-direction: column;
      align-items: normal;
    }

    .two-connection {
      background-color: rgb(255 253 227 / 60%);
      color: rgb(255 165 0);
      padding: 0 2rem;
      text-align: center;
      border: 1px solid;
      border-radius: 12px;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 4px;
    }
  }

  .optimize-p{
    font-family: Inter, normal !important;
    font-size: 1.125rem !important;
  }

.bullet-point{
  font-family: $secondary-font;
}

.setup-left,
.setup-right {
  width: 100%;
  padding: 2rem;
}

@media (min-width: 768px) {
  .setup-left,
  .setup-right {
    width: 50%;
  }
}

@media screen and (min-width: 2000px) {
  .how-it-works h3{
    font-size: 20px !important; 
  }
  
  .how-it-works h4,
  .how-it-works h5,
  .how-it-works p.subtitle-setup,
  .how-it-works li {
    font-size: 18px !important;
;
  }
}

@media screen and (min-width: 3000px) {
  .how-it-works h3{
    font-size: 22px !important; 
  }
  
  .how-it-works h4,
  .how-it-works h5,
  .how-it-works p.subtitle-setup,
  .how-it-works li {
    font-size: 20px !important;
  }
}