import { useEffect, useRef } from "react";
import { Helmet } from 'react-helmet';
import { useLoaderData, useNavigate } from "react-router-dom";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import wordpressIconSuccess from '../../assets/images/wordpress-logo.webp';
import { withAdminAndProductionCheck } from "../../utils/adminAndProductionCheck";
import { PageData } from "../KeywordsResearchV2/KeywordResearch";
import { pageURL } from "../routes";
import "./OptimizePublishArticle.min.css";

const OptimizePublishArticleSteps = () => {
    const { pageData } = useLoaderData() as { pageData: PageData };
    const navigate = useNavigate();
    const failAlertRef = useRef(null);
    const successAlertRef = useRef(null);

    const isConnected = pageData.has_gsc_integration && pageData.has_wp_integration;

    useEffect(() => {
        if (isConnected) {
            navigate(pageURL["OptimizePublishedArticle"]);
        }
    }, [isConnected, navigate]);

    function handleBtn(tab) {
        navigate(`${pageURL['new-integration']}?integration=${tab}`);
    }

    return (
        <div className="opt-pub-container">
            <Helmet>
                <title>Optimize Published Article | Abun.com</title>
                <meta
                  name="description"
                  content="Improve existing content using AI for better ranking and freshness."
                />
            </Helmet>
            <h2>Optimize Published Articles</h2>
            <p className="subtitle">
                Boost your existing content's performance with AI-powered optimization based on Google Search Console data.
            </p>

            {!isConnected && (
                <div className="setup-card">
                    <div className="setup-footer-wrapper">
                        <div className="setup-head">
                            <div>
                                <h3>Setup Required</h3>
                                <p className="subtitle-setup">Connect your platforms to start optimizing your published content</p>
                            </div>
                            {/* <div className="two-connection">
                                {!pageData.has_gsc_integration && !pageData.has_wp_integration ? (
                                    <p>2 connections needed</p>
                                ) : (!pageData.has_gsc_integration || !pageData.has_wp_integration) ? (
                                    <p>1 connection needed</p>
                                ) : <p>connected</p>}
                            </div> */}
                        </div>
                        <div className="setup-wrapper">
                            <div className="setup-left">

                                <div className="how-it-works">
                                    <h3>How It Works</h3>
                                    <ul>
                                        <li>
                                            <div className="icon-work">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="-0.5 -0.5 16 16" fill="none" stroke="#2563eb" stroke-linecap="round" stroke-linejoin="round" id="Refresh--Streamline-Tabler" height="16" width="16"><path d="M12.5 6.875A5.0625 5.0625 0 0 0 2.8125 5.625M2.5 3.125v2.5h2.5" stroke-width="1"></path><path d="M2.5 8.125a5.0625 5.0625 0 0 0 9.6875 1.25m0.3125 2.5v-2.5h-2.5" stroke-width="1"></path></svg>
                                                <div>
                                                    <h4>Import Content</h4>
                                                    <p className="subtitle-setup">We’ll fetch your published articles from WordPress automatically</p>
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div className="icon-work">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="-0.5 -0.5 16 16" fill="none" stroke="#2563eb" stroke-linecap="round" stroke-linejoin="round" id="Search--Streamline-Tabler" height="20" width="25"><path d="M1.875 6.25a4.375 4.375 0 1 0 8.75 0 4.375 4.375 0 1 0 -8.75 0" stroke-width="1"></path><path d="m13.125 13.125 -3.75 -3.75" stroke-width="1"></path></svg>
                                                <div>
                                                    <h4>Analyze Performance</h4>
                                                    <p className="subtitle-setup">We’ll analyze your content using Google Search Console data</p>
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div className="icon-work">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="-0.5 -0.5 16 16" fill="none" stroke="#2563eb" stroke-linecap="round" stroke-linejoin="round" id="Chart-Histogram--Streamline-Tabler" height="20" width="25"><path d="M1.875 1.875v11.25h11.25" stroke-width="1"></path><path d="M12.5 11.25v1.875" stroke-width="1"></path><path d="M10 10v3.125" stroke-width="1"></path><path d="M7.5 8.125v5" stroke-width="1"></path><path d="M5 10v3.125" stroke-width="1"></path><path d="M1.875 6.875c3.75 0 3.125 -3.125 5.625 -3.125s1.875 3.125 5.625 3.125" stroke-width="1"></path></svg>
                                                <div>
                                                    <h4>Optimize & Improve</h4>
                                                    <p className="subtitle-setup">Get AI-powered recommendations to boost rankings and traffic</p>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>

                                    <div className="benefits-box">
                                        <div className="benefits-icon is-flex">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="-0.5 -0.5 16 16" fill="none" stroke="#2563eb" stroke-linecap="round" stroke-linejoin="round" id="Square-Rounded-Check--Streamline-Tabler" height="20" width="25"><path d="m5.625 7.5 1.25 1.25 2.5 -2.5" stroke-width="1"></path><path d="M7.5 1.875c4.5 0 5.625 1.125 5.625 5.625s-1.125 5.625 -5.625 5.625 -5.625 -1.125 -5.625 -5.625 1.125 -5.625 5.625 -5.625z" stroke-width="1"></path></svg>
                                            <h5>Benefits</h5>
                                        </div>
                                        <ul>
                                            <li className="bullet-point">Identify underperforming content that needs improvement</li>
                                            <li className="bullet-point">Discover keyword opportunities you're missing</li>
                                            <li className="bullet-point">Optimize content based on actual search performance</li>
                                            <li className="bullet-point">Track improvements in rankings over time</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div className="setup-right">
                                <div className="right-grey-box">
                                    <h4>Required Connections</h4>

                                    <div className="connection-box">
                                        <div className="connection">
                                            <div className="opt-icons">
                                                <img className={"image"} style={{height: "22.5px", width: "24px", marginTop:"0.1rem"}}src={wordpressIconSuccess} alt={"integration-icon"} />
                                            </div>
                                            <div className="inner-connection">
                                                <div>
                                                    <p className="connection-title">WordPress Website (Required)</p>
                                                    <p className="connection-desc">Connect to import your published blog posts</p>
                                                </div>
                                                <button className={`connect-btn ${pageData.has_wp_integration ? 'success' : ''}`} onClick={() => { handleBtn('WordPress') }}>{pageData.has_wp_integration ? 'WordPress Connected' : 'Connect WordPress'}</button>
                                            </div>
                                        </div>

                                        <div className="connection">
                                            <div className="opt-icons">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="-0.5 -0.5 16 16" fill="none" stroke="#2ba72b" stroke-linecap="round" stroke-linejoin="round" id="Search--Streamline-Tabler" height="20" width="25"><path d="M1.875 6.25a4.375 4.375 0 1 0 8.75 0 4.375 4.375 0 1 0 -8.75 0" stroke-width="1"></path><path d="m13.125 13.125 -3.75 -3.75" stroke-width="1"></path></svg>
                                            </div>
                                            <div className="inner-connection">
                                                <div>
                                                    <p className="connection-title">Google Search Console (Optional) (Can Connect Later as well)</p>
                                                    <p className="connection-desc">Connect to retrieve performance data for your articles</p>
                                                </div>
                                                <button className={`connect-btn ${pageData.has_gsc_integration ? 'success' : ''}`} onClick={() => { handleBtn("GSC") }}>{pageData.has_gsc_integration ? 'GSC Connected' : 'Connect GSC'}</button>

                                            </div>
                                        </div>
                                    </div>

                                    <p className="subtitle-setup">
                                        Important: <br />
                                        Ensure your WordPress site domain matches the GSC verified property domain to successfully pull position metrics.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="setup-footer-wrapper">
                            <div className="footer">
                            <button className={`continue-btn ${pageData.has_wp_integration ? 'enabled' : 'disabled'}`} disabled={!pageData.has_wp_integration} onClick={() => navigate(pageURL["OptimizePublishedArticle"])}>
                                Continue
                            </button>
                        </div>
                        </div>

                    </div>
                </div>
            )}


            <ErrorAlert ref={failAlertRef} />
            <SuccessAlert ref={successAlertRef} />
        </div>
    );
};

export default withAdminAndProductionCheck(OptimizePublishArticleSteps);