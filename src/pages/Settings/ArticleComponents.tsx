import FormControlLabel from '@mui/material/FormControlLabel';
import FormGroup from '@mui/material/FormGroup';
import { styled } from '@mui/material/styles';
import Switch, { SwitchProps } from '@mui/material/Switch';
import { useMutation, useQuery } from "@tanstack/react-query";
import { Dispatch, MutableRefObject, SetStateAction, useEffect, useState } from "react";
import Select from 'react-select';
import { Tooltip } from "react-tooltip";
import AbunButton from "../../components/AbunButton/AbunButton";
import AbunLoader from '../../components/AbunLoader/AbunLoader';
import AbunModal from "../../components/AbunModal/AbunModal";
import { saveSettingsMutation } from "../../utils/api";
import allSupportedLanguagesOptions from "../../utils/constants/allSupportedLanguagesOptions";

interface ArticleComponenetsProps {
    errorAlertRef: MutableRefObject<any>
    successAlertRef: MutableRefObject<any>
    toggle_toc: boolean
    toggle_faq: boolean,
    toggle_bullet_points: boolean,
    toggle_meta_description: boolean,
    toggle_table: boolean,
    toggle_tldr: boolean,
    updatePageData?: () => void
    setUnsavedChanges?: Dispatch<SetStateAction<boolean>>
}

interface CustomizedSwitchProps {
    toggleCheck: boolean;
    handleToggle: () => void;
}


export default function ArticleComponents(props: ArticleComponenetsProps) {

    const [toggleToc, setToggleToc] = useState(props.toggle_toc)
    const [toggleFaq, setToggleFaq] = useState(props.toggle_faq)
    const [toggleTable, setToggleTable] = useState(props.toggle_table)
    const [toggleBulletPoint, setToggleBulletPoint] = useState(props.toggle_bullet_points)
    const [toggleMetaDes, setToggleMetaDes] = useState(props.toggle_meta_description)
    const [toggleTldr, setToggleTldr] = useState(props.toggle_tldr)

    // Sync local state with props when they change (e.g., when switching tabs)
    useEffect(() => {
        setToggleToc(props.toggle_toc);
    }, [props.toggle_toc]);

    useEffect(() => {
        setToggleFaq(props.toggle_faq);
    }, [props.toggle_faq]);

    useEffect(() => {
        setToggleTable(props.toggle_table);
    }, [props.toggle_table]);

    useEffect(() => {
        setToggleBulletPoint(props.toggle_bullet_points);
    }, [props.toggle_bullet_points]);

    useEffect(() => {
        setToggleMetaDes(props.toggle_meta_description);
    }, [props.toggle_meta_description]);

    useEffect(() => {
        setToggleTldr(props.toggle_tldr);
    }, [props.toggle_tldr]);

    const saveSettings = useMutation(saveSettingsMutation);

    function handleToggleCheck(toggleName: string) {
        let value = false;

        switch (toggleName) {
            case "toggle_toc":
                setToggleToc(prev => {
                    value = !prev;
                    return value;
                });
                break;
            case "toggle_table":
                setToggleTable(prev => {
                    value = !prev;
                    return value;
                });
                break;
            case "toggle_meta_description":
                setToggleMetaDes(prev => {
                    value = !prev;
                    return value;
                });
                break;
            case "toggle_faq":
                setToggleFaq(prev => {
                    value = !prev;
                    return value;
                });
                break;
            case "toggle_tldr":
                setToggleTldr(prev => {
                    value = !prev;
                    return value;
                });
                break;
            case "toggle_bullet_points":
                setToggleBulletPoint(prev => {
                    value = !prev;
                    return value;
                });
                break;
        }


        // Trigger save settings immediately
        saveSettings.mutate({
            settingsToSave: [
                { settingName: toggleName, settingValue: value }
            ]
        }, {
            onSuccess: () => {
                props.updatePageData?.();
                props.setUnsavedChanges?.(false);
                props.successAlertRef.current.show("Changes Saved!");
                setTimeout(() => {
                    try {
                        if (props.successAlertRef.current) {
                            props.successAlertRef.current.close();
                        }
                    } catch (e) { }
                }, 3000);
            },
            onError: () => {
                props.errorAlertRef.current?.show("Oops! Something went wrong :( Please try again later or contact us for further support.");
            }
        });
    }


    function CustomizedSwitch({ toggleCheck, handleToggle }: CustomizedSwitchProps) {
        const IOSSwitch = styled((props: SwitchProps) => (
            <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
        ))(({ theme }) => ({
            width: 42,
            height: 26,
            padding: 0,
            '& .MuiSwitch-switchBase': {
                padding: 0,
                margin: 2,
                transitionDuration: '300ms',
                '&.Mui-checked': {
                    transform: 'translateX(16px)',
                    color: '#fff',
                    '& + .MuiSwitch-track': {
                        backgroundColor: '#65C466',
                        opacity: 1,
                        border: 0,
                        ...theme.applyStyles('dark', {
                            backgroundColor: '#2ECA45',
                        }),
                    },
                    '&.Mui-disabled + .MuiSwitch-track': {
                        opacity: 0.5,
                    },
                },
                '&.Mui-focusVisible .MuiSwitch-thumb': {
                    color: '#33cf4d',
                    border: '6px solid #fff',
                },
                '&.Mui-disabled .MuiSwitch-thumb': {
                    color: theme.palette.grey[100],
                    ...theme.applyStyles('dark', {
                        color: theme.palette.grey[600],
                    }),
                },
                '&.Mui-disabled + .MuiSwitch-track': {
                    opacity: 0.7,
                    ...theme.applyStyles('dark', {
                        opacity: 0.3,
                    }),
                },
            },
            '& .MuiSwitch-thumb': {
                boxSizing: 'border-box',
                width: 22,
                height: 22,
            },
            '& .MuiSwitch-track': {
                borderRadius: 26 / 2,
                backgroundColor: '#E9E9EA',
                opacity: 1,
                transition: theme.transitions.create(['background-color'], {
                    duration: 500,
                }),
                ...theme.applyStyles('dark', {
                    backgroundColor: '#39393D',
                }),
            },
        }));

        return (
            <FormGroup sx={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
                <FormControlLabel
                    control={<IOSSwitch sx={{ m: 1 }} defaultChecked={toggleCheck}
                        onChange={handleToggle} />}
                    label=""
                />
            </FormGroup>
        );
    }

    return (
        <div className="settings-section article-components">
            <div className="card-content p-0">
                <h5 className="settings-section-title">Article Components</h5>
                <p className="sub-title">Choose which elements to include in your generated articles.</p>
                <div className="article-grid">
                    <div className="article-item">
                        <div>
                            <h4 className='article-component-heading'>Table of Contents</h4>
                            <p className='article-component-subtitle'>An auto-generated navigation section at the beginning of the article.</p>
                        </div>
                        <CustomizedSwitch toggleCheck={toggleToc} handleToggle={() => handleToggleCheck("toggle_toc")} />
                    </div>
                    <div className="article-item">
                        <div>
                            <h4 className='article-component-heading'>FAQ Section</h4>
                            <p className='article-component-subtitle'>Common question and answer related to the article topic.</p>
                        </div>
                        <CustomizedSwitch toggleCheck={toggleFaq} handleToggle={() => handleToggleCheck("toggle_faq")} />
                    </div>
                    <div className="article-item">
                        <div>
                            <h4 className='article-component-heading'>Tables</h4>
                            <p className='article-component-subtitle'>Data organized in rows and columns where appropriate</p>
                        </div>
                        <CustomizedSwitch toggleCheck={toggleTable} handleToggle={() => handleToggleCheck("toggle_table")} />
                    </div>
                    <div className="article-item">
                        <div>
                            <h4 className='article-component-heading'>TLDR (Summary)</h4>
                            <p className='article-component-subtitle'>A Brief summary of the article's key points.</p>
                        </div>
                        <CustomizedSwitch toggleCheck={toggleTldr} handleToggle={() => handleToggleCheck("toggle_tldr")} />
                    </div>
                    <div className="article-item">
                        <div>
                            <h4 className='article-component-heading'>Bullet Points</h4>
                            <p className='article-component-subtitle'>List to break down information into digestible points</p>
                        </div>
                        <CustomizedSwitch toggleCheck={toggleBulletPoint} handleToggle={() => handleToggleCheck("toggle_bullet_points")} />
                    </div>
                    <div className="article-item">
                        <div>
                            <h4 className='article-component-heading'>Meta Description</h4>
                            <p className='article-component-subtitle'>SEO-optimized description for search engines</p>
                        </div>
                        <CustomizedSwitch toggleCheck={toggleMetaDes} handleToggle={() => handleToggleCheck("toggle_meta_description")} />
                    </div>
                </div>
            </div>
        </div>
    )
}
