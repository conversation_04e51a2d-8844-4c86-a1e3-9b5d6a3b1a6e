/* eslint-disable jsx-a11y/anchor-is-valid */
import { lazy, Suspense, useEffect, useRef, useState } from "react";
import { useRevalidator, useSearchParams } from "react-router-dom";
import AbunButton from "../../components/AbunButton/AbunButton";
import AbunLoader from "../../components/AbunLoader/AbunLoader";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
// Import types directly to avoid issues with lazy loading
import { Helmet } from 'react-helmet';
import { FeatureImageTemplates, ImageSource, KeywordStrategies } from "./ImageSettings";
import './Settings.min.css';


// Lazy loaded components
const ArticleDetails = lazy(() => import("./ArticleDetails"));
const Backlinks = lazy(() => import("./BacklinksPreference"));
const Competitors = lazy(() => import("./Competitors"));
const ImageSettings = lazy(() => import("./ImageSettings"));
const ToneOfArticle = lazy(() => import("./ToneOfArticle"));
const WebsiteDetails = lazy(() => import("./WebsiteDetails"));
const ArticleComponents = lazy(() => import("./ArticleComponents"));

type Tab = 'images' | 'integration' | 'websiteDetails' | 'competitors' | 'articleDetails' | 'backlinks' | 'toneOfArticle' | 'ArticleComponents';

export interface ServerData {
	service: string
	keyword_strategy: KeywordStrategies
	image_source: ImageSource
	all_integrations: Array<string>
	google_search_console_integration: boolean
	google_analytics_integration: boolean
	google_drive_integration: boolean
	has_auto_publish_list: boolean
	website_title: string
	website_description: string
	website_industry: string
	website_icp: string
	username: string
	email: string
	tz: string
	website_domain: string
	competitor_domains: Array<string>
	competitor_edit_underway: boolean
	feature_image_templates: Array<FeatureImageTemplates>
	selected_template: string
	article_tone_of_voice: string
	external_backlinks_preference: "no-follow" | "follow" | "off"
	article_language_preference: string
	max_internal_backlinks: number
	max_external_backlinks: number
	internal_glossary_backlinks_preference: "on" | "off"
	max_internal_glossary_backlinks: number
	current_plan_name: string
	images_file_format: string
	feature_image_required: boolean
	website_connection_limit: number
	article_context: string
	tone_of_article: string
	scale_of_tone: number
	show_logo_on_featured_image: boolean
	toggle_toc: boolean
	toggle_faq: boolean,
	toggle_bullet_points: boolean,
	toggle_meta_description: boolean,
	toggle_table: boolean,
	toggle_tldr: boolean,
}

interface ArticleSettingProps {
	pageData: ServerData;
}

export default function Settings({ pageData }: ArticleSettingProps) {
	// ------------------------- QUERY PARAMETERS -----------------------
	const [searchParams] = useSearchParams();
	const tab = searchParams.get("tab");

	// ----------------------------- LOADER -----------------------------
	// const pageData: ServerData = useLoaderData() as ServerData;
	const { revalidate } = useRevalidator();
	const { feature_image_templates } = pageData;

	// ---------------------------- STATES ---------------------------
	const [currentTab, setCurrentTab] = useState<Tab | string>(
		tab || 'images'
	);

	const [
		selectedTemplate,
		setSelectedTemplate
	] = useState<string | null>(null);

	const [
		featuredImageIsEnable,
		setFeaturedImageIsEnable
	] = useState<boolean | undefined>();

	const [
		featuredImageTemplates,
		setFeaturedImageTemplates
	] = useState<Array<FeatureImageTemplates>>([]);

	const [tabChangeWarningActive, setTabChangeWarningActive] = useState(false);

	const [tabToChangeTo, setTabToChangeTo] = useState<Tab | string>("");

	const [unsavedChanges, setUnsavedChanges] = useState(false);

	// ---------------------------- REFS ----------------------------
	const errorAlertRef = useRef<any>(null);
	const successAlertRef = useRef<any>(null);

	// ---------------------------- EFFECTS ---------------------------
	useEffect(() => {
		if (pageData) {
			setSelectedTemplate(pageData.selected_template);
			setFeaturedImageIsEnable(pageData.feature_image_required);
		}
	}, [pageData]);

	useEffect(() => {
		setFeaturedImageTemplates(feature_image_templates);
	}, [feature_image_templates]);

	useEffect(() => {
		if (unsavedChanges) {
			// SAVE state to local storage
			localStorage.setItem("unsavedSettings", "true");
			// prevent user from leaving the page
		} else {
			// REMOVE state from local storage
			localStorage.removeItem("unsavedSettings");
		}
	}, [unsavedChanges]);
	// ---------------------------- FUNCTIONS ---------------------------
	function updatePageData() {
		revalidate();
	}

	// prevent user from leaving the page if they have unsaved changes
	window.onbeforeunload = function () {
		if (unsavedChanges) {
			return "You have unsaved changes. Are you sure you want to leave this page?";
		}
	}

	// Loading fallback component
	const LoadingFallback = () => (
		<div className="loadingData w-100 is-flex is-justify-content-center is-align-items-center">
			<AbunLoader show={true} height="20vh" />
		</div>
	);

	// =====================================================================
	// ----------------------------- MAIN JSX -----------------------------
	// =====================================================================
	const featuredImageTabContent = (
		<Suspense fallback={<LoadingFallback />}>
			<ImageSettings
				errorAlertRef={errorAlertRef}
				successAlertRef={successAlertRef}
				featuredImageTemplates={featuredImageTemplates}
				selectedTemplate={selectedTemplate}
				imagesFileFormat={pageData.images_file_format}
				keywordStrategy={pageData.keyword_strategy}
				imageSource={pageData.image_source}
				featuredImageIsEnable={featuredImageIsEnable}
				currentPlanName={pageData.current_plan_name}
				setFeaturedImageIsEnable={setFeaturedImageIsEnable}
				setSelectedTemplate={setSelectedTemplate}
				setFeaturedImageTemplates={setFeaturedImageTemplates}
				updatePageData={updatePageData}
				setUnsavedChanges={setUnsavedChanges}
				show_logo_on_featured_image={pageData.show_logo_on_featured_image}
			/>
		</Suspense>
	)

	function renderTab() {
		switch (currentTab) {
			case "images":
				return featuredImageTabContent;
			// case "integration":
			// 	return (
			// 		<Suspense fallback={<LoadingFallback />}>
			// 			<Integration
			// 				errorAlertRef={errorAlertRef}
			// 				successAlertRef={successAlertRef}
			// 				allIntegrations={pageData.all_integrations}
			// 				googleSearchConsoleIntegration={pageData.google_search_console_integration}
			// 				googleAnalyticsIntegration={pageData.google_analytics_integration}
			// 				googleDriveIntegration={pageData.google_drive_integration}
			// 				websiteConnectionLimit={pageData.website_connection_limit}
			// 				updatePageData={updatePageData}
			// 				currentPlanName={pageData.current_plan_name}
			// 			/>
			// 		</Suspense>
			// 	);

			case "websiteDetails":
				return (
					<Suspense fallback={<LoadingFallback />}>
						<WebsiteDetails
							errorAlertRef={errorAlertRef}
							successAlertRef={successAlertRef}
							siteTitle={pageData.website_title}
							siteDescription={pageData.website_description}
							siteIndustry={pageData.website_industry}
							siteICP={pageData.website_icp}
							setUnsavedChanges={setUnsavedChanges}
						/>
					</Suspense>
				);

			case "competitors":
				return (
					<Suspense fallback={<LoadingFallback />}>
						<Competitors
							successAlertRef={successAlertRef}
							errorAlertRef={errorAlertRef}
							domain={pageData.website_domain}
						/>
					</Suspense>
				);

			case "articleDetails":
				return (
					<Suspense fallback={<LoadingFallback />}>
						<>
							<ArticleDetails
								errorAlertRef={errorAlertRef}
								successAlertRef={successAlertRef}
								article_tone_of_voice={pageData.article_tone_of_voice}
								languagePreference={pageData.article_language_preference}
								articleContext={pageData.article_context}
								updatePageData={updatePageData}
								setUnsavedChanges={setUnsavedChanges}
							/>
							<ToneOfArticle
								errorAlertRef={errorAlertRef}
								successAlertRef={successAlertRef}
								toneOfArticle={pageData.tone_of_article}
								scaleOfTone={pageData.scale_of_tone}
								updatePageData={updatePageData}
								setUnsavedChanges={setUnsavedChanges}
							/>
						</>
					</Suspense>
				);

			case "backlinks":
				return (
					<Suspense fallback={<LoadingFallback />}>
						<Backlinks
							errorAlertRef={errorAlertRef}
							successAlertRef={successAlertRef}
							external_backlinks_preference={pageData.external_backlinks_preference}
							max_internal_backlinks={pageData.max_internal_backlinks}
							max_external_backlinks={pageData.max_external_backlinks}
							internal_glossary_backlinks_preference={pageData.internal_glossary_backlinks_preference}
							max_internal_glossary_backlinks={pageData.max_internal_glossary_backlinks}
							updatePageData={updatePageData}
							setUnsavedChanges={setUnsavedChanges}
						/>
					</Suspense>
				);
			case "ArticleComponents":
				return (
					<Suspense fallback={<LoadingFallback />}>
						<ArticleComponents
							errorAlertRef={errorAlertRef}
							successAlertRef={successAlertRef}
							toggle_toc={pageData.toggle_toc}
							toggle_faq={pageData.toggle_faq}
							toggle_bullet_points={pageData.toggle_bullet_points}
							toggle_meta_description={pageData.toggle_meta_description}
							toggle_table={pageData.toggle_table}
							toggle_tldr={pageData.toggle_tldr}
							updatePageData={updatePageData}
							setUnsavedChanges={setUnsavedChanges}
						/>
					</Suspense>
				);
			// case "toneOfArticle":
			// 	return (
			// 		<Suspense fallback={<LoadingFallback />}>
			// 			<ToneOfArticle
			// 				errorAlertRef={errorAlertRef}
			// 				successAlertRef={successAlertRef}
			// 				toneOfArticle={pageData.tone_of_article}
			// 				scaleOfTone={pageData.scale_of_tone}
			// 				updatePageData={updatePageData}
			// 				setUnsavedChanges={setUnsavedChanges}
			// 			/>
			// 		</Suspense>
			// 	);
			default:
				return featuredImageTabContent;
		}
	}

	function ChangeCurrentTab(tab: Tab) {
		// warn user if they have unsaved changes
		setTabToChangeTo(tab);
		if (unsavedChanges) {
			setTabChangeWarningActive(true);
		} else {
			setCurrentTab(tab);
		}
	}

	return (
		<>
			<div className="tabs w-100" style={{ scrollbarWidth: 'none' }}>
				<Helmet>
					<title>Article Settings | Abun.com</title>
					<meta
						name="description"
						content="Manage article output settings like tone, language, and structure."
					/>
				</Helmet>
				<ul>
					{/* Feature Image */}
					<li className={currentTab === 'images' ? "is-active" : ""} id="images-tab"
						onClick={() => ChangeCurrentTab("images")}>
						<a>Images</a>
					</li>
					{/* Content Scheduling */}
					{/* <li className={currentTab === 'integration' ? "is-active" : ""} id="integration-tab"
							onClick={() => ChangeCurrentTab("integration")}>
						<a>Integration</a>
					</li> */}
					{/* Website Details */}
					{/* <li className={currentTab === 'websiteDetails' ? "is-active" : ""}
							onClick={() => ChangeCurrentTab("websiteDetails")}>
						<a>Website Details</a>
					</li> */}
					{/* Competitors
					<li className={currentTab === 'competitors' ? "is-active" : ""}
							onClick={() => ChangeCurrentTab("competitors")}>
						<a>Competitors</a>
					</li> */}
					{/* Tone of Voice */}
					<li className={currentTab === 'articleDetails' ? "is-active" : ""}
						onClick={() => ChangeCurrentTab("articleDetails")}>
						<a>Content</a>  {/* textual Changing article details to article language */}
					</li>
					{/* Backlinks */}
					<li className={currentTab === 'backlinks' ? "is-active" : ""}
						onClick={() => ChangeCurrentTab("backlinks")}>
						<a>Linking</a>
					</li>
					{/* Article Components */}
					<li className={currentTab === 'ArticleComponents' ? "is-active" : ""}
						onClick={() => ChangeCurrentTab("ArticleComponents")}>
						<a>Article Components</a>
					</li>
					{/* <li className={currentTab === 'toneOfArticle' ? "is-active" : ""}
						onClick={() => ChangeCurrentTab("toneOfArticle")}>
						<a>Tone Of Article</a>
					</li> */}
				</ul>
			</div>
			<div className="tab-content w-100">
				{renderTab()}
			</div>
			<ErrorAlert ref={errorAlertRef} />
			<SuccessAlert ref={successAlertRef} />

			{/* Switching tabs unsaved changes warning */}
			<div className={"unsaved-settings-warning blur-background " + (tabChangeWarningActive ? "" : "hidden")}>
				<div className={"confirmation-card w-100 mt-4"}>
					<button className={"delete is-pulled-right"}
						onClick={() => {
							setTabChangeWarningActive(false);
						}} />
					<div className={"confirmation-card-content w-100"}>
						<h3 className={"is-size-4 has-text-centered"}>
							You have unsaved changes. Are you sure you want to leave this page?
						</h3>
						<div className={"is-flex is-justify-content-space-evenly"} style={{ width: "90%" }}>
							<AbunButton type={"primary"}
								className={"mt-5 is-block ml-auto mr-auto go-back-button"}
								clickHandler={() => {
									setTabChangeWarningActive(false);
								}}>
								&nbsp;&nbsp;Go Back
							</AbunButton>
							<AbunButton type={"danger"}
								className={"mt-5 is-block ml-auto mr-auto leave-button"}
								clickHandler={() => {
									setTabChangeWarningActive(false);
									setCurrentTab(tabToChangeTo as Tab);
								}}>
								&nbsp;&nbsp;Yes, Leave
							</AbunButton>
						</div>
					</div>
				</div>
			</div>
		</>
	)
}
