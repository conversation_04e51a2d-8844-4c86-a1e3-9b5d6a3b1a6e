// src/components/RouteTracker.tsx
import { useLocation } from 'react-router-dom';
import { useEffect } from 'react';

function getTabId() {
  let tabId = sessionStorage.getItem('tabId');
  if (!tabId) {
    tabId = Date.now().toString();
    sessionStorage.setItem('tabId', tabId);
  }
  return tabId;
}

function pushHistory(path: string) {
  const tabId = getTabId();
  const stackKey = `${tabId}-historyStack`;
  let stack = JSON.parse(sessionStorage.getItem(stackKey) || '[]');
  if (stack[stack.length - 1] !== path) {
    stack.push(path);
    sessionStorage.setItem(stackKey, JSON.stringify(stack));
  }
}

export default function RouteTracker() {
  const location = useLocation();

  useEffect(() => {
    const path = location.pathname + location.search;
    pushHistory(path);
  }, [location]);

  return null;
}
