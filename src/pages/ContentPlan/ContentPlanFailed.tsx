import errorFileImage from '../../assets/images/error-red-file.webp';
import GenericButton from "../../components/GenericButton/GenericButton";
import {retryContentPlanMutation} from "../../utils/api";
import {useMutation} from "@tanstack/react-query";

export default function ContentPlanFailed() {
	// ---------------------- MUTATIONS ----------------------
	const retryContentPlan = useMutation(retryContentPlanMutation);

	// =======================================================
	// ---------------------- MAIN CODE ----------------------
	// =======================================================
	return (
		<>
			<div className={"card"}>
				<div className={"card-content"}>
					<div className={"content is-flex is-flex-direction-column is-align-items-center p-4"}>
						<div className={"block"}>
							<h2 className={"is-size-3 has-text-weight-bold font-secondary has-text-centered"}>
								Content Plan Generation Failed :(
							</h2>
						</div>
						<div className={"block"}>
							<img src={errorFileImage} alt="error red circle" width={128} height={128}/>
						</div>
						<div className={"block"}>
							<p className={"has-text-centered is-size-5"}>
								Please use the button below to <b>retry</b> generating content plan for your website.
							</p>
							<p className={"has-text-centered is-size-5"}>
								If the issue persists, please let us know through our <b>live chat support</b>.
							</p>
						</div>
						<div className={"block mt-6"}>
							<GenericButton text={"Retry Content Plan Generation"}
														 icon={"spinner-static"}
														 type={"primary"}
														 disable={retryContentPlan.isLoading}
														 clickHandler={() => {
															 retryContentPlan.mutate(undefined, {
																 onSuccess: () => {
																	 window.location.reload();
																 }
															 });
														 }}/>
						</div>

					</div>
				</div>
			</div>
		</>
	)
}