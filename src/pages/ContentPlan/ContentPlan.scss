$card-content-padding: 1.5rem;
$card-radius: 12px;

@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/components/card";

@import "../../assets/bulma-overrides/bulmaOverrides";


// ---------------- NO CONNECTED WEBSITE PAGE STYLES ----------------
.cpnw-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

}


// ---------------- PROCESSING PAGE STYLES ----------------
.cpp-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .cpp-title {
        font: bold 1.8rem $secondary-font;
        color: $primary;

        &.black {
            color: $black;
        }

        &.normal-font {
            font-weight: normal;
        }
    }
}

.cpp-tips {
    box-shadow: 0 0.5em 1em -0.125em rgba(0, 0, 0, 0.1), 0 0px 0 1px rgba(0, 0, 0, 0.02);

    @include until(974px) {
        max-width: 90% !important;
    }

    @include from(974px) {
        max-width: 60% !important;
    }
}


// ---------------- MAIN STYLES ----------------
.content-plan-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;

    .content-plan-max-width {
        max-width: 70%;

        @include until(1370px) {
            max-width: 90%;
        }

        @include until(769px) {
            max-width: 100%;
        }
    }

    .content-plan-general-stats {
        display: grid;
        grid-template-rows: repeat(2, 1fr);
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 1rem;
    }

    .website-logo {
        display: inline-block;
        max-width: 60px;
    }

    .web-title {
        width: 70%;
    }

    .web-description {
        width: 70%;
        margin-inline: auto;
    }

    .content-plan-competitors {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-row-gap: 2rem;
        grid-column-gap: 4rem;

        @include until(1350px) {
            grid-column-gap: 2rem;
        }

        @include until(1000px) {
            grid-template-columns: repeat(2, 1fr);
            grid-row-gap: 1.5rem;

            .competitor {
                flex-direction: column;

                img {
                    width: 100%;
                    height: auto;
                }
            }
        }

        @include until(480px) {
            grid-template-columns: 1fr;
        }

        .competitor {
            display: flex;
            flex-direction: row;

            img {
                width: 44px;
                height: 44px;
                object-fit: cover;
                border-radius: 50%;
            }
        }
    }


    .content-plan-article-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-column-gap: 1rem;
    }

    .content-plan-article-titles {
        list-style: none;

        li {
            padding: 1rem 0;
            border-bottom: solid 1px $grey;
            font-size: 1.25rem;

            @include until(1370px) {
                width: auto;
            }
        }
    }

    .content-plan-performance-chart {
        display: inline-block;
        width: 100%;
        height: auto;
        border-radius: 12px;
        box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.08);
    }

    .content-plan-abun-feature-list {
        list-style: none;
        width: 100%;
        margin-inline: auto;

        @include until(1100px) {
            width: 100%;
        }

        li {
            font-size: 1.2rem;
            margin: 1rem 0;
        }
    }
}

.stat-card-cursor {
    cursor: pointer;
}