import { Player } from "@lottiefiles/react-lottie-player";
import { <PERSON> } from "react-router-dom";
import ProTips, { Tip } from "../../components/ProTips/ProTips";
import { useQuery } from "@tanstack/react-query";
import { contentPlanDonePollingQuery } from "../../utils/api";
import { useEffect } from "react";

interface ContentPlanProcessingProps {
   tips: Array<Tip>;
}


export default function ContentPlanProcessing(
   props: ContentPlanProcessingProps
) {
   

   let tips: Array<Tip> = props.tips;

   const { data } = useQuery(contentPlanDonePollingQuery());

   useEffect(() => {
      if (data) {
         if (
            (data as any)["data"]["status"] === "done" ||
            (data as any)["data"]["status"] === "failed"
         ) {
            // website connection timestamp
            localStorage.setItem(
               "websiteConnectionTimeStamp",
               new Date().getTime().toString()
            );
            window.location.reload();
         }
      }
   }, [data]);

   return (
      <>
         <div className={"card"}>
            <div className={"card-content"}>
               <div className={"content pt-3 pb-3 pl-6 pr-6"}>
                  <div className={"cpp-container"}>
                     <p className={"cpp-title black has-text-centered"}>
                        Creating your SEO based Content Strategy &<br />
                        Automation Plan...
                     </p>
                     <Player
                        autoplay
                        loop
                        src="https://assets2.lottiefiles.com/packages/lf20_uecuu64m.json"
                        style={{ height: "300px", width: "300px" }}
                     ></Player>
                     <div
                        className={
                           "is-flex is-flex-direction-column is-align-items-center"
                        }
                     >
                        <p
                           className={"has-text-grey-darker m-0"}
                           style={{ fontSize: "0.9rem" }}
                        >
                           Good Things Take Time 😊
                        </p>
                        <p
                           className={"has-text-grey-darker m-0"}
                           style={{ fontSize: "0.9rem" }}
                        >
                           It might take around 3-5 minutes to complete.
                        </p>
                        <p
                           className={"has-text-grey-darker m-0"}
                           style={{ fontSize: "0.9rem" }}
                        >
                           If it takes more, please{" "}
                           <Link
                              to={`/dashboard`}
                              className={"has-text-grey-darker is-underlined"}
                           >
                              report
                           </Link>{" "}
                           so we can fix it asap.
                        </p>
                        <div className={"notification is-light mt-4"}>
                           NOTE: Other features might not be fully usable until
                           Content Plan Generation is completed.
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
         {tips.length > 0 && (
            <ProTips additional_classes={["mt-6", "cpp-tips"]} tips={tips} />
         )}
      </>
   );
}
