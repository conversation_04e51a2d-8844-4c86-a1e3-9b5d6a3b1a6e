import './ContentPlan.min.css';
import {useLoaderData} from "react-router-dom";
import {Tip} from "../../components/ProTips/ProTips";
import {useEffect} from "react";
import ContentPlanFailed from "./ContentPlanFailed";
import ContentPlanProcessing from "./ContentPlanProcessing";
import ContentPlanNoWebsite from "./ContentPlanNoWebsite";
import ContentPlanDone from "./ContentPlanDone";

interface ContentPlanPageData {
	has_active_website: boolean,
	status?: 'done' | 'processing' | 'failed'
	tips?: Array<Tip>,
}

export default function ContentPlan() {
	useEffect(() => {
		document.title = "Content Plan | Abun"
	}, []);

	const pageData: ContentPlanPageData = useLoaderData() as any;

	// render the correct page

	if (!pageData.has_active_website) return <ContentPlanNoWebsite/>;

	switch (pageData.status) {
		case "processing":
			return <ContentPlanProcessing tips={pageData['tips']!}/>;

		case "done":
			return <ContentPlanDone/>;

		case "failed":
			return <ContentPlanFailed/>

		default:
			return <ContentPlanFailed/>
	}
}