import { useMutation, useQuery } from "@tanstack/react-query";
import { useEffect, useRef, useState } from "react";
import { Link } from "react-router-dom";
import defaultWebIcon from '../../assets/images/icons/content-plan-web-default.webp';
import AbunButton from "../../components/AbunButton/AbunButton";
import Icon from "../../components/Icon/Icon";
import Input from "../../components/Input/Input";
import { addCompetitors, getContentPlanDataQuery, retryFn, saveWebsiteIcp, saveWebsiteIndustry } from "../../utils/api";
import { pageURL } from "../routes";
// import GenericButton from "../../components/GenericButton/GenericButton";
import AbunModal from "../../components/AbunModal/AbunModal";
import TextArea from "../../components/TextArea/TextArea";
import { getDefaultCompetitor<PERSON>ogo } from "../../utils/misc";

// interface Competitor {
// 	domain: string
// 	logo_url: string
// 	kw_count: number
// }

interface ContentPlanData {
	website_name: string
	website_logo_url: string
	website_title: string
	website_domain: string
	website_description: string
	website_icp: string
	website_industry: string
	competitor_domains: Array<string>
	article_titles: Array<string>
	additional_articles_count: number
	total_articles: number
	total_keywords: number
	domain_data: DomainData
	performance_chart_url: string
}

interface DomainData {
	organic_traffic: number | undefined
	organic_keywords: number | undefined
	domain_authority: number | undefined
	total_backlinks: number | undefined
	follow_count: number | undefined
	no_follow_count: number | undefined
	referring_domains_count: number | undefined
}

export default function ContentPlanDone() {
	// ------------------------ STATES ------------------------
	const [
		contentPlanData,
		setContentPlanData
	] = useState<ContentPlanData | null>(null);
	const [editIndustry, setEditIndustry] = useState(false);
	const [industryInput, setIndustryInput] = useState("");
	const [editICP, setEditICP] = useState(false);
	const [icpInput, setIcpInput] = useState("");
	const [websiteConnectionTimeStamp, setWebsiteConnectionTimeStamp] = useState<string | null>(null); // timestamp when the current website was connected

	const [
		showAddCompetitorsModal,
		setShowAddCompetitorsModal
	] = useState<boolean>(false);

	const [
		domainTextArea,
		setDomainTextArea
	] = useState<string>("");

	// ----------------------- REFS -----------------------
	const failAlertRef = useRef<any>(null);
	const successAlertRef = useRef<any>(null);


	// ------------------------ QUERIES ------------------------
	const getContentPlanData = useQuery(getContentPlanDataQuery());

	// ------------------------ MUTATIONS ------------------------
	const saveWebsiteIndustryMut = useMutation({
		mutationKey: ['saveWebsiteIndustry', industryInput],
		mutationFn: saveWebsiteIndustry,
		cacheTime: 0,
		retry: retryFn,
		onError: (error) => {
			error(error);
		}
	});
	const saveWebsiteIcpMut = useMutation({
		mutationKey: ['saveWebsiteIcp', icpInput],
		mutationFn: saveWebsiteIcp,
		cacheTime: 0,
		retry: retryFn,
		onError: (error) => {
			error(error);
		}
	});
	const addCompetitorsMut = useMutation({
		mutationKey: ['addCompetitors'],
		mutationFn: addCompetitors,
		cacheTime: 0,
		retry: retryFn,
		onError: (error) => {
			console.error(error);
			successAlertRef.current?.close();
			failAlertRef.current?.show("Server Error. Please try again in some time.");
		}
	})

	// ------------------------ EFFECTS ------------------------
	useEffect(() => {
		if (getContentPlanData.data) {
			let responseData: ContentPlanData = getContentPlanData.data['data'];
			setContentPlanData(responseData);
			setIndustryInput(responseData.website_industry);
			setIcpInput(responseData.website_icp);
			const storedTimeStamp = localStorage.getItem("websiteConnectionTimeStamp");
			setWebsiteConnectionTimeStamp(storedTimeStamp);
		}
	}, [getContentPlanData.data]);

	// ------------------------ FUNCTIONS ------------------------
	function handleIndustrySave() {
		if (industryInput) {
			saveWebsiteIndustryMut.mutate({industry: industryInput}, {
				onSuccess: () => {
					setContentPlanData({...contentPlanData!, website_industry: industryInput});
					setEditIndustry(false);
				}
			})
		}
	}

	function handleIndustryCancel() {
		setEditIndustry(false);
		setIndustryInput(contentPlanData?.website_industry!);
	}

	function handleICPSave() {
		if (icpInput) {
			saveWebsiteIcpMut.mutate({icp: icpInput}, {
				onSuccess: () => {
					setContentPlanData({...contentPlanData!, website_icp: icpInput});
					setEditICP(false);
				}
			})
		}
	}

	function handleICPCancel() {
		setEditICP(false);
		setIcpInput(contentPlanData?.website_icp!);
	}

	function generateScheduleDate(index: number) {
		if (websiteConnectionTimeStamp !== null) {
			const websiteConnectionDate = new Date(+websiteConnectionTimeStamp);
			const newDate = new Date(websiteConnectionDate.getTime() + index * (24 * 60 * 60 * 1000));

			const day = newDate.getDate();
			const year = newDate.getFullYear();
			const month = newDate.toLocaleString('default', { month: 'short' });

			const formattedDate = `${day}${getDateSuffix(day)} ${month}, ${year}`;
			return formattedDate;
		} else {
			return "";
		}
	}

	// function to get the datesuffix (st, nd, rd, or th) for a date => 1st Jan, 2nd Jan, 12th Jan, 25th Jan
	function getDateSuffix(day: number): string {
		if (day >= 11 && day <= 13) {
			return 'th';
		}
		const lastDigit = day % 10;
		switch (lastDigit) {
			case 1:
				return 'st';
			case 2:
				return 'nd';
			case 3:
				return 'rd';
			default:
				return 'th';
		}
	}

	function addCompetitorsHandler() {
		let domains: Array<string> = domainTextArea.split("\n");
		if (domains.length > 0) {
			domains = domains.map(value => value.toLowerCase().trim());
			addCompetitorsMut.mutate({ domains: domains }, {
				onSuccess: () => {
					// Close modal
					setShowAddCompetitorsModal(false);
					setDomainTextArea("");

					// Modify (plan) data
					getContentPlanData.refetch().then();

					failAlertRef.current?.close();
				},
				onError: () => {
					failAlertRef.current?.show("Failed to add Competitors, please try again!");
				}
			})
		}
	}



	// ==================================================================
	// ------------------------ MAIN RENDER CODE ------------------------
	// ==================================================================
	if (getContentPlanData.isFetching) {
		return (
			<div className={"is-flex is-flex-direction-column is-justify-content-center is-align-items-center mt-6"}>
				<h3 className={"is-size-3"}>Fetching your Content Plan data.</h3>
				<p className={"is-size-5"}>This might take a few seconds... <Icon iconName={"spinner"}/></p>
			</div>
		)

	} else if (getContentPlanData.isError) {
		return (
			<>
				<p>Oops! Something went wrong :(</p>
				<p>Error: {getContentPlanData.error as string}</p>
			</>
		)

	} else if (contentPlanData) {
		return (<>
			<AbunModal active={showAddCompetitorsModal}
								 headerText={""}
								 closeable={true}
								 closeableKey={true}
								 hideModal={() => setShowAddCompetitorsModal(false)}>
				<h4 className={"is-size-4 mt-2 has-text-centered font-secondary has-text-primary has-text-weight-bold"}>
					Enter Competitors
				</h4>
				<p className={"has-text-centered"}>
					You can add both full URLs (ex: <b>https://example.com/blog/</b>) or only registered
					domain (ex: <b>example.com</b>). Invalid values will be ignored.
				</p>
				<TextArea value={domainTextArea}
									className={"mt-4"}
									placeholder={"Enter each competitor on a new line..."}
									rows={15}
									onChange={setDomainTextArea}/>
				<AbunButton type={"success"}
										className={"mt-5 is-block ml-auto mr-auto"}
										disabled={!domainTextArea.length || addCompetitorsMut.isLoading}
										clickHandler={addCompetitorsHandler}>
					<Icon iconName={"floppy-disk"} additionalClasses={["icon-white"]}/>
					&nbsp;&nbsp;{addCompetitorsMut.isLoading ? "Saving..." : "Save Competitors"}
				</AbunButton>
			</AbunModal>
			<div className={"content-plan-container"}>
				<div className={"block"}>
					<h2 className={"is-size-2"}>Your Content Plan</h2>
				</div>
				<div className={"block"}>
					<div className={"is-flex is-align-items-center is-justify-content-center"}>
							<img src={contentPlanData.website_logo_url}
									 className="website-logo"
									 alt="website logo"
									 onError={({currentTarget}) => {
										 currentTarget.onerror = null;
										 currentTarget.src = defaultWebIcon;
									 }}/>
						<div className={"is-flex is-align-items-center pl-5"}>
							<div>
							<h2 className={"is-size-2"}>{contentPlanData.website_name}</h2>
							<a href={`https://${contentPlanData.website_domain}`} target="_blank" rel="noreferrer">
								https://{contentPlanData.website_domain}
							</a>
							</div>
						</div>
					</div>
				</div>
				<div className={"block is-flex is-flex-direction-column is-align-items-center"}>
					{/* ------------------------------ TITLE ------------------------------ */}
					<h3 className={"web-title is-size-3 has-text-primary has-text-centered font-secondary"}>
						{contentPlanData.website_title}
					</h3>
					<div className={"content-plan-max-width"}>
						{/* ------------------------------ DESCRIPTION ------------------------------ */}
						<p className={"web-description has-text-grey-darker has-text-centered mt-4"}>
							{contentPlanData.website_description}
						</p>
						{/* ------------------------------ GENERAL STATS ------------------------------ */}
						<div className={"content-plan-general-stats mt-6"}>
							<StatCard statName={"Organic Traffic"}
												value={contentPlanData.domain_data.organic_traffic}/>
							<StatCard statName={"Organic Keywords"}
												value={contentPlanData.domain_data.organic_keywords}/>
							<StatCard statName={"Domain Authority"}
												value={contentPlanData.domain_data.domain_authority}/>
							<StatCard statName={"Total Backlinks"}
												value={contentPlanData.domain_data.total_backlinks}/>
							<StatCard statName={"Follow / No Follow Links"}
												value={`${contentPlanData.domain_data.follow_count || "NA"} / ${contentPlanData.domain_data.no_follow_count || "NA"}`}/>
							<StatCard statName={"Referring Domain"}
												value={contentPlanData.domain_data.referring_domains_count}/>
						</div>
						{/* ------------------------------ INDUSTRY ------------------------------ */}
						<div className={"mt-6"}>
							<h5 className={"is-size-5 has-text-primary has-text-weight-bold font-secondary"}>
								Industry
								&nbsp;&nbsp;&nbsp;
								<Icon iconName={"edit"}
											width={"1em"}
											additionalClasses={["icon-grey", "icon-cursor-pointer"]}
											onClick={() => setEditIndustry(true)}/>
							</h5>
							<p className={"has-text-grey-darker mt-1"}>
								{editIndustry ?
									<div className={"is-flex is-flex-direction-row is-align-items-center"}>
										<Input value={industryInput}
													 type={"text"}
													 onChange={(value) => {
														 setIndustryInput(value)
													 }}/>
										<AbunButton type={"success"}
																className={"ml-4 is-small"}
																clickHandler={handleIndustrySave}>
											<Icon iconName={"floppy-disk"} additionalClasses={["icon-white"]}/>
											&nbsp;&nbsp;
											{saveWebsiteIndustryMut.isLoading ? "Saving..." : "Save"}
										</AbunButton>
										<AbunButton type={"primary"}
																className={"ml-4 is-small"}
																clickHandler={handleIndustryCancel}>
											Cancel
										</AbunButton>
									</div> :
									contentPlanData.website_industry}
							</p>
						</div>
						{/* ------------------------------ ICP ------------------------------ */}
						<div className={"mt-5"}>
							<h5 className={"is-size-5 has-text-primary has-text-weight-bold font-secondary"}>
								Target Audience / ICP
								&nbsp;&nbsp;&nbsp;
								<Icon iconName={"edit"}
											width={"1em"}
											additionalClasses={["icon-grey", "icon-cursor-pointer"]}
											onClick={() => {
												setEditICP(true);
											}}/>
							</h5>
							<p className={"has-text-grey-darker mt-1"}>
								{editICP ? <div className={"is-flex is-flex-direction-row is-align-items-center"}>
										<Input value={icpInput}
													 type={"text"}
													 onChange={(value) => {
														 setIcpInput(value)
													 }}/>
										<AbunButton type={"success"}
																className={"ml-4 is-small"}
																clickHandler={handleICPSave}>
											<Icon iconName={"floppy-disk"} additionalClasses={["icon-white"]}/>
											&nbsp;&nbsp;
											{saveWebsiteIcpMut.isLoading ? "Saving..." : "Save"}
										</AbunButton>
										<AbunButton type={"primary"}
																className={"ml-4 is-small"}
																clickHandler={handleICPCancel}>
											Cancel
										</AbunButton>
									</div> :
									contentPlanData.website_icp}
							</p>
						</div>
						{/* ------------------------------ COMPETITORS ------------------------------
						<div className={"is-flex is-flex-direction-column is-align-items-center is-justify-content-center"}
								 style={{marginTop: "5rem"}}>
							<h5 className={"is-size-3 has-text-primary has-text-weight-bold font-secondary has-text-centered"}>
								Competitors Identified
							</h5>
							<div className={"content-plan-competitors mt-5"}>
								{contentPlanData.competitor_domains.map(compDomain => (
									<CompetitorCard key={compDomain}
																	logoURL={`${process.env['REACT_APP_LOGO_URL'] + "/" + compDomain}`}
																	domain={compDomain}/>
								))}
							</div>
							<GenericButton text={"Add Competitors"}
						 				type={"success"}
										icon={"plus"}
										additionalClassList={["mt-6"]} 
										clickHandler={() => setShowAddCompetitorsModal(true)} />
							<Link to={pageURL['competitorResearch']}
										className={"button is-success mt-4"}>
								<Icon iconName={"plus"} additionalClasses={["icon-white", "mr-3"]}/><p className={"link-text"}>Add Competitors</p>
							</Link>
						</div>
						*/}
						{/* ------------------------------ PERFORMANCE CHART ------------------------------ */}
						<div className={"has-text-centered pt-3"}>
						<h3 className={"has-text-primary has-text-centered is-size-3 mt-6"}>
								Increase Organic Traffic using Abun. Your SEO & Content Co-Pilot.
							</h3>
						<div className={"columns is-desktop mt-5 mb-6"}>
							<div className={"column is-flex is-align-items-center pr-5"}>
								<img src={contentPlanData.performance_chart_url}
									 alt="performance chart"
									 className={"content-plan-performance-chart has-shadow"}/>
							</div>
							<div className={"column pl-5"}>
							<ul className={"content-plan-abun-feature-list has-text-left mt-4"}>
								<li><Icon iconName={"green-checkmark-circle"}/>&nbsp;&nbsp;Fully Automated Keyword Research</li>
								<li><Icon iconName={"green-checkmark-circle"}/>&nbsp;&nbsp;Top, Middle & Bottom of Funnel Content</li>
								<li><Icon iconName={"green-checkmark-circle"}/>&nbsp;&nbsp;Articles Optimized for Ranking</li>
								<li><Icon iconName={"green-checkmark-circle"}/>&nbsp;&nbsp;Auto Article Creation & Posting</li>
								<li><Icon iconName={"green-checkmark-circle"}/>&nbsp;&nbsp;SERP / Rank Tracking & Reporting</li>
								<li><Icon iconName={"green-checkmark-circle"}/>&nbsp;&nbsp;Articles Written Keeping Your ICP in Mind
								</li>
							</ul>
							</div>
						</div>
							<p className={"is-size-4 mt-6"}>
								Auto Post Content to your Blog &
								See your Organic Traffic Grow.
							</p>
							<Link to={pageURL['settings'] + "?tab=integration"}
										className={"button is-success mt-4"}>
								Enable Automation
							</Link>	
						</div>
						{/* ------------------------------ ARTICLE PLAN ------------------------------ */}
						<div className={"is-flex is-flex-direction-column is-justify-content-center is-align-items-center"}
								 style={{marginTop: "5rem"}}>
							<h5 className={"is-size-3 has-text-primary font-secondary has-text-centered"}>
							Your Personalized Article Schedule for next 30 days
							</h5>
						</div>
						<div className={"mt-5"}>
							<ul className={"content-plan-article-titles"}>
								{contentPlanData.article_titles.map((title, index) => (
									<li className={"is-flex is-justify-content-space-between"} key={index}><p>{title}</p> <p>{generateScheduleDate(index + 1)}</p></li>
								))}
							</ul>
							<Link to={pageURL['articles']}>
								<p className={"has-text-primary is-size-6"}>+{contentPlanData.additional_articles_count} More!</p>
							</Link>
							<div className={"has-text-centered"}>
							<Link to={pageURL['articles']} className={"button is-success m-4"}>Generate Sample Article</Link>
							{/* <Link to={pageURL['manageSubscription']} className={"button is-success m-4"}>Upgrade & Schedule</Link> */}
							</div>
						</div>
					</div>
				</div>
			</div>
			</>)
	} else {
		return (
			<>
				<p>Oops! Something went wrong :(</p>
				<p>Error: Missing Data</p>
			</>
		)
	}
}

function StatCard(props: { statName: string, value: string | number | undefined, onClick?: () => void }) {
	return (
		<div className={`card ${props.onClick && "stat-card-cursor"}`} onClick={props.onClick}>
			<div className={"card-content"}>
				<div className={"content"}>
					<h5 className={"is-size-6 has-text-weight-bold font-secondary"}>
						{props.statName}
					</h5>
					<p className={"is-size-4 has-text-warning has-text-weight-bold font-secondary"}>
						{props.value || "NA"}
					</p>
				</div>
			</div>
		</div>
	)
}

function CompetitorCard(props: {logoURL: string, domain: string}) {
	return (
		<div className={"competitor"}>
			<img src={props.logoURL} 
					onError={({currentTarget}) => {
					currentTarget.onerror = null;
					currentTarget.src = getDefaultCompetitorLogo();
					}} alt="logo"/>
			<div className={"ml-4 is-flex is-justify-content-center is-align-items-center"}>
				<p className={"is-size-5"}>{props.domain}</p>
			</div>
		</div>
	)
}
