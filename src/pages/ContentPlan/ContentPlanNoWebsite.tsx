import {pageURL} from "../routes";
import {Link} from "react-router-dom";
import Icon from "../../components/Icon/Icon";

export default function ContentPlanNoWebsite() {
	return (
		<div className={"cpnw-container"}>
			<div className={"card w-100"}>
				<div className={"card-content"}>
					<div
						className={"content is-flex is-flex-direction-column align-center is-justify-content-center pt-3 pb-3"}>
						<h2 className={"is-size-2 has-text-centered has-text-primary has-text-weight-bold font-secondary"}>
							Add your website to create Content Plan<br />
							to boost your Organic SEO Traffic
						</h2>
						<p className={"has-text-centered is-size-4 mt-3"}>
							If you feel this is an error, please connect with our Live Chat Team.
						</p>
						<Link to={pageURL['connectWebsite']} className={"button is-success mt-5 ml-auto mr-auto"}>
							&nbsp;&nbsp;<b>Add Website</b>&nbsp;&nbsp;<Icon iconName={"arrow-right"} additionalClasses={["icon-white", "is-size-7"]} />
						</Link>
					</div>
				</div>
			</div>
		</div>
	)
}