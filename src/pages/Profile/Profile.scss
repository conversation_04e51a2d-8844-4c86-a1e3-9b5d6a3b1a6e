@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

$input-placeholder-color: $grey-darker;

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/form/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";

@import "../../assets/bulma-overrides/bulmaOverrides";
@import "../../assets/custom-global-styles/customGlobablStyles";

.profile-header{
  h2 {
    font-weight: 600 !important;
    font-family: $primary-font !important;
    font-size: 2rem !important;
  }

  p {
    color: rgba(0, 0, 0, .698);
    font-family: $secondary-font !important;
    font-size: 1.125rem !important;
  }
}

.profile-main-content{
    border: 1px solid #e7e7e7;
    border-radius: 8px;

    h1{
      font-size: 1.5rem;
      color: #000;
      font-weight: 600;
    }

    .label-full-name{
      width: 100%;

      @media (min-width:1160px) {
        max-width: 350px;
      }
    }


    .label-title{
      color: #000;
      font-weight: 500;
    }

    .label-full-name input{
      color: #000000 !important;
    }

    .profile-email-field {
      position: relative;
      width: 100%;
      
      @media (min-width:1160px) {
        max-width: 350px;
      }

      input{
        border-color: #dbdbdb;
        color: #000;
        font-weight: 400;
      }
    
      img {
        position: absolute;
        top: 9px;
        right: 9px;
      }
    }
    .email-verfified-text{
      color: $success;
      font-size: 0.9rem;
      font-weight: 500;
      font-family: $secondary-font !important;
    }
}

.profile-footer-container {
  border: 1px solid #e7e7e7;
  border-radius: 8px;

  .website-logo-container{
    width: 100%;
    max-width: 410px;

    .label-title{
      color: #000;
      font-weight: 500;
    }
  }

  .logout-button {
    background-color: $white !important;
    border: 1px solid #e7e7e7 !important;
    border-radius: 7px;
    padding: 1.3em 1.5em;
    font-size: 1.2rem;
    color: $black !important;

    &:hover {
      background-color: #f5f5f5 !important;
    }

    &:focus{
      box-shadow: none !important;
    }
  }

  .profile-footer-title {
    font-size: 1.5rem;
    color: #000;
    font-weight: 600;
  }

  h4 {
    font-size: 1.3rem;
    font-weight: 500;
  }

  .delete-text {
    font-family: $secondary-font !important;
  }

}

.domain-image-upload {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  gap: 1.4rem;
  padding-top: 1rem;
  border-radius: 0.5rem;

  .domain-image-container {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #E5E5E5;
    border-radius: 0.5rem;
    // margin-top: 1rem;
    height: auto;
    width: 30%;

    @media screen and (max-width : 600px) {
      width: 60%;
    }
  }

  .domain-image-upload-container {

    @media screen and (max-width : 600px) {
      width: 100%;
    }

    .image-file-name{
      color: #000;
      font-weight: 500;
      font-size: 13px;
      margin-left: 4px;
    }

    .featured-image-upload-actions {
      width: 100%;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 10px;

      label{
        border-radius: 6px;
        color: #000;
        font-weight: 500;
      }
    }
  }
}


.Profile-delete-modal {
  .card-header p{
    color: #000 !important;
  }
  .card-content {
    padding: 1.5rem !important;

    h4 {
      color: #000;
      font-weight: normal;
    }

    p {
      font-family: $secondary-font !important;
    }

  }
}