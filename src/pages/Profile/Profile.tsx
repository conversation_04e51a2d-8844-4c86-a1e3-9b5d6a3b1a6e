import FormControlLabel from '@mui/material/FormControlLabel';
import FormGroup from '@mui/material/FormGroup';
import { styled } from '@mui/material/styles';
import Switch, { SwitchProps } from '@mui/material/Switch';
import { useMutation } from "@tanstack/react-query";
import { useEffect, useRef, useState } from "react";
import { useLoaderData, useNavigate, useRouteLoaderData } from "react-router-dom";
import TimezoneSelect from "react-timezone-select";
import defaultWebIcon from '../../assets/images/icons/defaultCompetitorLogos/default-competitor-logo1.webp';
import AbunButton from "../../components/AbunButton/AbunButton";
import AbunModal from "../../components/AbunModal/AbunModal";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import GenericButton from "../../components/GenericButton/GenericButton";
import Icon from "../../components/Icon/Icon";
import Input from "../../components/Input/Input";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { BasePageData } from "../../pages/Base/Base";
import {
	deleteCurrentWebsite,
	emailPreferenceMutation,
	resendVerificationEmailMutation,
	saveUserDetailsMutation,
	uploadDomainLogoMutation
} from "../../utils/api";
import { pageURL } from "../routes";
import './Profile.min.css';

interface ProfilePageData {
	verified: boolean
	username: string
	website: string | null
	email: string
	tz: string
	send_notif_email: boolean
	// content_plan_generation_status: string
	plan_name: string
	domain_logo_url: string
}

export default function Profile() {
	// ---------------------------- PAGE DATA ----------------------------
	const { pageData } = useLoaderData() as {
		pageData: ProfilePageData;
	};
	const basePageData: BasePageData = useRouteLoaderData("base") as BasePageData;
	// ---------------------------- REACT ROUTER ----------------------------
	const navigate = useNavigate();

	// ---------------------------- STATES ----------------------------
	const [username, setUsername] = useState(pageData.username);
	const [sendNotifEmail, setSendNotifEmail] = useState(pageData.send_notif_email);
	const [domainLogoURL, setDomainLogoURL] = useState(pageData.domain_logo_url);
	const [
		selectedTimezone,
		setSelectedTimezone
	] = useState(pageData.tz === "UTC" ? "Etc/GMT" : pageData.tz);
	const [discWebsiteInput, setDiscWebsiteInput] = useState("");
	const [disconnectWebsiteModelActive, setDisconnectWebsiteModelActive] = useState(false);
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const [selectedFileURL, setSelectedFileURL] = useState<string | null>(null);
	const [uploading, setUploading] = useState<boolean>(false);
    const [fileName, setFileName] = useState('');

	// ---------------------------- MUTATIONS ----------------------------
	const resendVerificationEmail = useMutation(resendVerificationEmailMutation);

	// -------------------------- REFS --------------------------
	const successAlertRef = useRef<any>(null);
	const errorAlertRef = useRef<any>(null);

	// ---------------------------- MUTATIONS ----------------------------
	const saveUserDetails = useMutation(saveUserDetailsMutation);
	const saveEmailPref = useMutation(emailPreferenceMutation);
	const uploadDomainLogoImage = useMutation(uploadDomainLogoMutation);
	const deleteWebsite = useMutation({
		mutationKey: ['deleteCurrentWebsite'],
		mutationFn: deleteCurrentWebsite,
		cacheTime: 0,
		onSuccess: () => {
			window.location.reload();
			navigate(pageURL['createArticle']);
		},
		onError: (error) => {
			console.error(error);
			errorAlertRef.current?.show("Oops! Something went wrong. Please try again in some time.")
		}
	})

	// ---------------------------- EFFECTS ----------------------------
	useEffect(() => {
		document.title = "Profile | Abun"
	}, []);

	// ----------------------- NON STATE CONSTANTS -----------------------
	const websiteDomain = pageData.website?.startsWith("default") && pageData.website.endsWith(".xyz") ? "Default" : pageData.website

	
	// --------------------- FUNCTIONS ------------------------
	function handleFileSelection(event: React.ChangeEvent<HTMLInputElement>) {
		if (event.target.files && event.target.files.length > 0) {
			const file = event.target.files[0];

			// Check if file size is greater than 2 MB
			const maxSize = 1024 * 1024 * 2; 

			if (file.size > maxSize) {
				errorAlertRef.current?.show("File size should be less than 2mb.");
				return;
			}

			setSelectedFile(file);
			setFileName(file.name);
			setSelectedFileURL(URL.createObjectURL(file));
		}
	}

	function handleNameChange(value) {
		const minLength = 3;
        const maxLength = 75;

		if (value.length > maxLength) {
            errorAlertRef.current?.show(`Maximum ${maxLength} characters allowed.`);
            return; // Block if over maxLength
        }

        setUsername(value);

        if (value.length < minLength) {
            errorAlertRef.current?.show(`Minimum ${minLength} characters required.`);
			setTimeout(() => {
				errorAlertRef.current?.close();
			}, 4000);
        }
	}

	async function uploadImage() {
		if (selectedFile) {
			setUploading(true);

			// upload the image
			uploadDomainLogoImage.mutate({
				image: selectedFile,
			}, {
				onSuccess: (data) => {
					setDomainLogoURL(data.data.domain_logo_url);
					successAlertRef.current?.show("Domain logo uploaded successfully..")
					setSelectedFile(null);
					setSelectedFileURL(null);
					setUploading(false);
					setFileName('');
					setTimeout(() => {
				        successAlertRef.current?.close();
			        }, 3000);
				},
				onError: () => {
					errorAlertRef.current?.show("Oops! Failed to upload :( Please try again later or contact us for further support.");
					setUploading(false);
				}
			});
		}
	}

	const convertToBase64 = (file) => {
		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			reader.readAsDataURL(file);
			reader.onload = () => resolve(reader.result);
			reader.onerror = (error) => reject(error);
		});
	};

	function CustomizedSwitch() {
		const IOSSwitch = styled((props: SwitchProps) => (
			<Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
		))(({ theme }) => ({
			width: 42,
			height: 26,
			padding: 0,
			'& .MuiSwitch-switchBase': {
				padding: 0,
				margin: 2,
				transitionDuration: '300ms',
				'&.Mui-checked': {
					transform: 'translateX(16px)',
					color: '#fff',
					'& + .MuiSwitch-track': {
						backgroundColor: '#2e64fe',
						opacity: 1,
						border: 0,
						...theme.applyStyles('dark', {
							backgroundColor: '#2ECA45',
						}),
					},
					'&.Mui-disabled + .MuiSwitch-track': {
						opacity: 0.5,
					},
				},
				'&.Mui-focusVisible .MuiSwitch-thumb': {
					color: '#33cf4d',
					border: '6px solid #fff',
				},
				'&.Mui-disabled .MuiSwitch-thumb': {
					color: theme.palette.grey[100],
					...theme.applyStyles('dark', {
						color: theme.palette.grey[600],
					}),
				},
				'&.Mui-disabled + .MuiSwitch-track': {
					opacity: 0.7,
					...theme.applyStyles('dark', {
						opacity: 0.3,
					}),
				},
			},
			'& .MuiSwitch-thumb': {
				boxSizing: 'border-box',
				width: 22,
				height: 22,
			},
			'& .MuiSwitch-track': {
				borderRadius: 26 / 2,
				backgroundColor: '#E9E9EA',
				opacity: 1,
				transition: theme.transitions.create(['background-color'], {
					duration: 500,
				}),
				...theme.applyStyles('dark', {
					backgroundColor: '#39393D',
				}),
			},
		}));
	
			return (
				<FormGroup>
					<FormControlLabel
						control={<IOSSwitch sx={{ m: 1 }} defaultChecked={sendNotifEmail} 
						            onChange={() => {
										const newValue = !sendNotifEmail;
								    	saveEmailPref.mutate({ send_notif_email: newValue }, {
								    		onSuccess: () => {
								    			setSendNotifEmail(newValue);
												successAlertRef.current?.show(
													`Email notifications ${newValue ? 'enabled' : 'disabled'}`
												);
												setTimeout(() => {
													successAlertRef.current?.close();
												}, 3000);
								    		}
								    	});
								    }} />								
								}
						label=""
					/>
				</FormGroup>
			);
		}
	
	// ===================================================================
	// ---------------------------- MAIN CODE ----------------------------
	// ===================================================================
	return (
		<>  
		    <div className="profile-header">
			    <h2 className={"w-100"}>Account Settings</h2>
			    <p>Manage Your Account preferences</p>
			</div>
			<div className={"w-100 mt-5 profile-main-content"}>
				<div className={""}>
					<h1 className={"card-header-title px-5"}>Profile Settings</h1>
				</div>
				<div className={"card-content p-5"}>
					<div className={"content"}>
						<div className={""}>
							<div className="w-100 is-flex is-flex-wrap-wrap" style={{gap:'17px'}}>

								{/* ------------------------- FULL NAME ------------------------- */}
							    <label className={"label label-full-name"}>
							    	<span className="label-title">Full Name:</span>
							    	<Input value={username}
							    		type={"text"}
							    		className={"mt-2"}
							    		placeholder={"Your full name (ex. John Doe)"}
										onChange={(val) => handleNameChange(val)}
										/>
							    </label>

                                {/* ------------------------- TIMEZONE ------------------------- */}
								<label className={"label label-full-name"}>
									<span className="label-title">Timezone:</span>
									<TimezoneSelect
										value={selectedTimezone}
										className={"mt-2"}
										onChange={(tz) => {
											setSelectedTimezone(tz.value)
										}}
										styles={{
                                            singleValue: (provided) => ({
                                              ...provided,
                                              fontWeight: 400,
											  color:'#000'
                                            }),
											option: (provided, state) => ({
                                              ...provided,
                                              fontWeight: 400,
                                              color: '#000',
                                            })
                                        }}
									/>
								</label>
							</div>

							{/* ------------------------- EMAIL ADDRESS ------------------------- */}
							<label className={"label mt-5"}>
								<span className="label-title">Email Address:</span>
								<div className="" style={{gap:'9px'}}>
									<div className={"profile-email-field mt-2"}>
										<input type="text"
											defaultValue={pageData.email}
											disabled={true}
											className={"input disabled-color"} />
                                            {pageData.verified && <Icon iconName={"green-checkmark-circle"} />}
									</div>
                                    {!pageData.verified &&
											<button className={"button is-small is-primary mt-3"}
												disabled={resendVerificationEmail.isLoading}
												onClick={() => {
													successAlertRef.current?.close();
													errorAlertRef.current?.close();
													resendVerificationEmail.mutate(undefined, {
														onSuccess: () => {
															successAlertRef.current?.show(
																"Verification email has been sent to your registered email id successfully!"
															);
														},
														onError: (error) => {
															console.error(error);
															errorAlertRef.current?.show("Oops! Something went wrong. Please try again later");
														}
													})
												}}>
												Resend Verification Email
											</button>
									}
								</div>
							</label>

                            {/* ------------------------- SAVE PROFILE CHANGES ------------------------- */}
							<GenericButton text={"Update"}
								type={"success"}
								additionalClassList={["mt-4", "profile-save-button"]}
								clickHandler={() => {
									successAlertRef.current?.close();
									errorAlertRef.current?.close();
									saveUserDetails.mutate({ username: username, tz: selectedTimezone }, {
										onSuccess: () => {
											successAlertRef.current?.show("All user details saved successfully!");
										},
										onError: () => {
											errorAlertRef.current?.show(
												"Oops! Something went wrong :( Please try " +
												"again later or contact us for further support."
											);
										}
									})
								}} />

							<hr style={{backgroundColor:'#e7e7e7', maxWidth:'717px'}}/>

                            {/* ------------------------- EMAIL NOTIFICATION ------------------------- */}
							<label className="label mt-5 is-flex is-align-items-center" style={{width:'fit-content'}}>
								<CustomizedSwitch
						            aria-label="Enable Email Notification"
					            />
								<div>
									<span className="label-title">Email Notifications</span>
									<p style={{fontWeight:'400'}}>Receive account updates when task is completed</p>
								</div>
							</label>
						</div>
					</div>
				</div>
			</div>

			{/* ------------------------- LOGOUT ------------------------- */}
			{/* <div className={"card w-100 mt-5"}>
				<div className={"card-content settings-card"} style={{padding: '1.5rem'}}>
					<div className={"content is-flex is-flex-direction-row is-justify-content-center is-align-items-center"}>
						<NavLink to={`/logout`} className="logout-button align-image-text-vertically is-primary button">
							<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
							    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
							    <polyline points="16 17 21 12 16 7"></polyline>
							    <line x1="21" x2="9" y1="12" y2="12"></line>
						    </svg>&nbsp;&nbsp;Logout from Abun
						</NavLink>
					</div>
				</div>
			</div> */}

			{/* ------------------------- DISCONNECT WEBSITE ALERT MODAL ------------------------- */}
			<div className="Profile-delete-modal">
			<AbunModal active={disconnectWebsiteModelActive}
				headerText={"Delete Project"}
				closeable={true}
				closeableKey={true}
				closeOnOutsideClick={true}
				hideModal={() => {
					setDisconnectWebsiteModelActive(false);
				}}>
				<div>
					<h4 className={"is-size-4 has-text-centered"}>Are You Sure? Deleting <b>{websiteDomain}</b> is permanent!</h4>
					<p className={"has-text-centered mt-4"}>
					You'll lose access to all generated Articles, competitor insights, and keyword data. Published articles on your website will stay.
					</p>
					<p className={"has-text-centered mt-3"}>
						Not Sure? <a href="/contact-us" target="_blank" rel="noopener noreferrer">Contact Support Now</a>
					</p>
					<div className={"is-flex is-flex-direction-row is-justify-content-center mt-6 has-text-centered"}>
						<AbunButton type={"danger"} clickHandler={() => {
							deleteWebsite.mutate();
						}}>
							Yes, Delete
						</AbunButton>
						<AbunButton type={"primary"} className={"ml-4"} clickHandler={() => {
							setDisconnectWebsiteModelActive(false);
						}}>
							Cancel
						</AbunButton>
					</div>
				</div>
			</AbunModal>
			</div>

			{/* ------------------------- DISCONNECT WEBSITE ------------------------- */}
			 <div className={"profile-footer-container mt-5 w-100 p-5"}>
				{websiteDomain && <div className="is-flex is-flex-direction-column">
					<h1 className="mb-5 profile-footer-title">Website Settings</h1>

                    {/* ------------------------- WEBSITE LOGO UPLOAD ------------------------- */}
					<div className={"label website-logo-container"}>
						<span className="label-title">Website Logo (Max Size: 2MB)</span>
						<div className={"domain-image-upload"}>
							<div className={"domain-image-container"}>
									{domainLogoURL ?
										<img src={domainLogoURL} alt={"featured"} /> : <img src={defaultWebIcon} alt={"featured"}/>
									}
							</div>
							<div className={"domain-image-upload-container"}>
								
								<div className={"featured-image-upload-actions"}>
									<input type={"file"}  className="is-hidden" id="fileInput" accept={"image/*"} onChange={handleFileSelection} />
									<div className="is-flex is-flex-direction-column is-align-items-flex-start">
									    <label htmlFor="fileInput" className="button">
                                            Choose File
                                        </label>
									</div>
									<AbunButton className="" type={"success"} clickHandler={uploadImage} disabled={uploading}>
										{uploading ? "Uploading..." : "Upload"}
									</AbunButton>
								</div>
								{ fileName && (
								<span className={"image-file-name"}>{fileName.length > 16 ? `${fileName.slice(0, 16)}...` : fileName}</span>
								)}
							</div>
						</div>
					</div>

					<hr style={{backgroundColor:'#e7e7e7', maxWidth:'717px'}}/>
					
					<h4 className={"has-text-danger mb-2"}>Delete Website</h4>
					<p className="delete-text">Permanently delete&nbsp;<b>{websiteDomain}</b>&nbsp;and all associated data. This cannot be undone.</p>
					<AbunButton type={"danger"}
						className={"mt-3"}
						clickHandler={() => {
							setDisconnectWebsiteModelActive(true);
						}}>
						Delete&nbsp;{websiteDomain} 
					</AbunButton>
				</div>}
			</div>

			<ErrorAlert ref={errorAlertRef} />
			<SuccessAlert ref={successAlertRef} />
		</>
	)
}

