import { useMutation } from '@tanstack/react-query';
import React, { useRef, useState } from 'react';
import { Link, useLoaderData, useNavigate } from 'react-router-dom';
import { Tooltip } from 'react-tooltip';
import AbunLoader from "../../components/AbunLoader/AbunLoader";
import AbunModal from '../../components/AbunModal/AbunModal';
import ErrorAlert from '../../components/ErrorAlert/ErrorAlert';
import GenericButton from '../../components/GenericButton/GenericButton';
import SuccessAlert from '../../components/SuccessAlert/SuccessAlert';
import { glossaryMutation } from '../../utils/api';
import { pageURL } from '../routes';
import './Glossary.min.css';
import GlossaryHome from "./GlossaryHome";

interface UserData {
    verified: boolean
    username: string
    website: string
    email: string
    tz: string
    send_notif_email: boolean
    glossary_remaining: number
}

const Glossary: React.FC = () => {
    // ----------------------------- LOADER -----------------------------
    const pageData: UserData = useLoaderData() as UserData;

    const [word, setWord] = useState('');
    const [loadingWord, setLoadingWord] = useState(false);
    const [proceedEnabled, setProceedEnabled] = useState(false);
    const [requestModalActive, setRequestModalActive] = useState(false);
    const [activeTab, setActiveTab] = useState("glossary-creator");
    const navigate = useNavigate();

    const glossaryMut = useMutation(glossaryMutation);

    // --------------------------- Refs ---------------------------
    const successAlertRef = useRef<any>(null);
    const errorAlertRef = useRef<any>(null);

    const handleGenerateGlossary = () => {
        if (!word.trim()) return;
        // Trigger the mutation
        glossaryMut.mutate(
            { word },
            {
                onSuccess: (response) => {
                    if (response.data.status === "rejected") {
                        if (response.data.reason === "max_limit_reached") {
                            errorAlertRef.current?.show("You have reached your max glossary generation limit for the month.");
                        } else {
                            errorAlertRef.current?.show(`Glossary generation request failed. Error ID: ${response.data.reason}`);
                        }
                        return;
                    }
                    const { project_name, task_id, project_id } = response.data;
                    localStorage.setItem('glossary_task_id', task_id);
                    successAlertRef.current?.show('Query submitted. It will take few minutes to complete.');
                    navigate(`/glossary-generator/${project_id}`);
                },
                onError: () => {
                    errorAlertRef.current?.show(
                        'Oops! Something went wrong. Please try again or contact support if the issue persists.'
                    );
                    setTimeout(() => {
                        errorAlertRef.current?.close();
                    }, 5000);
                },
                onSettled: () => {
                    setLoadingWord(false);
                },
            }
        );
    };


    function goBack() {
        navigate(pageURL['glossaryHome']);
    }

    // Track input changes and update word state
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newWord = e.target.value;
        setWord(newWord);

        // Enable the Proceed button when there is input
        setProceedEnabled(newWord.trim().length > 0 && pageData.verified);
    };

    return (
        <div className="glossary-card">
            <div className="">
                <div className="mb-5 is-flex is-justify-content-space-between is-flex-wrap-wrap">
                    <div className={"is-flex gloassy-header is-justify-content-center is-flex-direction-column "}>
                        <h2>Glossary Article Creator</h2>
                        <p className={"glossary-p has-text-dark"}>Automatically generate a complete glossary of key terms for your niche, product, or industry, optimized for SEO and clarity.<br />
                            Educate your audience, boost topical authority, and capture long-tail traffic with zero manual effort.
                        </p>
                    </div>

                    <span className="is-block mt-2">
                        {pageData.glossary_remaining} credits remaining. <Link to={pageURL["subscriptionCredit"]} className="is-text has-text-black is-underlined" >View Credits</Link>
                    </span>
                </div>

                <div className="tabs is-medium" style={{ scrollbarWidth: 'none' }}>
                    <ul>
                        <li className={activeTab === "glossary-creator" ? "is-active" : ""}>
                            <a onClick={() => setActiveTab("glossary-creator")}>AI Glossary Creator</a>
                        </li>
                        <li className={activeTab === "projects" ? "is-active" : ""}>
                            <a onClick={() => setActiveTab("projects")}>Projects</a>
                        </li>
                    </ul>
                </div>

                {activeTab === "glossary-creator" &&
                    <>
                        <div className="is-flex is-align-items-center is-flex-direction-column glossary-form-container">
                            <h3 >Input a topic to auto-generate glossary definitions</h3>
                            <hr />
                            <div className="glossary-content w-100">
                                <div className="field">
                                    <label className="is-size-6 has-text-weight-medium has-text-black label">Enter a Topic for glossary terms</label>
                                    <div className="control">
                                        <input
                                            className="input"
                                            type="text"
                                            placeholder="Topic"
                                            value={word}
                                            onChange={handleInputChange}
                                            onKeyDown={(e) => {
                                                if (e.key === 'Enter' && proceedEnabled) {
                                                    handleGenerateGlossary();
                                                }
                                            }}
                                        />
                                    </div>
                                </div>
                                <div data-tooltip-id="proceedButtonTip" data-tooltip-content="Verify email to create glossary terms">
                                    <GenericButton
                                        text={"PROCEED"}
                                        iconAfterText={"arrow-right"}
                                        iconWidth={"1.1em"}
                                        type={"none"}
                                        additionalClassList={["mt-2", "button is-responsive is-link", "is-size-6"]}
                                        clickHandler={handleGenerateGlossary}
                                        disable={!proceedEnabled}
                                    />
                                    {!pageData.verified && (
                                        <Tooltip id="proceedButtonTip" place="bottom" />
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* ------------------------------ ONGOING REQUEST MODAL ------------------------------ */}
                        <AbunModal
                            active={requestModalActive}
                            headerText={""}
                            closeable={false}
                            hideModal={() => setRequestModalActive(false)}
                        >
                            <div className="loadingData w-100 is-flex is-justify-content-center is-align-items-center">
                                <AbunLoader show={requestModalActive} height="20vh" />
                            </div>
                            <p className="is-size-4 has-text-centered mb-4">Generating Glossary Terms</p>
                        </AbunModal>


                        <SuccessAlert ref={successAlertRef} style={{ left: '60%' }} />
                        <ErrorAlert ref={errorAlertRef} style={{ left: '60%' }} />
                    </>
                }

                {activeTab === "projects" &&
                    <GlossaryHome />
                }

            </div>
        </div>
    );
};

export default Glossary;
