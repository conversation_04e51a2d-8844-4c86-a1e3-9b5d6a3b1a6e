import Icon, { IconNames } from "../../components/Icon/Icon";

type Stats = {
    id: string
    icon_name: IconNames
    title: string
    count: number
}

type GlossaryReportProps = {
  glossaryReport: Stats[];
};

export default function GlossaryReport({ glossaryReport }: GlossaryReportProps){    

    // ----------------------- STATES -----------------------
    
        return (
            <>
                <div className={"glossary-report-container mt-4 mb-2"}>
                    {glossaryReport.map((stat) => (
                        <div className={"card mt-2"} key={stat.id}>
                            <div className={"card-content p-0"}>
                                <div className={"content"}>
                                    <div className={"usage-stats-container"}>
                                        <p className={"usage-stats-item--title"}>
                                            <Icon iconName={stat.icon_name} />&nbsp;&nbsp;&nbsp;{stat.title}
                                        </p>
                                        <h4 style={{color: "#000"}} className={"card-number"}>{stat.count}</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}                    				                        
                    </div>
            </>
        )
    }