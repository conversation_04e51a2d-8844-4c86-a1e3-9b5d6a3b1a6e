import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useQuery } from "@tanstack/react-query";
import { useEffect, useRef, useState } from "react";
import { Helmet } from 'react-helmet';
import { Link, useLoaderData, useNavigate } from "react-router-dom";
import AbunButton from "../../components/AbunButton/AbunButton";
import AbunModal from "../../components/AbunModal/AbunModal";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { getGuestPostFinderQueries, makeApiRequest } from "../../utils/api";
import { pageURL } from "../routes";
import './GuestPostFinder.min.css';
import GuestPostFinderTable from "./GuestPostFinderTable";

interface Query {
    id: string,
    query: string,
    limit: number,
    is_processing: boolean,
    created_at: Date
}

interface UserData {
    verified: boolean
    username: string
    website: string
    email: string
    tz: string
    send_notif_email: boolean
    guest_post_finder_remaining: number
}

function GuestPostFinder() {
    // ----------------------------- LOADER -----------------------------
    const pageData: UserData = useLoaderData() as UserData;

    // -------------------------- STATES --------------------------
    const [queries, setQueries] = useState<Array<Query>>([]);
    const [topic, setTopic] = useState("")
    const [limit, setLimit] = useState(400);
    const navigate = useNavigate();
    const [gpfQueriesGenerated, setGpfQueriesGenerated] = useState(0);
    const [currentPlanName, setCurrentPlanName] = useState('');
    const [maxGpfAllowed, setMaxGpfAllowed] = useState(0);
    const [showWarning, setShowWarning] = useState(false);
    const [activeTab, setActiveTab] = useState("guest-post");


    // -------------------------- QUERIES --------------------------
    const {
        isFetching,
        isError,
        data,
        refetch
    } = useQuery({
        queryKey: ['getGuestPostFinderQueries'],
        queryFn: getGuestPostFinderQueries,
        refetchOnWindowFocus: false
    });


    // -------------------------- REFS --------------------------
    const errorAlertRef = useRef<any>(null);
    const successAlertRef = useRef<any>(null);

    // ---------------------- EFFECTS ----------------------
    useEffect(() => {
        if (data) {
            setQueries(data['data']['queries']);
            setGpfQueriesGenerated(data.data.gpf_queries_generated);
            setCurrentPlanName(data.data.current_plan_name);
            setMaxGpfAllowed(data.data.max_gpf_allowed);
        }
    }, [data]);

    // Creative Logic Implemented on keyword Click
    const handleSubmit = async (e: any) => {
        e.preventDefault();
        errorAlertRef.current?.close();
        // Check if the topic is empty
        if (!topic || !limit) {
            return;
        }
        try {
            const response = await makeApiRequest(
                `/api/frontend/post-guest-post-finder-query/`,
                'post',
                {
                    query: topic,
                    limit: limit
                }
            );

            const data = response.data;
            if (data.success) {
                localStorage.setItem('taskId', data.task_id);
                successAlertRef.current?.show('Query submitted. It will take few minutes to complete.');
                navigate(`/guest-post-finder/view/${data.id}/`);
            } else {
                if (data.reason === "max_limit_reached") {
                    errorAlertRef.current?.show('You have reached your maximum limit for this plan. Please Upgrade !');
                }
                else {
                    errorAlertRef.current?.show('Failed to fetch queries from backend API.');
                }
            }
        } catch (err) {
            errorAlertRef.current?.show('An error occurred while fetching GPF queries.');
        }
    }

    const handleProceedClick = async (e) => {
        const updatedLimit = Math.max(0, maxGpfAllowed - gpfQueriesGenerated);
        setLimit(updatedLimit); // Update limit
        setShowWarning(false); // Close the modal

        e.preventDefault();
        errorAlertRef.current?.close();
        // Check if the topic is empty
        if (!topic || !limit) {
            return;
        }
        try {
            const response = await makeApiRequest(
                `/api/frontend/post-guest-post-finder-query/`,
                'post',
                {
                    query: topic,
                    limit: updatedLimit
                }
            );

            const data = response.data;
            if (data.success) {
                localStorage.setItem('taskId', data.task_id);
                successAlertRef.current?.show(`Query submitted. It will take few minutes to complete.`);
                navigate(`/guest-post-finder/view/${data.id}/`);
            } else {
                if (data.reason === "max_limit_reached") {
                    errorAlertRef.current?.show('You have reached your maximum limit for this plan. Please Upgrade !');
                }
                else {
                    errorAlertRef.current?.show('Failed to fetch queries from backend API.');
                }
            }
        } catch (err) {
            errorAlertRef.current?.show('An error occurred while fetching gpf queries.');
        }
    };

    // ============================================================
    // --------------------- MAIN RENDER CODE ---------------------
    // ============================================================
    if (isError) {
        return (
            <section className="section">
                <div className="container">
                    <div className="box">
                        <h1 className="title has-text-centered">Find Blogs with Guest Post
                            Opportunities for your Topic/Niche</h1>
                        <p className="has-text-centered is-size-5">
                            Failed to load data. Please try again later.
                        </p>
                    </div>
                </div>
            </section>
        );
    } else {
        return (
            <>
                <Helmet>
                    <title>Create Guest Post Finder | Abun.com</title>
                    <meta
                        name="description"
                        content=" Discover guest post opportunities tailored to your keywords."
                    />
                </Helmet>
                <div className="guest-post-finder-container w-100">
                    <div className={""}>
                        {/* ------------ Guest Post Finder ------------ */}
                        <div className="mb-5 is-flex-wrap-wrap guest-header is-flex is-justify-content-space-between">
                            <div className={"is-flex is-justify-content-center is-flex-direction-column "}>
                                <h2>Guest Post Finder</h2>
                                <p className={"guest-p has-text-dark"}>Quickly find blogs that accept guest posts in your niche or topic.<br />
                                    Use this tool to discover real websites where you can contribute content, build authority, and earn high-quality backlinks.
                                </p>
                            </div>
                            <span className="mt-3">
                                {pageData.guest_post_finder_remaining} Guest Post Finder remaining. <Link to={pageURL["subscriptionCredit"]} className="is-text has-text-black is-underlined" >View Credits</Link>
                            </span>
                        </div>

                        <div className="tabs is-medium" style={{ scrollbarWidth: 'none' }}>
                            <ul>
                                <li className={activeTab === "guest-post" ? "is-active" : ""}>
                                    <a onClick={() => setActiveTab("guest-post")}>Guest Post Finder</a>
                                </li>
                                <li className={activeTab === "guest-projects" ? "is-active" : ""}>
                                    <a onClick={() => setActiveTab("guest-projects")}>Projects</a>
                                </li>
                            </ul>
                        </div>

                        {activeTab === "guest-post" &&
                            <>
                                <div className={" is-flex is-align-items-center is-flex-direction-column  guest-post-container"}>
                                    <h3 >Let's find the Guest posts for target keyword</h3>
                                    <hr />
                                    <form className="guest-form w-100"
                                        onSubmit={(e) => {
                                            e.preventDefault(); // Prevent the default form submission behavior                                    
                                            const remaining = maxGpfAllowed - gpfQueriesGenerated;
                                            if (limit > remaining) {
                                                setShowWarning(true);
                                            } else {
                                                handleSubmit(e);
                                            }
                                        }}>
                                        <div className="field">
                                            <label className="ca-label has-text-black label">Your Target Keyword</label>
                                            <div className="control">
                                                <input
                                                    className="input"
                                                    type="text"
                                                    placeholder="Enter topic"
                                                    value={topic}
                                                    onChange={(e) => setTopic(e.target.value)}
                                                    // style={{ marginTop: '3px', width: "60%" }}
                                                    required
                                                />
                                            </div>
                                        </div>
                                        <div className="field" style={{ display: 'none' }}>
                                            <label className="ca-label has-text-black label">How many opportunities do you want to
                                                find? (It should be between 10-400)</label>
                                            {/* <span className="text-muted"> </span><br /> */}
                                            <input
                                                className="ca-input input mb-3"
                                                type="number"
                                                placeholder="Enter limit"
                                                value={limit}
                                                onChange={(e) => {
                                                    const value = parseInt(e.target.value);
                                                    if (isNaN(value) || (value > 0 && value <= 400)) {
                                                        setLimit(value);
                                                    }
                                                }}
                                                min={1}
                                                max={400}
                                                style={{ marginTop: '3px', textAlign: 'center', width: '30%' }}
                                                required
                                            />
                                        </div>
                                        <button type={"submit"} className="mt-2 button is-responsive is-link" style={{ width: 'fit-content' }}
                                            disabled={isFetching || !pageData.verified || queries.some((query) => query.query === topic && query.is_processing) || (topic.trim() === "")}>
                                            PROCEED <FontAwesomeIcon icon={faArrowRight} className="ml-2 is-size-6" />
                                        </button>
                                    </form>
                                </div>
                                <SuccessAlert ref={successAlertRef} />
                                <ErrorAlert ref={errorAlertRef} />

                                {/************************* Warning Alert ***************************/}
                                <AbunModal
                                    active={showWarning}
                                    headerText={"Usage Limit Alert"}
                                    closeable={false}
                                    hideModal={() => setShowWarning(false)}>
                                    <div>
                                        <p className={"has-text-centered mt-4"}>
                                            You have only {Math.max(0, maxGpfAllowed - gpfQueriesGenerated)} searches left in your account.<br />Do you want to proceed?
                                        </p>
                                        <div className={"mt-6 has-text-centered is-flex is-justify-content-center is-align-items-center"}>
                                            <AbunButton type={"danger"} clickHandler={handleProceedClick} disabled={Math.max(0, maxGpfAllowed - gpfQueriesGenerated) === 0}>PROCEED</AbunButton>
                                            <AbunButton type={"primary"} className={"ml-4"} clickHandler={() => setShowWarning(false)}>CANCEL</AbunButton>
                                        </div>
                                    </div>
                                </AbunModal>
                            </>
                        }

                        {activeTab === "guest-projects" &&
                            <GuestPostFinderTable />
                        }

                    </div>
                </div>
            </>
        );
    }
}

export default GuestPostFinder;
