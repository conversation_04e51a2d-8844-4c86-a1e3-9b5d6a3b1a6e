import { useQuery } from "@tanstack/react-query";
import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import { useEffect, useRef, useState } from "react";
import { Helmet } from 'react-helmet';
import { useNavigate } from "react-router-dom";
import AbunLoader from '../../components/AbunLoader/AbunLoader';
import AbunTable from "../../components/AbunTable/AbunTable";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import LinkButton from "../../components/LinkButton/LinkButton";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { getGuestPostFinderQueries } from "../../utils/api";
import './GuestPostFinder.min.css';


interface Query {
    guest_project_id: string,
    query: string,
    limit: number,
    is_processing: boolean,
    created_at: Date
}

function GuestPostFinderTable() {
    // ---------------------- NON STATE CONSTANTS ----------------------
    const pageSizes = [15, 25, 50, 100];

    // -------------------------- STATES --------------------------
    const [queries, setQueries] = useState<Array<Query>>([]);
    const navigate = useNavigate();

    // -------------------------- QUERIES --------------------------
    const {
        isLoading,
        isError,
        data,
        refetch
    } = useQuery({
        queryKey: ['getGuestPostFinderQueries'],
        queryFn: getGuestPostFinderQueries,
        refetchOnWindowFocus: false
    });


    // -------------------------- REFS --------------------------
    const errorAlertRef = useRef<any>(null);
    const successAlertRef = useRef<any>(null);

    // ---------------------- EFFECTS ----------------------
    useEffect(() => {
        if (data) {
            setQueries(data['data']['queries']);
        }
    }, [data]);

    // ---------------------- TABLE COLUMN DEFS ----------------------
    const columnHelper = createColumnHelper<Query>();
    const columnDefs: ColumnDef<any, any>[] = [

        columnHelper.accessor((row: Query) => row.query, {
            id: 'topic',
            header: "Topic",
            cell: (props) => {
                const handleClick = () => {
                    navigate(`/guest-post-finder/view/${props.row.original.guest_project_id}/`);
                };

                return (
                    <span onClick={handleClick} style={{ cursor: 'pointer' }}>
                        {props.row.original.query}
                    </span>
                );
            },
            enableGlobalFilter: true
        }),
        columnHelper.accessor((row: Query) => row.limit, {
            id: 'posts',
            header: "Posts",
            cell: info => info.getValue(),
            enableGlobalFilter: true,
            meta: {
                align: 'center'
            }
        }),
        columnHelper.accessor((row: Query) => row.created_at, {
            id: 'date',
            header: "Created On",
            cell: cellProps => {
                let selectedDate: any = cellProps.row.original.created_at;

                // defining date showing context
                const getRelativeTime = (dateString: string) => {
                    const createdDateObj = new Date(dateString);
                    const now = new Date();
                    const timeDiff = now.getTime() - createdDateObj.getTime();

                    // Handle future dates
                    if (timeDiff < 0) {
                        return "just now";
                    }

                    const seconds = Math.floor(timeDiff / 1000);
                    const minutes = Math.floor(seconds / 60);
                    const hours = Math.floor(minutes / 60);
                    const days = Math.floor(hours / 24);

                    // Check conditions in ascending order of time units
                    if (seconds < 60) {
                        return "just now";
                    }

                    if (minutes < 60) {
                        return minutes === 1 ? "a minute ago" : `${minutes} minutes ago`;
                    }

                    if (hours < 24) {
                        return hours === 1 ? "an hour ago" : `${hours} hours ago`;
                    }

                    if (days > 30) {
                        const day = createdDateObj.getDate();
                        const month = createdDateObj.toLocaleString('default', { month: 'short' });
                        const year = createdDateObj.getFullYear().toString().slice(-2);
                        return `${day} ${month}, ${year}`;
                    }

                    return days === 1 ? "a day ago" : `${days} days ago`;
                };

                return getRelativeTime(selectedDate);
            },
            meta: {
                align: 'center'
            }
        }),
        columnHelper.display({
            id: 'view',
            header: () => "View",
            cell: cellProps => {
                let processing: boolean = cellProps.row.original.is_processing;
                let limit = cellProps.row.original.limit
                if (processing) {

                    return (
                        <LinkButton
                            linkTo={`/guest-post-finder/view/${cellProps.row.original.guest_project_id}/`}
                            text={"Generating..."}
                            type={"primary"}
                            width={"100px"}
                            outlined={true}
                            additionalClassList={["is-small", "more-rounded-borders"]}
                        />
                    );
                }
                else {
                    return (
                        <LinkButton
                            linkTo={`/guest-post-finder/view/${cellProps.row.original.guest_project_id}/`}
                            text={"View"}
                            type={"success"}
                            width={"100px"}
                            additionalClassList={["is-small", "more-rounded-borders"]}
                            disabled={limit === 0}
                        />
                    );
                }
            },
            enableGlobalFilter: false,
            meta: {
                align: 'center'
            }
        }),
    ]
    function goBack() {
        navigate(-1);
    }

    if (isLoading){
        return (
            <div className={"w-100 is-flex is-justify-content-center is-align-items-center"}>
                <AbunLoader show={isLoading} height="20vh" />
            </div>
        )
    }

    return <>
        <div className="guest-post-table w-100">
            <Helmet>
                <title>Guest Post Finder Projects | Abun.com</title>
                <meta
                  name="description"
                  content="Manage and track your guest posting outreach campaigns."
                />
            </Helmet>
            <div className={""}>
                {/* <span className={"back-btn"} style={{ cursor: "pointer" }} onClick={goBack}>
                    <svg className="back-btn" width="30" height="24" viewBox="0 0 30 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M26.0435 12.0003H2.82031M2.82031 12.0003L12.8382 1.98242M2.82031 12.0003L12.8382 22.0181" stroke="black" stroke-opacity="0.5" stroke-width="3" />
                    </svg>
                </span> */}
                <div className="guest-project-abun-table">
                    <AbunTable tableContentName={"Guest Post Queries"}
                        tableData={queries}
                        columnDefs={columnDefs}
                        pageSizes={pageSizes}
                        initialPageSize={pageSizes[0]}
                        noDataText={"No Queries data available."}
                        searchboxPlaceholderText={"Search query..."}
                    />
                </div>
                <SuccessAlert ref={successAlertRef} />
                <ErrorAlert ref={errorAlertRef} />

            </div>
        </div>
    </>
}

export default GuestPostFinderTable;