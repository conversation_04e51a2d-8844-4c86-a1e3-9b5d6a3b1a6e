// GuestPostFinder.scss
@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/elements/container";
@import 'bulma/sass/form/_all';
@import "bulma/sass/components/tabs";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";

@import "../../assets/bulma-overrides/bulmaOverrides";

.guest-post-table {
    font-family: $primary-font;
 }

.guest-project-abun-table {
    .abun-table-responsive {

        tbody {
            color: #000;
        }
    }
}

.guest-post-container{
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    width: fit-content;

    .ca-label {
        font-size: 18px;
        font-weight: 500;
    }
    
    .ca-input::placeholder {
        @media(max-width:480px) {
            font-size: 13px;
        }
    }
    
    .ca-input {
        font-size: 14px;
    }

     h3 {
        font-size: 1.4rem;
        font-weight: 600;
        padding: 1rem;
        align-self: start;

    }

    hr {
        background: #e7e7e7;
        height: 1px;
        margin: 0;
        width: 100%;
    }

}

.guest-post-finder-container{
    font-family: $primary-font !important;

    .guest-header{

        h2{
            font-family: $primary-font !important;
            font-size: 2rem !important;
            font-weight: 600 !important;
            margin-bottom: 4px;
        }
    
        .guest-p{
            // color: rgba(0, 0, 0, .698);
            font-family: $secondary-font !important;
            font-size: 1.125rem !important;
        }
    }

    .not-found-p{
        font-family: $secondary-font;
    }

    .guest-form{
        justify-items: center;
        display: flex;
        flex-direction: column;
        padding: 1rem;
        padding-left: 1.6rem;
    }
}

.left-arrow {
  display: inline-block;
  transform: scaleX(-1);
}

.guest-post-view-table{
    h1{
        font-size: 2em;
        font-weight: normal;
        text-align: center;
        font-family: $primary-font !important;
    }
}