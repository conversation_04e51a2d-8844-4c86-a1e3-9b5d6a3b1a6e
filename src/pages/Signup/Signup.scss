@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

// bulma overrides
$card-content-padding: 4rem;
$input-shadow: unset;
$input-border-color: $grey-dark;
$input-color: $grey-darker;
$input-placeholder-color: $grey-darker;

@import "bulma/sass/utilities/all";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/button";
@import "bulma/sass/components/card";
@import "bulma/sass/form/all";

.signup-container *{
  font-family: $primary-font;
}

.signup-container {
  align-content: center;
  height: 100%;
  min-height: 100vh;

  .heading-text {
    font-size: 3rem;
    line-height: 1.2em;
    font-weight: 700;
    color: $black;
    text-align: center;
    letter-spacing: -1px;

    @include until($tablet) {
      font-size: 2rem;
    }

    @media screen and (max-width: 1560px) {
      font-size: 2.5rem;
    }
  }

  .signup-card {
    width: 100%;
    border-radius: 16px;
    box-shadow: 0 8px 17px #00000029;

    @media (max-width: 1600px) {
      zoom: 0.75;
    }

    @media (max-width:850px) {
      flex-direction: column;
      justify-self: center;
    }

    @media (max-width:550px) {
      width: 95%;
    }

    .card-content {
      padding: 2rem;

      .content{

        .field input{
          max-width: 450px;
        }

        .terms-condition-text {
          font-size: 13px;
        }

        .sign-up-btn{
          width: 100%;
          max-width: 297px;
          color: #000 !important;
          background: #fac44b !important;
          border-radius: 15px;
          border: none !important;
          padding: 2rem !important;

          &:hover {
            background: #f3c254 !important;
          }
        }

        .eye-icon {
          position: absolute!important;
          top: 10px!important;
          bottom: 0!important;
          margin: auto -30px!important;
        }

        p {
          color: #5D5E98;
        }
      }

      .google-separator{

        .google-button{
          width: 100%;
          max-width: 380px;
          justify-self: center;
          padding: 1.65rem 1rem !important;
          justify-content: space-between;
          transition: background-color 0.2s ease, transform 0.2s ease, box-shadow 0.3s ease;

          &:hover{
            background-color: #f9f9f9;
            border-color: #dbdbdb !important;
          }
        }

        .separator {
          display: flex;
          align-items: center;
          justify-content: center;

          .line {
            width: 100%;
            max-width: 187px;
            height: 1px;
            background: #dbdbdb;
          }

          .or {
            margin: 0 0.5rem;
            font-size: 1.2rem;
            color: #7a7a7a;
          }
        }
      }
    }


    .right-container{
      border-radius: 16px !important;
      background: #FAF2E5 !important;
      padding: 2rem;

      .sub-title{
        font-size: 2rem;
        font-weight: 500;
        line-height: 1.2;
        color: $primary;

      }

      .feature-columns{
        justify-items: center;}

        .feature-list li{
          font-size: 1.45rem;
          margin-top: 10px;
          font-weight: 400;
          line-height: 1.3;
        }
    }
  }

  /*@media screen and (max-width: 1300px) {
    width: 40%;
    right: 30%;
  }

  @media screen and (max-width: 1160px) {
    width: 50%;
    right: 25%;
  }

  @media screen and (max-width: 925px) {
    width: 60%;
    right: 20%;
  }

  @media screen and (max-width: 770px) {
    width: 80%;
    right: 10%;
  }

  @media screen and (max-width: 580px) {
    width: 90%;
    right: 5%;
  }

  @media screen and (max-width: 512px) {
    zoom: 0.8;
  }

  @media screen and (max-height: 900px) {
    zoom: 0.75;
  }

  @media screen and (max-height: 680px) {
    zoom: 0.7;
  }
  */



}


.show-error-message-below {
  position: relative !important;

  &::after {
    content: attr(data-err-msg);
    position: absolute;
    bottom: -25px;
    left: 0;
    color: $danger;
    font-size: .85rem;

    @media (min-width:1560px) {
      left: 12px;
      
    }

    @media (max-width:850px) and (min-width:470px) {
      left: 57px;
    }
  }
}

.show-error-message-above {
  position: relative !important;

  &::after {
    content: attr(data-err-msg);
    position: absolute;
    bottom: 3.5rem;
    left: 0;
    color: #e12047;
    font-size: 1rem;
    width: 100%;
    max-width: 456px;
    text-align: center;

    @media (max-width:850px) and (min-width:470px) {
      left: 37px;
    }
  }
  
   // If data-err-msg is not empty, apply margin-top to input
   &[data-err-msg]:not([data-err-msg=""]) {
    input {
      margin-top: 26px;
    }
  }
}