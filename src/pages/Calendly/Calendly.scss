/* Modal CSS */

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    /* Slightly darker semi-transparent background */
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1002;
    /* Ensure it’s above other content */
    overflow: hidden;
}

/* Modal Content */
.modal-content {
    position: relative;
    background: white;
    padding: 20px;
    border-radius: 8px;
    position: relative;
    width: 90%;
    /* Increased max-width */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-height: 90vh;
    /* Ensures it doesn’t overflow vertically */
    transition: all 0.3s ease-in-out;
    /* Adds smooth transition */
    transform: scale(0.9);
}

/* To enable smooth zoom-in effect on modal open */
.modal-overlay.show .modal-content {
    transform: scale(1);
    /* Zooms the modal in smoothly */
}

.calendly-inline-widget {
    overflow: hidden;
}