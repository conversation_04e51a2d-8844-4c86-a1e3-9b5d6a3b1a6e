import { useQuery } from '@tanstack/react-query';
import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import { useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import AbunLoader from '../../components/AbunLoader/AbunLoader';
import AbunTable from "../../components/AbunTable/AbunTable";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { withAdminAndProductionCheck } from "../../utils/adminAndProductionCheck";
import { getAICalculatorsQuery } from "../../utils/api";
import './AICalculatorTable.min.css';

interface AICalculator {
    calculator_id: string;
    calc_type: string;
    created_on: string;
    status: string; // 'processing', 'completed', 'failed', etc.
}

function AICalculatorTable() {
    // --------------------------- CONSTANTS ---------------------------
    const pageSizes = [5, 10, 15, 30, 50, 100, 500];

    // --------------------------- STATES ---------------------------
    const [calculators, setCalculators] = useState<AICalculator[]>([]);

    // --------------------------- REFS ---------------------------
    const errorAlertRef = useRef<any>(null);
    const successAlertRef = useRef<any>(null);

    // --------------------------- HOOKS ---------------------------
    const navigate = useNavigate();

    // --------------------------- QUERIES ---------------------------
    const {
        isLoading,
        refetch
    } = useQuery({
        ...getAICalculatorsQuery(),
        refetchInterval: 5000, // Refetch every 5 seconds to update processing status
        onSuccess: (response: any) => {
            if (response.data) {
                setCalculators(response.data.table_data);
            }
        },
        onError: () => {
            errorAlertRef.current?.show("Failed to load calculators");
        }
    });

    // --------------------------- HANDLERS ---------------------------
    const handleCreateNewCalculator = () => {
        navigate("/ai-calculator-generator");
    };

    // --------------------------- TABLE CONFIG ---------------------------
    const columnHelper = createColumnHelper<AICalculator>();

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    };

    const getStatusBadge = (status?: string) => {
        if (!status) return null;

        const statusConfig = {
            'processing': { text: 'Processing', class: 'is-warning' },
            'completed': { text: 'Completed', class: 'is-success' },
            'failed': { text: 'Failed', class: 'is-danger' },
            'submitted': { text: 'Processing', class: 'is-warning' }
        };

        const config = statusConfig[status as keyof typeof statusConfig] || { text: status, class: 'is-light' };

        return (
            <span className={`tag ${config.class}`}>
                {config.text}
            </span>
        );
    };

    const columnDefs: ColumnDef<AICalculator, any>[] = [
        columnHelper.accessor('calc_type', {
            header: "Calculator Name",
            cell: (info) => info.getValue()
        }),
        columnHelper.accessor('status', {
            header: "Status",
            cell: (info) => getStatusBadge(info.getValue())
        }),
        columnHelper.accessor('created_on', {
            header: "Created On",
            cell: (info) => formatDate(info.getValue())
        })
    ];

    return (
        <div className="ai-calculator-table-tp-container">
            <div className="table-container w-100">
                {isLoading ? (
                    <div className={"w-100 is-flex is-justify-content-center is-align-items-center"}>
                        <AbunLoader show={isLoading} height="20vh" />
                    </div>
                ) : (
                    <AbunTable
                        tableContentName="AI Calculators"
                        tableData={calculators}
                        columnDefs={columnDefs}
                        pageSizes={pageSizes}
                        initialPageSize={pageSizes[1]}
                        noDataText="No Calculators Found."
                        handleRowClick={(row) => {
                            navigate("/ai-calculator-generator", {
                                state: {
                                    calculatorId: row.calculator_id,
                                    calculatorType: row.calc_type
                                }
                            });
                        }}
                    />
                )}
            </div>

            <ErrorAlert ref={errorAlertRef} />
            <SuccessAlert ref={successAlertRef} />
        </div>
    );
};

export default withAdminAndProductionCheck(AICalculatorTable);
