$second-breakpoint: 1360px;

@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";
@import "../../assets/custom-global-styles/customGlobablStyles";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/components/card";
@import "bulma/sass/elements/button";
@import "bulma/sass/elements/tag";
@import "bulma/sass/elements/progress";
@import "bulma/sass/components/tabs";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";

@import "../../assets/bulma-overrides/bulmaOverrides";

.offer-card {
  width: fit-content;
  background: white;
  border-radius: 200px;
  border: 2px solid #000000;
  box-shadow: 0px 4px 1px 0px #000000;
  padding: 10px 30px;
  margin: auto;
  margin-bottom: 50px;

  h2 {
    color: #7a63df;
    font-family: $primary-font;
    font-size: 20px;
    font-weight: 600;
    letter-spacing: -0.2px;
  }
}

.signup-plan-selection-container * {
  font-family: $primary-font;
}

.subscription-page-header {

  .tabs {
    background: #F2F3F4;
    width: fit-content;
    margin: 0 auto;
    border-radius: 10px;
    padding: 10px;

    @media (max-width: 768px) {
      padding: 8px;
      font-size: 0.9rem;
    }

    @media (max-width: 480px) {
      padding: 6px;
      font-size: 0.85rem;
    }

    ul {
      border-bottom: none;
      gap: 8px;
    }

    li {

      a {
        border-bottom: none;
        border-radius: 9px;
        font-size: 18px;

        @media (max-width: 768px) {
          font-size: 16px;
          padding: 0.4em 0.8em;
        }

        @media (max-width: 480px) {
          font-size: 14px;
          padding: 0.3em 0.6em;
        }

        &:hover {
          background-color: #2E64FE;
          color: $white;
        }
      }

      &.is-active a {
        background-color: #2E64FE;
        color: $white;
      }
    }
  }

}

.down-plan-card {

  button {
    border-radius: 50px;
    padding: 10px 20px;
    width: 180px;
    color: #000;
    border: 1px solid #000 !important;
    background-color: #fff;
    transition: transform 0.4s ease;

    &:hover {
      background-color: #2E64FE !important;
      color: #fff !important;
      border: none !important;
      transform: translateY(-5px);
    }

  }

  @media (max-width: 768px) {
    button {
      width: 160px;
      padding: 8px 16px;
      font-size: 0.9rem;
    }
  }

  @media (max-width: 480px) {
    button {
      width: 140px;
      padding: 6px 12px;
      font-size: 0.85rem;
    }
  }
}

.subscription-manager-plan-cards-container-new-pricing {
  width: 95%;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  grid-row-gap: 2em;
  grid-column-gap: 1.5em;
  margin-block: 1rem;
  font-family: $primary-font;
  justify-content: center;

  @media (max-width: 1200px) {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    grid-column-gap: 1rem;
    row-gap: 1.5rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    width: 100%;
    padding: 0 1rem;
    row-gap: 1rem;
  }

  @media (max-width: 480px) {
    padding: 0 0.5rem;
  }

  .plan-card {
    display: flex;
    flex-direction: column;
    gap: 10px;
    background-color: $white;
    border: 2.75px solid #000;
    border-radius: 0.625rem;
    height: 100%;
    padding: 1.25rem;
    margin: 0; // Remove any default margins

    @media (max-width: 1200px) {
      padding: 1rem;
    }

    @media (max-width: 768px) {
      padding: 1rem;
      margin: 0 !important; // Override any inherited margins
    }

    @media (max-width: 480px) {
      padding: 0.75rem;
    }

    // Ensure the main content area takes up available space
    .is-flex.is-flex-direction-column {
      height: 100%;
      display: flex !important;
      flex-direction: column !important;

      // Push the button container to the bottom
      >div:last-child {
        margin-top: auto;
        padding-top: 1rem;
      }
    }

    .plan-details--buy-button {
      background-color: #ffffff;
      border-radius: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px 20px;
      border: 1px solid #000000 !important;
      width: 60%;
      height: auto;
      margin: 1em auto;
      color: $black !important;
      cursor: pointer;
      transition: transform 0.4s ease;
      font-size: 1rem;

      @media (max-width: 768px) {
        width: 80%;
        padding: 12px 24px;
        font-size: 0.95rem;
      }

      @media (max-width: 480px) {
        width: 90%;
        padding: 10px 20px;
        font-size: 0.9rem;
      }

      &:disabled {
        filter: grayscale(0.8);
        cursor: not-allowed;
        color: $black;
      }

      &:hover {
        background-color: #2E64FE !important;
        color: #fff !important;
        border: none !important;
        transform: translateY(-5px);
      }
    }

    .pricing-gap {
      gap: 1em;
      white-space: nowrap;

      @media (min-width: 1355px) and (max-width: 1374px) {
        gap: 4px;
      }
    }

    @include until($second-breakpoint) {
      padding: 1rem;
    }

    // Remove overlapping margins for better mobile experience
    &:first-child,
    &:nth-child(2),
    &:nth-child(3),
    &:last-child {
      z-index: 1;
      margin-left: 0;

      @media (min-width: 1201px) {

        // Only apply overlapping on larger screens where it looks good
        &:nth-child(2) {
          margin-left: -1em;
          z-index: 2;
        }

        &:nth-child(3) {
          margin-left: -1em;
          z-index: 3;
        }

        &:last-child {
          margin-left: -1em;
          z-index: 5;
        }
      }
    }

    hr {
      margin: 1.25rem 0;
      border: 0;
      border-top: 1px solid #e7e7e7;
      width: 80%;
    }

    .plan-name {
      font-size: 2.5rem;
      font-weight: 700;
      line-height: 1.1em;
      letter-spacing: -1px;
      color: $black;

      @media (max-width: 768px) {
        font-size: 2rem;
      }

      @media (max-width: 480px) {
        font-size: 1.75rem;
      }
    }

    .cancel-pricing {
      font-weight: 400;
      font-size: 1.875rem;
      line-height: 1.1em;
      color: #8E8E90;
      text-decoration: line-through;
      white-space: nowrap;

      @media (max-width: 768px) {
        font-size: 1.5rem;
      }

      @media (max-width: 480px) {
        font-size: 1.25rem;
      }
    }

    .pricing {
      font-weight: 700;
      font-size: 3.75rem;
      color: $black;
      line-height: 1.1em;

      @media (max-width: 768px) {
        font-size: 2.5rem;
      }

      @media (max-width: 480px) {
        font-size: 2rem;
      }

      .pricing-suffix {
        font-size: 1.125rem;
        font-weight: 400;
        color: $black;

        @media (max-width: 768px) {
          font-size: 1rem;
        }

        @media (max-width: 480px) {
          font-size: 0.875rem;
        }
      }
    }

    .plan-details {
      display: flex;
      flex-direction: column;
      gap: 0.3em;
      margin-top: -16px;

      .plan-article-gradient {
        width: fit-content;
        background: repeating-linear-gradient(to right, #d1cfd7, #D1CFD7 2px, transparent 0px, transparent 5px),
          linear-gradient(to top, #C3F2CE 0%, #c3f2ce 48%, transparent 73%, transparent 100%);
        background-size: 100% 2.6px, 100% 100%;
        background-repeat: no-repeat;
        line-height: 1.1;
        background-position: 0px 114%, 0px 6px;
      }

      .plan-details--item {
        font-size: 1rem;
        padding-bottom: 5px;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        text-wrap: nowrap;

        //1650px;
        @include until(1650px) {
          font-size: 0.85rem;
        }

        span.icon {
          margin-right: 2px;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          max-width: 1.375rem;
          max-height: 1.375rem;
        }
      }

      // Fix for Coming Soon badges alignment
      ul {
        li {
          display: flex;
          align-items: center;
          white-space: nowrap;

          // Ensure proper alignment for Coming Soon items
          .is-flex.is-align-items-center.is-justify-content-space-between {
            width: 100%;
            flex-wrap: nowrap;

            span:first-child {
              display: flex;
              align-items: center;
              flex-shrink: 0;
              white-space: nowrap;
            }

            .tag {
              flex-shrink: 0;
              margin-left: auto;

              // Large screens (1200px and above)
              @media (min-width: 1200px) {
                font-size: 0.6rem !important;
                padding: 0.18em 0.45em !important;
              }

              // Medium screens (768px to 1199px)
              @media (min-width: 768px) and (max-width: 1199px) {
                font-size: 0.58rem !important;
                padding: 0.16em 0.4em !important;
              }

              // Small screens (480px to 767px)
              @media (min-width: 480px) and (max-width: 767px) {
                font-size: 0.55rem !important;
                padding: 0.14em 0.35em !important;
              }

              // Extra small screens (below 480px)
              @media (max-width: 479px) {
                font-size: 0.52rem !important;
                padding: 0.12em 0.3em !important;
              }
            }
          }

          // For non-Coming Soon items
          .is-flex.is-align-items-center:not(.is-justify-content-space-between) {
            white-space: nowrap;
          }
        }
      }
    }

    &.popular {
      position: relative;
      border-radius: 0.625rem;
      border: 5px solid #2E64FE;
      background-color: #FFFFE6;

      @include until($second-breakpoint) {
        transform: scale(1);
      }

      .pricing {
        color: $black !important;
      }

      .plan-details--buy-button {
        background: #2E64FE;
        color: #fff !important;
        transition: transform 0.4s ease;
        border: none !important;

        &:hover {
          transform: translateY(-5px);
        }
      }
    }
  }

  // Removed old mobile styles - now handled in main .plan-card selector
}

// .subscription-manager-plan-cards-container {
//   width: 95%;
//   display: grid;
//   grid-template-columns: repeat(auto-fit, minmax(230px, 1fr));
//   grid-row-gap: 2em;
//   margin-block: 1rem;
//   font-family: $primary-font;

//   @include until($second-breakpoint) {
//     row-gap: 1rem;
//     column-gap: 1rem;
//   }

//   .plan-card {
//     display: flex;
//     flex-direction: column;
//     // justify-content: space-between;

//     background-color: $white;
//     border: 3.25px solid #000;
//     border-radius: 2rem;
//     height: 52rem;
//     padding: 1.25rem;
//     zoom: 0.85;

//     .plan-details--buy-button {
//       background-color: #ffffff;
//       border-radius: 50px;
//       display: flex;
//       align-items: center;
//       justify-content: center;
//       padding: 10px 20px;
//       border: 1px solid #000000 !important;
//       width: 60%;
//       height: auto;
//       box-shadow: 1px 2px 0px 0px #000000;
//       margin: 1em auto;
//       color: $black !important;
//       cursor: pointer;
//       transition: all 0.3s ease;

//       &:disabled {
//         filter: grayscale(0.8);
//         cursor: not-allowed;
//         color: $black;
//       }

//       &:hover {
//         color: $black;
//         transform: translate(-0.25rem, -0.25rem);
//         background: #fac44b !important;
//         box-shadow: 0.25rem 0.25rem #000000;
//       }
//     }

//     .pricing-gap {
//       gap: 1em;
//       white-space: nowrap;

//       @media (min-width: 1355px) and (max-width: 1374px) {
//         gap: 4px;
//       }
//     }

//     @include until($second-breakpoint) {
//       padding: 1rem;
//     }

//     &:first-child {
//       z-index: 1;

//       @include until($second-breakpoint) {
//         border: 4px solid #000;
//         border-radius: 2rem;
//       }
//     }

//     &:nth-child(2) {
//       z-index: 2;
//       margin-left: -1em;

//       @include until($second-breakpoint) {
//         border: 4px solid #000;
//         border-radius: 2rem;
//       }
//     }

//     &:nth-child(3) {
//       z-index: 3;
//       margin-left: -1em;

//       @include until($second-breakpoint) {
//         border: 4px solid #000;
//         border-radius: 2rem;
//       }
//     }

//     &:last-child {
//       z-index: 5;
//       margin-left: -1em;

//       @include until($second-breakpoint) {
//         border: 4px solid #000;
//         border-radius: 2rem;
//       }
//     }

//     hr {
//       margin: 0.5rem auto;
//       border: 0;
//       border-top: 1px solid #ddddddaa;
//       width: 80%;
//     }

//     .plan-name {
//       font-size: 2rem;
//       font-weight: 900;
//       color: $black;
//     }

//     .cancel-pricing {
//       font-weight: 500;
//       font-size: 1.7rem;
//       color: $grey-dark;
//       text-decoration: line-through;
//       white-space: nowrap;

//       @media (max-width: 600px) {
//         font-size: 1.45rem;
//       }
//     }

//     .pricing {
//       font-weight: 700;
//       font-size: 3.1rem;
//       color: $black;

//       .pricing-suffix {
//         margin-left: 0.5rem;
//         font-size: 1.125rem;
//         font-weight: 900;
//         color: $black;
//       }
//     }

//     .plan-details {
//       display: flex;
//       flex-direction: column;
//       gap: 0.3em;

//       .plan-details--item {
//         font-size: 1rem;
//         padding-bottom: 5px;
//         display: flex;
//         flex-direction: row;
//         justify-content: flex-start;
//         align-items: center;
//         text-wrap: nowrap;

//         //1650px;
//         @include until(1650px) {
//           font-size: 0.85rem;
//         }

//         span.icon {
//           margin-right: 2px;
//           display: flex;
//           flex-direction: row;
//           justify-content: center;
//           align-items: center;
//           max-width: 1.375rem;
//           max-height: 1.375rem;
//         }
//       }
//     }

//     &.popular {
//       position: relative;
//       border-radius: 2rem;
//       background-color: #daffb5;
//       transform: scale(1.075);

//       @include until($second-breakpoint) {
//         transform: scale(1);
//       }

//       .pricing {
//         color: $black !important;
//       }

//       .plan-details--buy-button {
//         background: #f2ce40;

//         &:hover {
//           transform: translate(0.2rem, 0.2rem);
//           background-color: #ffffff !important;
//           color: #000000 !important;
//           border: 1px solid #000000;
//           box-shadow: 0.25rem 0.25rem #00000000;
//         }
//       }
//     }
//   }

//   @media (max-width: 768px) {
//     .plan-card {
//       margin-left: 0.3em !important;
//     }
//   }
// }

.popular {
  border-style: solid;
  border-color: $primary;
}

.stripe-portal-white-icon {
  filter: invert(100%) sepia(0%) saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%);
}

.usage-stats-section {
  font-family: $primary-font;
  width: 100%;

  .section-label {
    font-size: 2em;
    font-weight: bold;
    margin-bottom: 16px;
  }

  .usage-stats-container {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    grid-column-gap: 1em;
    grid-row-gap: 2em;
    justify-content: center;

    @media (max-width: 1200px) {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      grid-column-gap: 0.75em;
      grid-row-gap: 1.5em;
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      grid-column-gap: 0;
      grid-row-gap: 1rem;
      padding: 0 1rem;
    }

    @media (max-width: 480px) {
      padding: 0 0.5rem;
    }

    .usage-stats-item {
      display: block;
      /* background: white; */
      background-color: #fff;
      border-radius: 0.9rem;
      box-shadow: 0 0.5em 1em -0.125em #0000001a, 0 0 0 1px #00000005;
      color: #000;
      max-width: 100%;
      position: relative;
      padding: 1em;
      /* width: 0vw; */
      border: 1px solid black;

      .usage-stats-item--title {
        font-size: 1.2rem;
        font-weight: 700;
        margin-bottom: 1em;
      }

      .progress {
        margin-bottom: 1rem;
      }

      .usage-stats-item--info {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 3px;

        p {
          font-size: 0.9rem;
        }

        .usage-stats-item--value {
          img {
            font-size: 2.2rem;
          }
        }
      }
    }
  }
}

@media (max-width: 1540px) {
  .subscription-manager-plan-cards-container .plan-card .pricing {
    font-size: 2.21rem;
    white-space: nowrap;
  }
}

@media (max-width: 600px) {
  .subscription-manager-plan-cards-container .plan-card .pricing {
    font-size: 1.8rem;
  }
}

//  Free plan text when user switch to subscription plan
.switch-to-free-plan {
  width: auto !important;
}

.billing-container {
  .billing-header {
    h3 {
      font-size: 2em;
      font-weight: bold;
      margin-bottom: 16px;
      font-family: $primary-font;
    }

    .btns-stripe-portal {
      width: fit-content;
      background: #fac44b;
      font-family: $secondary-font;
      border-radius: 200px;
      border: 1px solid #000000;
      box-shadow: 0px 4px 1px 0px #000000;
      padding: 10px 30px;
    }

    .btns-stripe-portal:hover {
      background-color: #ffffff !important;
      color: #000000 !important;
      box-shadow: 0rem 0rem #00000000;
      cursor: pointer;
    }
  }

  // .table tbody {
  //   background-color: #f2f6fc;
  // }
}

@media (max-width: 493px) {
  .billing-header {
    flex-direction: column;
    margin-bottom: 1.5em;
  }
}

.header-text {
  color: #000;
  padding: 5px 0px;
}

// .cell-amount{
//   background-color: #f2f6fc ;
// }

// .table tbody{
//   background-color: #f2f6fc;
// }

.cancel-button {
  /* color: white; */
  color: black;
  background: white;
  border: 1px solid black;
  padding: 7px 30px;
  border-radius: 200px;
  font-family: $secondary-font;
}

.cancel-button:hover {
  // background: rgb(38, 36, 36);
  color: rgb(255, 0, 0);
  cursor: pointer;
}

.subscription-page-abun-table {

  td:hover {
    cursor: default !important;
  }

  .custom-abun-table {

    .abun-table-responsive {

      tbody {
        color: #000;

        tr td {
          text-decoration: none;
        }
      }
    }
  }
}


.subscription-cancel-btn {
  background-color: grey !important;
}