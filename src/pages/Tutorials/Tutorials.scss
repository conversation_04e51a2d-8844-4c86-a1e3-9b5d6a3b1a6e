@import "../../assets/fonts/customFonts";

.tutorial-container {
    text-align: center;

    .video-grid {
        display: grid;
        align-items: center;
        grid-template-columns: repeat(3, 1fr);
        column-gap: 2rem;
        row-gap: 3rem;

        .video-item {
            text-align: center;
            cursor: pointer;

            img {
                width: 100%;
                border-radius: 4px;
            }

        }
    }

    h2{
        font-family: $primary-font !important;
        font-size: 2rem !important;
        font-weight: 600 !important;
    }

    .abun-table-title{
         font-family: $primary-font !important;
    }
}

.iframe-container {

    iframe {
        width: 70vw;
        height: 75vh;
    }
}

@media screen and (max-width: 1400px) {
    .iframe-container iframe {
        width: 80vw;
        height: 65vh;
    }
}

@media screen and (max-width: 1000px) {
    .iframe-container iframe {
        width: 90vw;
        height: 50vh;
    }
}