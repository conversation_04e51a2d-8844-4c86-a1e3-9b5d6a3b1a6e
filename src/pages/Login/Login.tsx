import { faArrowRight, faStar } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import axios, { AxiosError } from 'axios';
import { useEffect, useRef, useState } from "react";
import { Icon } from 'react-icons-kit';
import { eye } from 'react-icons-kit/feather/eye';
import { eyeOff } from 'react-icons-kit/feather/eyeOff';
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import LocalIcon from "../../components/Icon/Icon";
import { storeAccessToken, storeRefreshToken } from "../../utils/jwt";
import { pageURL } from "../routes";
import './Login.min.css';

interface LoginResponse {
	access_token: string
	refresh_token: string
}

interface ErrorResponse {
	err_id?: string;
	message: string;
}

export default function Login() {
	// ------------------------- QUERY PARAMETERS -----------------------
	const [searchParams] = useSearchParams();
	const appSumoCode = searchParams.get("code");

	// ----------------------- STATES -----------------------
	const [loginUnderway, setLoginUnderway] = useState(false);
	const [googleLoginUnderWay, setGoogleLoginUnderWay] = useState(false);

	// ----------------------- REACT REFS -----------------------
	const emailElementRef = useRef<HTMLInputElement>(null);
	const passwordElementRef = useRef<HTMLInputElement>(null);
	const errorMessageTextRef = useRef<HTMLParagraphElement>(null);

	const navigate = useNavigate();

	useEffect(() => {
		document.title = "Login | Abun"
	}, []);

	/* -------------- validation functions -------------- */
	function emailIsValid(value: string): boolean {
		return /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(value);
	}

	function passwordIsValid(value: string): boolean {
		return value.length >= 6
	}

	/* ------------------------------------------------- */

	/**
	 * Shows error message below the submit button
	 * @param msg - error message to show
	 */
	function displayErrorMessage(msg: string) {
		if (errorMessageTextRef.current) {
			errorMessageTextRef.current.classList.remove('is-hidden');
			errorMessageTextRef.current.innerText = msg;
		}
	}

	/**
	 * Hides error message below the submit button
	 */
	function removeErrorMessage() {
		if (errorMessageTextRef.current) {
			errorMessageTextRef.current.classList.add('is-hidden');
			errorMessageTextRef.current.innerText = "";
		}
	}

	function submitLoginData() {
		// clear all error messages
		removeErrorMessage();

		const email: string = emailElementRef.current?.value || "";
		const password: string = passwordElementRef.current?.value || "";

		if (!emailIsValid(email) || !passwordIsValid(password)) {
			displayErrorMessage("Please enter a valid email id and password");
			return;
		}

		setLoginUnderway(true);

		/* submit data to server */
		axios({
			method: "post",
			url: process.env.REACT_APP_DRF_DOMAIN + "/api/frontend/login/",
			responseType: 'json',
			data: {
				email: email,
				password: password,
				appsumo_code: appSumoCode
			}
		}).then(response => {
			// Logged in successfully. Redirect to next page.
			let result: LoginResponse = response.data;
			let accessToken: string = result.access_token;
			let refreshToken: string = result.refresh_token;

			// check to prevent "undefined" string being stored as the value
			if (accessToken && refreshToken) {
				storeAccessToken(accessToken);
				storeRefreshToken(refreshToken);
				return navigate(pageURL['createArticle']);
			} else {
				setLoginUnderway(false);
				displayErrorMessage("We are currently facing some issues. Please try again later.");
			}

		}).catch(err => {
			setLoginUnderway(false);
			const axiosError = err as AxiosError<ErrorResponse>;
			if (axiosError.response?.status === 401) {
				const errId = axiosError.response.data?.message || "Unknown error occurred.";
				displayErrorMessage(errId);
			} else {
				console.error(err);
				displayErrorMessage("We are currently facing some issues. Please try again later.");
			}
		});
	}


	const [password, setPassword] = useState("");
	const [type, setType] = useState('password');
	const [icon, setIcon] = useState(eyeOff);
	const handleToggle = () => {
		if (type === 'password') {
			setIcon(eye);
			setType('text')
		} else {
			setIcon(eyeOff)
			setType('password')
		}
	}

	const stars = (
	  <div className="stars is-flex is-justify-content-center" style={{marginBottom:'0.4rem', gap:'5px'}}>
		{[...Array(5)].map((_, i) => (
		  <FontAwesomeIcon key={i} icon={faStar} style={{ fontSize: '1rem', color:'#FFD43B'}}/>
		))}
	  </div>
	);

	return (
		<div className="login-wrapper">
			<div className="login-container">

				<div className="card login-card mt-5">
					<div className="card-content">
	                    <div className="reviews-container mb-5 level is-flex">
						    <div className="review-box">
						      {stars}
						      <p className="quote" style={{color:'#A79577'}}>"biggest value bomb"</p>
						    </div>
    
						    <div className="review-box">
						      {stars}
						      <p className="quote" style={{color:'#A79577'}}>"helped us scale & grow"</p>
						    </div>
						    	
						    <div className="review-box">
						      {stars}
						      <p className="quote" style={{color:'#A79577'}}>"my entire team can't stop using it"</p>
						    </div>	
                        </div>
					    <h1 className="heading-text mb-5 epilogue has-text-black">Login to Abun.com</h1>
						<div className="google-separator">
						    <button 
							    className="button is-rounded google-button is-flex is-align-items-center mb-5"
								disabled={loginUnderway || googleLoginUnderWay}
								onClick={() => {
									setGoogleLoginUnderWay(true);

									axios({
										method: "get",
										url: process.env.REACT_APP_DRF_DOMAIN + "/api/fontend/google-signup-login-auth/?signup=false",
										responseType: 'json',
									}).then((response) => {
										if (response.status === 200) {
											const auth_url = response.data.authorization_endpoint;
											window.location.href = auth_url;
										} else {
											setGoogleLoginUnderWay(false);
										}
									})

								}}>
                              <span>
						    	<LocalIcon iconName='google' width="1.7em" style={{maxWidth:'1.7em'}}/>
                              </span>
                              <span className="has-text-black ml-2 has-text-weight-normal is-size-5" style={{marginRight:'3rem'}}>Continue with Google</span>
                            </button>
    
						    {/* Separator */}
                            <div className="separator mb-5">
                              <span className="line"></span>
                              <span className="or">OR</span>
                              <span className="line"></span>
                            </div>
						</div>

						<div className="content has-text-centered"
							onKeyDown={e => {
								if (e.key === 'Enter') {
									submitLoginData();
								}
							}}>
							<p ref={errorMessageTextRef} className="has-text-danger is-small has-text-centered is-hidden error-msg mb-0"></p>

							{/* ------------------ EMAIL ID ------------------ */}
							<div className="field mt-2">
								<div className="control">
									<input ref={emailElementRef}
										className="input is-medium"
										type="email"
										name="password"
										placeholder="Email ID" />
								</div>
							</div>
							{/* ------------------ PASSWORD ------------------ */}
							<div className="field mt-5">
								<div className="control">
									<input ref={passwordElementRef}
										className="input is-medium"
										type={type}
										name="password"
										placeholder="Password"
										value={password}
										onChange={(e) => setPassword(e.target.value)}
										autoComplete="current-password" />
									<span className="eye-icon" onClick={handleToggle}>
										<Icon icon={icon} size={25} />
									</span>
								</div>
							</div>

							<p className="has-text-centered mt-5 terms-condition-text">
                                By signing up you agree to our <a href="https://abun.com/terms-conditions/" rel={"noreferrer"} target={"_blank"}>Terms & Conditions</a> and <a href="https://abun.com/privacy-policy/" rel={"noreferrer"} target={"_blank"}>Privacy Policy</a>.
                            </p>
							<button  className="button login-btn is-large mb-3 is-responsive mt-5"
                                disabled={loginUnderway || googleLoginUnderWay}
                                onClick={submitLoginData}>
                                    <span className="is-flex is-align-items-center">Proceed 
										{loginUnderway ?
										    <LocalIcon iconName={"spinner"} additionalClasses={["icon-white","ml-3"]} style={{filter:'invert(0)'}}/>
										:
										    <FontAwesomeIcon icon={faArrowRight} className="ml-2 is-size-6"/>}
									</span> 
                            </button>

				            <p className="has-text-white has-text-centered is-size-5 mt-5">
				            	<div className="user-login">
				            		<Link to={appSumoCode ? `${pageURL['appSumoSignUp']}?code=${appSumoCode}` : pageURL['signup']} className="has-text-black underline-hover">Create New Account</Link>
				            		<span className="has-text-black"><span className="divider">|</span></span>
				            		<Link to={pageURL['forgotPassword']} className="has-text-black underline-hover">Forgot Password?</Link>
				            	</div>
				            </p>

						</div>
					</div>
				</div>
			</div>
		</div>
	)
}
