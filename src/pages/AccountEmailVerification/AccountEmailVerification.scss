@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/components/card";

@import "../../assets/bulma-overrides/bulmaOverrides";


.account-email-verification-container {
  // justify-content: space-between;
  height: 100vh;
  font-family: $primary-font;
  background-image: linear-gradient(to bottom, #B5E9FF, #FFFFFF);

  .check-icon {
    height: 4rem;
    margin-inline: auto;
    font-size: 4rem;
    margin-bottom: 3rem;
  }

  .card-contain {
    background-color: #FFFFFF;
    color: #000000;
    border-radius: 16px !important;
    position: absolute;
    width: 90%;
    max-width: 800px;
    top: 50%;
    left: 50%;
    padding: 1.5rem;
    transform: translate(-50%, -50%);
    box-shadow: 0 0.5em 1em -0.125em rgba(0, 0, 0, 0.1), 0 0px 0 1px rgba(0, 0, 0, 0.02);
    display: flex;
    flex-direction: column;
    align-items: center;

    @include until(1300px) {
      top: 60%;
    }

  }

  h2 {
    font-size: 1.6rem;
  }

  p {
    font-size: 1rem;
  }


}

@media only screen and (max-width: 400px) {
  .btns a {
    font-size: 0.7rem;
  }
}


.account-email-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  font-family: $primary-font;
  background-image: linear-gradient(to bottom, #B5E9FF, #FFFFFF);

  .success-card-content {
    padding: 2rem 6rem;
    //min-width: 600px;
  }

  .card {
    border-radius: 16px;
  }
}