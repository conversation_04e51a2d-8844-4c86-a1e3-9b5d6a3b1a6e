import { useQuery } from "@tanstack/react-query";
import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
// import html2canvas from 'html2canvas';
// import { jsPDF } from 'jspdf';
// import { Download, Info } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";

import {
    Bar,
    BarChart,
    CartesianGrid,
    Cell,
    Label,
    LabelList,
    Tooltip as RechartsTooltip,
    ResponsiveContainer,
    TooltipProps,
    Treemap,
    XAxis,
    YAxis
} from "recharts";
import abunLogo from "../../assets/images/branding/abun_black_text_logo.webp";
import AbunButton from "../../components/AbunButton/AbunButton";
import AbunLoader from "../../components/AbunLoader/AbunLoader";
import AbunModal from "../../components/AbunModal/AbunModal";
import AbunTable from '../../components/AbunTable/AbunTable';
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import GenericButton from "../../components/GenericButton/GenericButton";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { fetchGSCKeywordsData2 } from "../../utils/api";
import './GSCAnalytics.min.css';

interface selectedDomainProps {
    selectedDomain?: string
    handleBackBtnClick: () => void
}

interface GSCKeyword {
    url: string
    device: string
    keyword: string
    clicks: number
    impressions: number
    ctr: number
    position: number
}

type WordStats = {
    name: string;
    size: number;
    totalClicks: number;
    totalImpressions: number;
    topKeywords: string[];
    keywordCount: number;
    topCount: number;
    color: string;
};

interface Props {
    data: GSCKeyword[]; // full GSC keyword data
}

const CustomTreemapContent = (props) => {
    const { x, y, width, height, name = "", color, size, sizeMarginTop = 4 } = props;

    if (!width || !height || !name) return null; // Prevent rendering empty blocks

    const padding = 5;
    const availableWidth = width - padding * 2;
    const availableHeight = height - padding * 2;

    const words = name.split(" ");
    const lines: string[] = [];
    let currentLine = "";

    words.forEach(word => {
        const testLine = currentLine ? `${currentLine} ${word}` : word;
        if (testLine.length * 6 < availableWidth) {
            currentLine = testLine;
        } else {
            lines.push(currentLine);
            currentLine = word;
        }
    });

    if (currentLine) lines.push(currentLine);

    const lineHeight = 14; // Spacing between lines
    const totalTextHeight = lines.length * lineHeight;

    // Check if text fits; otherwise, reduce font size or truncate
    const fontSize = Math.min(13, availableHeight / lines.length);
    const textFits = totalTextHeight <= availableHeight;

    const formatNumber = (num: number): string => {
        if (num >= 1_000_000_000) return (num / 1_000_000_000).toFixed(1).replace(/\.0$/, "") + "B";
        if (num >= 1_000_000) return (num / 1_000_000).toFixed(1).replace(/\.0$/, "") + "M";
        if (num >= 1_000) return (num / 1_000).toFixed(1).replace(/\.0$/, "") + "K";
        return num.toString();
    };

    return (
        <g>
            {/* Box Background */}
            <rect x={x} y={y} width={width} height={height} fill={color} stroke="#333" />

            {/* Wrapped Text */}
            <text
                x={x + width / 2}
                y={y + (height - totalTextHeight) / 2 + fontSize}
                textAnchor="middle"
                dominantBaseline="middle"
                fontSize={fontSize}
                fontWeight="600"
                fontFamily="Inter, sans-serif"
                fill="black"
                style={{
                    textShadow: "none",
                    strokeWidth: 0,
                    stroke: "none"
                }}
            >
                {textFits ? (
                    lines.map((line, index) => (
                        <tspan key={index} x={x + width / 2} dy={index === 0 ? 0 : lineHeight}>
                            {line}
                        </tspan>
                    ))
                ) : (
                    <tspan>
                        {`${name.substring(0, availableWidth / 6)}...`}
                    </tspan>
                )}
            </text>
            {size && (
                <text
                    x={x + width / 2}
                    y={y + (height - totalTextHeight) / 2 + fontSize + lineHeight * lines.length + sizeMarginTop}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    fontSize={fontSize}
                    fontWeight="600"
                    fontFamily="Inter, sans-serif"
                    fill="black"
                    style={{
                        textShadow: "none",
                        strokeWidth: 0,
                        stroke: "none"
                    }}
                >
                    {formatNumber(size)}
                </text>
            )}
        </g>
    );
};

// custom tooltip for chart
const CustomTooltip = ({ active, payload }: TooltipProps<number, string>) => {
    if (!active || !payload || !payload[0]) return null;

    const data = payload[0].payload;

    return (
        <div style={{ background: "#fff", border: "1px solid #ccc", padding: "10px", fontSize: "0.9rem", marginTop: "20px" }}>
            <p style={{ color: "black", fontFamily: "Inter" }}>Word: <b>{data.name}</b></p>
            <p style={{ color: "black", fontFamily: "Inter" }}>Total Number of Keywords with "<b>{data.name}</b>": <b>{data.keywordCount}</b></p>
            <p style={{ color: "black", fontFamily: "Inter" }}>Top Keywords:</p>
            <ul style={{ paddingLeft: 18, fontFamily: "Inter" }}>
                {data.topKeywords.map((kw: string, idx: number) => (
                    <li key={idx}><b>{idx + 1}. {kw}</b></li>
                ))}
            </ul>
            <p style={{ color: "black", fontFamily: "Inter" }}>Total Keywords in Position 1-10: <b>{data.topCount}</b></p>
            <p style={{ color: "black", fontFamily: "Inter" }}>Total Clicks: <b>{data.totalClicks}</b></p>
            <p style={{ color: "black", fontFamily: "Inter" }}>Total Impressions: <b>{data.totalImpressions}</b></p>
        </div>
    );
};

const TreemapChart = ({ gscKeywordsData }) => {
    const wordStatsMap: Record<string, WordStats> = {};
    const stopwords = [
        "a", "an", "and", "are", "as", "at", "be", "by", "for", "from", "has", "he", "in", "is", "it", "its", "of", "on", "that", "the", "to", "was", "were", "will", "with", "&", "?"
    ];

    gscKeywordsData.forEach((item) => {
        const words = item.keyword.toLowerCase().split(/\s+/);
        words.forEach((word) => {
            if (stopwords.includes(word)) return;

            if (!wordStatsMap[word]) {
                wordStatsMap[word] = {
                    name: word,
                    size: 0,
                    totalClicks: 0,
                    totalImpressions: 0,
                    topKeywords: [],
                    keywordCount: 0,
                    topCount: 0,
                    color: ""
                };
            }

            const stats = wordStatsMap[word];
            stats.size += item.impressions;
            stats.totalImpressions += item.impressions;
            stats.totalClicks += item.clicks;
            stats.keywordCount += 1;
            if (item.position >= 1 && item.position <= 10) stats.topCount += 1;

            stats.topKeywords.push(item);
        });
    });

    // Trim top keywords for tooltip (by impression)
    Object.values(wordStatsMap).forEach((stat: any) => {
        stat.topKeywords = stat.topKeywords
            .sort((a, b) => b.impressions - a.impressions)
            .slice(0, 5)
            .map(k => k.keyword);
    });

    const processedData = Object.values(wordStatsMap)
        .sort((a, b) => b.size - a.size)
        .slice(0, 20);

    const colors = ["#dab6f0", "#a4d3f5", "#baf0b2", "#f8e79a", "#f7cfa7"];
    processedData.forEach((item, index) => {
        item.color = colors[index % colors.length];
    });

    return (
        <div style={{ width: "100%", height: 400 }}>
            <div className="has-text-centered" style={{ marginBottom: "10px" }}>
                <h1 style={{ fontSize: "1.5rem", fontFamily: "Inter" }}>Word Map / Keyword Cluster Analysis</h1>
                <span style={{ fontFamily: "Inter" }}>Top Words that are parts of your keywords</span>
            </div>
            <ResponsiveContainer>
                <Treemap
                    width={600}
                    height={400}
                    data={processedData}
                    dataKey="size"
                    stroke="#fff"
                    fill="#333"
                    isAnimationActive={false}
                    content={<CustomTreemapContent />}
                >
                    <RechartsTooltip
                        content={<CustomTooltip />}
                        cursor={{ stroke: "none" }}
                    />
                    {processedData.map((entry, index) => (
                        <Cell
                            key={`cell-${index}`}
                            fill={entry.color}
                            style={{ transition: "none" }}
                        />
                    ))}
                </Treemap>
            </ResponsiveContainer>
        </div>
    );
};

const TopQueriesChart = ({ data }: { data: { keyword: string; clicks: number }[] }) => {
    const isEmpty = !data.length || data.every(d => d.clicks === 0);

    return (
        <div style={{ width: '100%', height: "400px" }}>
            <h1 className="has-text-centered" style={{ fontSize: "1.5rem", fontFamily: "Inter, sans-serif" }}>
                Top 5 Queries by Clicks
            </h1>
            {isEmpty ? (
                <div style={{ height: "90%", display: "flex", alignItems: "center", justifyContent: "center" }}>
                    <p>Not enough data</p>
                </div>
            ) : (
                <ResponsiveContainer height={"90%"}>
                    <BarChart layout="vertical" data={data} margin={{ top: 10, right: 30, left: 100, bottom: 10 }}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis type="number">
                            <Label
                                value={"Number of Clicks"}
                                offset={-10}
                                position="insideBottom"
                                style={{ fontSize: 13 }}
                            />
                        </XAxis>
                        <YAxis dataKey="keyword" type="category" style={{ fontSize: 13 }} />
                        <Bar dataKey="clicks" fill="#1890ff">
                            <LabelList dataKey="clicks" position="right" style={{ fontSize: 13 }} />
                        </Bar>
                    </BarChart>
                </ResponsiveContainer>
            )}
        </div>
    );
};

// CustomTooltip for bar
const CustomTooltipHover: React.FC<TooltipProps<number, string>> = ({ active, payload, label }) => {

    if (active && payload && payload.length) {
        const data = payload[0].payload;
        return (
            <div
                style={{
                    background: "#fff",
                    padding: "8px 12px",
                    borderRadius: "6px",
                    boxShadow: "0 2px 6px rgba(0, 0, 0, 0.15)",
                    fontSize: "13px",
                    color: "#000",
                }}
            >
                <p>You received <b>{data.totalClicks ?? "0"} clicks</b> out of <br /> <b>{data.totalImpressions ?? "0"} impressions</b> from <b>{data.count ?? "0"} keywords</b> <br /> ranking between <b>{data.range ?? "0"} {data.chart ?? ""}</b></p>
            </div>
        );
    }

    return null;
};


export const GSCData = (props: selectedDomainProps) => {
    const pageSizes = [10, 25, 50, 100, 500, 1000];

    // -------------------------- STATES --------------------------
    const [gscKeywordsData, setGscKeywordsData] = useState<Array<GSCKeyword>>([]);
    const [isProcessing, setIsProcessing] = useState(false);
    const [mainModalActive, setMainModalActive] = useState(true);
    const [dateRange, setDateRange] = useState("");
    const [showLogo, setShowLogo] = useState(false);
    const [quickRankingData, setQuickRankingData] = useState<Array<GSCKeyword>>([]);
    const [devicePerformanceData, setDevicePerformanceData] = useState<Array<GSCKeyword>>([]);
    const [topKeywords, setTopKeywords] = useState<Array<GSCKeyword>>([]);
    const [selectedTab, setSelectedTab] = useState("3-5 words");

    const dateOptions = ["Last week", "Last 30 days", "Last 3 months", "Last 1 year"];

    const regexList = ["\\b(what|where|when|why|can|how|does|who|would|could|should|are|have|was|will)\\b",
        "^(best|top|vs|versus|compare|comparison|review|reviews|pricing|price|cost|cheap|affordable|tool|tools|software|app|apps|platform|platforms|solution|solutions|service|services|buy|purchase|order|subscription|free trial|demo|trial|alternative|alternatives|plan|plans|package|packages|deal|deals)\\b",
        /^(\w+\s+){2,4}\w+$/, /^(\w+\s+){5,7}\w+$/, /^(\w+\s+){7,}\w+$/,
        ".*[\p{Hiragana}\p{Katakana}\p{Han}\p{Hangul}\p{Cyrillic}\p{Arabic}\p{Hebrew}\p{Thai}\p{Devanagari}\p{Bengali}\p{Gujarati}\p{Tamil}\p{Telugu}\p{Kannada}\p{Malayalam}].*"]

    const [questionBasedKeywords, setQuestionBasedKeywords] = useState<GSCKeyword[]>([]);
    const [commercialIntentKeywords, setCommercialIntentKeywords] = useState<GSCKeyword[]>([]);
    const [longTailKeywords, setLongTailKeywords] = useState<GSCKeyword[]>([]);
    const [multilingualKeywords, setMultilingualKeywords] = useState<GSCKeyword[]>([]);
    const [realoadGSCInsights, setRealoadGSCInsights] = useState(false)
    const [lastUpdatedAt, setLastUpdatedAt] = useState("")


    // -------------------------- QUERIES --------------------------
    const getGscData = useQuery(fetchGSCKeywordsData2(props.selectedDomain, dateRange, realoadGSCInsights));

    // --------------------------- REFS ---------------------------
    const failAlertRef = useRef<any>(null);
    const successAlertRef = useRef<any>(null);

    const formatNumber = (num: number): string => {
        if (num >= 1_000_000_000) return (num / 1_000_000_000).toFixed(1).replace(/\.0$/, "") + "B";
        if (num >= 1_000_000) return (num / 1_000_000).toFixed(1).replace(/\.0$/, "") + "M";
        if (num >= 1_000) return (num / 1_000).toFixed(1).replace(/\.0$/, "") + "K";
        return num.toString();
    };

    const parseNumber = (str: string): number => {
        if (str.includes("B")) return parseFloat(str) * 1_000_000_000;
        if (str.includes("M")) return parseFloat(str) * 1_000_000;
        if (str.includes("K")) return parseFloat(str) * 1_000;
        return parseFloat(str);
    };

    const createBuckets = (min: number, max: number, chartType: string): string[] => {
        let buckets: string[] = [];

        if (chartType == "CTR") {
            buckets = ["1-3%", "4-10%", "11-15%", "16-20%", "21-30%", "31-50%", "51-80%", "81-100%"]
        }
        else if (chartType == "Position") {
            let start = min;
            while (start < max) {
                let next = start < 5 ? start + 3 : start * 2;
                if (next > max) next = max;
                buckets.push(`${formatNumber(start)}-${formatNumber(next)}`);
                start = next + 1;
            }
        }
        else {
            let start = min;
            while (start < max) {
                let next = start < 5 ? start + 3 : start * 3;
                if (next > max) next = max;
                buckets.push(`${formatNumber(start)}-${formatNumber(next)}`);
                start = next + 1;
            }
        }

        if (buckets.length == 0) {
            buckets.push("No Data Available")
        }

        return buckets;
    };


    const aggregateData = (queries: Array<GSCKeyword>): {
        position: Array<{ range: string; count: number; totalImpressions: number; totalClicks: number }>;
        ctr: Array<{ range: string; count: number; totalImpressions: number; totalClicks: number }>;
        impressions: Array<{ range: string; count: number; totalImpressions: number; totalClicks: number }>;
    } => {
        const maxPos = Math.max(...queries.map(q => q.position));
        const maxCTR = Math.max(...queries.map(q => q.ctr * 100));
        const maxImp = Math.max(...queries.map(q => q.impressions));

        // Generate dynamic ranges
        const posRanges = createBuckets(1, maxPos, "Position");
        const ctrRanges = createBuckets(0, maxCTR, "CTR");
        const impRanges = createBuckets(1, maxImp, "Impressions");

        // Initialize bucket counters
        const buckets = {
            position: Object.fromEntries(posRanges.map(range => [range, 0])),
            ctr: Object.fromEntries(ctrRanges.map(range => [range, 0])),
            impressions: Object.fromEntries(impRanges.map(range => [range, 0])),
        };

        // Function to find the appropriate bucket
        const assignToBucket = (value: number, ranges: string[]): string => {
            const bucket = ranges.find(range => {
                const [low, high] = range.replace("%", "").split("-").map(parseNumber);
                return value >= low && value <= high;
            });

            return bucket || ranges[ranges.length - 1]; // Ensure it never returns undefined
        };

        // Distribute data into buckets
        queries.forEach(({ position, ctr, impressions }) => {
            buckets.position[assignToBucket(position, posRanges)]++;
            buckets.ctr[assignToBucket(ctr * 100, ctrRanges)]++;
            buckets.impressions[assignToBucket(impressions, impRanges)]++;
        });

        // Add total impressions and clicks for each bucket
        const totalImpressionsAndClicks = {
            position: posRanges.map(range => ({
                range,
                totalImpressions: 0,
                totalClicks: 0,
            })),
            ctr: ctrRanges.map(range => ({
                range,
                totalImpressions: 0,
                totalClicks: 0,
            })),
            impressions: impRanges.map(range => ({
                range,
                totalImpressions: 0,
                totalClicks: 0,
            })),
        };

        // Calculate total impressions and clicks for each bucket
        queries.forEach(({ position, ctr, impressions, clicks }) => {
            const posBucket = assignToBucket(position, posRanges);
            const ctrBucket = assignToBucket(ctr * 100, ctrRanges);
            const impBucket = assignToBucket(impressions, impRanges);

            // Update total impressions and clicks for each bucket
            // Update total impressions and clicks for position buckets
            const posBucketData = totalImpressionsAndClicks.position.find(bucket => bucket.range === posBucket);
            if (posBucketData) {
                posBucketData.totalImpressions += impressions;
                posBucketData.totalClicks += clicks;
            }

            // Update total impressions and clicks for CTR buckets
            const ctrBucketData = totalImpressionsAndClicks.ctr.find(bucket => bucket.range === ctrBucket);
            if (ctrBucketData) {
                ctrBucketData.totalImpressions += impressions;
                ctrBucketData.totalClicks += clicks;
            }

            // Update total impressions and clicks for impressions buckets
            const impBucketData = totalImpressionsAndClicks.impressions.find(bucket => bucket.range === impBucket);
            if (impBucketData) {
                impBucketData.totalImpressions += impressions;
                impBucketData.totalClicks += clicks;
            }
        });

        // Add calculated data to buckets
        const transformedBuckets = {
            position: Object.entries(buckets.position).map(([range, count]) => {
                const matchingData = totalImpressionsAndClicks.position.find(data => data.range === range);
                return {
                    range,
                    count,
                    totalImpressions: matchingData?.totalImpressions || 0,
                    totalClicks: matchingData?.totalClicks || 0,
                    chart: "position"
                };
            }),
            ctr: Object.entries(buckets.ctr).map(([range, count]) => {
                const matchingData = totalImpressionsAndClicks.ctr.find(data => data.range === range);
                return {
                    range,
                    count,
                    totalImpressions: matchingData?.totalImpressions || 0,
                    totalClicks: matchingData?.totalClicks || 0,
                    chart: "ctr"
                };
            }),
            impressions: Object.entries(buckets.impressions).map(([range, count]) => {
                const matchingData = totalImpressionsAndClicks.impressions.find(data => data.range === range);
                return {
                    range,
                    count,
                    totalImpressions: matchingData?.totalImpressions || 0,
                    totalClicks: matchingData?.totalClicks || 0,
                    chart: "impressions"
                };
            }),
        };

        return transformedBuckets;

    };

    const calculateMetrics = (queries) => {
        const totalClicks = queries.reduce((sum, q) => sum + q.clicks, 0);
        const totalImpressions = queries.reduce((sum, q) => sum + q.impressions, 0);
        const avgCTR = totalImpressions > 0 ? ((totalClicks / totalImpressions) * 100).toFixed(2) : "0.00";
        const avgPosition = (queries.reduce((sum, q) => sum + parseFloat(q.position), 0) / queries.length).toFixed(1);

        return {
            totalClicks: formatNumber(totalClicks),
            totalImpressions: formatNumber(totalImpressions),
            rawTotalImpressions: totalImpressions,
            avgCTR,
            avgPosition
        };
    };

    const metrics = calculateMetrics(gscKeywordsData);
    const aggregated = aggregateData(gscKeywordsData);

    const getRelativeTime = (dateString: string) => {
        const createdDateObj = new Date(dateString);
        const now = new Date();
        const timeDiff = now.getTime() - createdDateObj.getTime();

        const seconds = Math.floor(Math.abs(timeDiff) / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (timeDiff < 0) {
            // FUTURE TIME
            if (seconds < 60) return "in a few seconds";
            if (minutes < 60) return `in ${minutes} minute${minutes === 1 ? '' : 's'}`;
            if (hours < 24) return `in ${hours} hour${hours === 1 ? '' : 's'}`;
            return `in ${days} day${days === 1 ? '' : 's'}`;
        }

        // PAST TIME
        if (seconds < 60) return "just now";
        if (minutes < 60) return minutes === 1 ? "a minute ago" : `${minutes} minutes ago`;
        if (hours < 24) return hours === 1 ? "an hour ago" : `${hours} hours ago`;
        if (days > 30) {
            const day = createdDateObj.getDate();
            const month = createdDateObj.toLocaleString('default', { month: 'short' });
            const year = createdDateObj.getFullYear().toString().slice(-2);
            return `${day} ${month}, ${year}`;
        }
        return days === 1 ? "a day ago" : `${days} days ago`;
    };

    useEffect(() => {
        if (dateRange) {
            successAlertRef.current?.show("GSC Data will be updated based on the selected date range.");
            setIsProcessing(true);
            getGscData.refetch();
        }
    }, [dateRange]);

    useEffect(() => {
        if (realoadGSCInsights) {
            setIsProcessing(true);
            getGscData.refetch();
        }
    }, [realoadGSCInsights]);

    useEffect(() => {
        const processGscData = async () => {

            if (getGscData.data && getGscData.data['data']) {

                setLastUpdatedAt(getRelativeTime(getGscData.data['last_updated']))
                const keywordMap: Record<string, GSCKeyword> = {};
                const deviceMap: Record<string, {
                    clicks: number;
                    impressions: number;
                    ctrSum: number;
                    ctrCount: number;
                    positionSum: number;
                    positionCount: number;
                }> = {};
                const keywordClicksMap: Record<string, number> = {};

                // Perform filtering
                (getGscData.data['data'] as { rows: any[] })?.rows?.forEach((row: any) => {
                    const keyword = row.keys[0];
                    const url = row.keys[1];
                    const device = row.keys[2];
                    const clicks = row.clicks;
                    const impressions = row.impressions;
                    const ctr = row.ctr;
                    const position = row.position;

                    if (!keywordMap[keyword]) {
                        keywordMap[keyword] = {
                            url: url,
                            device: device,
                            keyword: keyword,
                            clicks: clicks,
                            impressions: impressions,
                            ctr: ctr.toFixed(2),
                            position: Math.round(position),
                        };
                    }
                    else {
                        keywordMap[keyword]["clicks"] += clicks
                        keywordMap[keyword]["impressions"] += impressions
                        keywordMap[keyword]["ctr"] += ctr.toFixed(2)
                        keywordMap[keyword]["position"] += Math.round(position)
                        keywordMap[keyword]["ctr"] = (keywordMap[keyword]["ctr"] / 2)
                        keywordMap[keyword]["position"] = Math.round(keywordMap[keyword]["position"] / 2)

                    }
                    // Device-wise stats
                    if (!deviceMap[device]) {
                        deviceMap[device] = {
                            clicks: 0,
                            impressions: 0,
                            ctrSum: 0,
                            ctrCount: 0,
                            positionSum: 0,
                            positionCount: 0
                        };
                    }

                    const stats = deviceMap[device];
                    stats.clicks += clicks;
                    stats.impressions += impressions;
                    stats.ctrSum += ctr;
                    stats.ctrCount++;
                    stats.positionSum += position;
                    stats.positionCount++;

                    if (keywordClicksMap[keyword]) {
                        keywordClicksMap[keyword] += row.clicks;
                    } else {
                        keywordClicksMap[keyword] = row.clicks;
                    }
                });

                // Prepare final device performance array
                const formattedDeviceStats: GSCKeyword[] = Object.entries(deviceMap).map(([device, stats]) => ({
                    device,
                    keyword: '', // Not relevant here
                    url: '',     // Not relevant here
                    clicks: stats.clicks,
                    impressions: stats.impressions,
                    ctr: Number((stats.ctrSum / (stats.ctrCount || 1)).toFixed(2)),
                    position: Number((stats.positionSum / (stats.positionCount || 1)).toFixed(1)),
                }));

                // Convert the object values back into an array
                const gscKeywords = Object.values(keywordMap);

                // Filter for position between 11 and 30
                const seenUrls = new Set<string>();

                const filteredInitialData = gscKeywords.filter((item) => {
                    if (item.position >= 11 && item.position <= 30 && !seenUrls.has(item.url)) {
                        seenUrls.add(item.url);
                        return true;
                    }
                    return false;
                });

                const filteredTopKeywords = Object.entries(keywordClicksMap)
                    .map(([keyword, clicks]) => ({ keyword, clicks }))
                    .sort((a, b) => b.clicks - a.clicks)
                    .slice(0, 5);

                // Simulate async to ensure state update propagates
                await new Promise((resolve) => setTimeout(resolve, 0));


                const questionBasedPattern = new RegExp(regexList[0], "i");
                const questionBasedMatch = gscKeywords.filter((item) => questionBasedPattern.test(item.keyword));
                setQuestionBasedKeywords(questionBasedMatch);

                const commercialIntentPattern = new RegExp(regexList[1], "i");
                const commercialIntentMatch = gscKeywords.filter((item) => commercialIntentPattern.test(item.keyword));
                setCommercialIntentKeywords(commercialIntentMatch);

                const longTailPattern = new RegExp(regexList[2], "i");
                const longTailMatch = gscKeywords.filter((item) => longTailPattern.test(item.keyword));
                setLongTailKeywords(longTailMatch);

                // const multilingualPattern = new RegExp(regexList[5], "u");
                // const multilingualMatch = gscKeywords.filter((item) => multilingualPattern.test(item.keyword));
                // setMultilingualKeywords(multilingualMatch);
                const multilingualPattern = /[\u3040-\u30FF\u3400-\u4DBF\u4E00-\u9FFF\uAC00-\uD7AF\u0400-\u04FF\u0600-\u06FF\u0590-\u05FF\u0E00-\u0E7F\u0900-\u097F\u0980-\u09FF\u0A80-\u0AFF\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF\u0D00-\u0D7F]/;

                const multilingualMatch = gscKeywords.filter(item =>
                    multilingualPattern.test(item.url || '') || multilingualPattern.test(item.keyword || '')
                );
                setMultilingualKeywords(multilingualMatch);

                setGscKeywordsData(gscKeywords as any);
                setQuickRankingData(filteredInitialData as any);
                setDevicePerformanceData(formattedDeviceStats);
                setTopKeywords(filteredTopKeywords as any)
                setIsProcessing(false); // Processing complete
                setRealoadGSCInsights(false)
            }
        };

        processGscData();
    }, [getGscData.data]);


    const RegexKeywordSection: React.FC<Props> = ({ data }) => {

        const queryColumnHelper = createColumnHelper<GSCKeyword>();

        const queryColumnDefs: ColumnDef<GSCKeyword, any>[] = [
            queryColumnHelper.accessor((row: GSCKeyword) => row.keyword, {
                id: 'keyword',
                header: "Query",
                cell: cellProps => {

                    return (
                        <>
                            <span style={{ cursor: "default" }}>
                                {cellProps.row.original.keyword}
                            </span>
                        </>
                    )

                },
                enableSorting: false,
            }),
            queryColumnHelper.accessor((row: GSCKeyword) => row.url, {
                id: 'url',
                header: "URL",
                cell: cellProps => {

                    return (
                        <>
                            <a className="underline-on-hover" href={`${cellProps.row.original.url}`}
                                style={{ color: "#000", fontFamily: "Inter" }} target="_blank">
                                {cellProps.row.original.url}<svg width="24" height="12" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M432 320H400a16 16 0 0 0 -16 16V448H64V128H208a16 16 0 0 0 16-16V80a16 16 0 0 0 -16-16H48A48 48 0 0 0 0 112V464a48 48 0 0 0 48 48H400a48 48 0 0 0 48-48V336A16 16 0 0 0 432 320zM488 0h-128c-21.4 0-32.1 25.9-17 41l35.7 35.7L135 320.4a24 24 0 0 0 0 34L157.7 377a24 24 0 0 0 34 0L435.3 133.3 471 169c15 15 41 4.5 41-17V24A24 24 0 0 0 488 0z" /></svg>
                            </a>
                        </>
                    )

                },
                enableSorting: false,
            }),
            queryColumnHelper.accessor((row: GSCKeyword) => row.clicks, {
                id: 'clicks',
                header: "Clicks",
                cell: cellProps => {

                    return (
                        <>
                            <span style={{ cursor: "default"}}>
                                {cellProps.row.original.clicks}
                            </span>
                        </>
                    )

                },
                enableSorting: true,
            }),
            queryColumnHelper.accessor((row: GSCKeyword) => row.impressions, {
                id: 'impressions',
                header: "Impressions",
                cell: cellProps => {

                    return (
                        <>
                            <span style={{ cursor: "default" }}>
                                {cellProps.row.original.impressions}
                            </span>
                        </>
                    )

                },
                enableSorting: true,
            }),
            queryColumnHelper.accessor((row: GSCKeyword) => row.ctr, {
                id: 'ctr',
                header: "CTR",
                cell: cellProps => {

                    return (
                        <>
                            <span style={{ cursor: "default" }}>
                                {cellProps.row.original.ctr ? cellProps.row.original.ctr : 0.00}%
                            </span>
                        </>
                    )

                },
                enableSorting: true,
            }),
            queryColumnHelper.accessor((row: GSCKeyword) => row.position, {
                id: 'position',
                header: "Position",
                cell: cellProps => {

                    return (
                        <>
                            <span style={{ cursor: "default"}}>
                                {cellProps.row.original.position}
                            </span>
                        </>
                    )

                },
                enableSorting: true,
            }),
        ];

        return (
            <>
                <div className="gsc-table w-100">
                    <AbunTable
                        id="long-tail-keyword-queries"
                        tableContentName={""}
                        tableName="Long Tail Keyword Queries"
                        tableDescription="Low Competition, Highly Specific Searches."
                        columnDefs={queryColumnDefs}
                        tableData={longTailKeywords}
                        pageSizes={[10, 25, 50, 100, 200]}
                        initialPageSize={10}
                        enableSorting={true}
                        defaultSortingState={[{ id: "impressions", desc: true }]}
                        noDataText={"No Data Found"}
                        selectedTab={selectedTab}
                        filterTabs={[
                            {
                                name: "3-5 words",
                                onClick: () => {
                                    setSelectedTab("3-5 words")
                                    let longTailPattern = new RegExp(regexList[2], "i");
                                    let longTailMatch = gscKeywordsData.filter((item) => longTailPattern.test(item.keyword));
                                    setLongTailKeywords(longTailMatch);
                                }
                            },
                            {
                                name: "6-8 words",
                                onClick: () => {
                                    setSelectedTab("6-8 words")
                                    let longTailPattern = new RegExp(regexList[3], "i");
                                    let longTailMatch = gscKeywordsData.filter((item) => longTailPattern.test(item.keyword));
                                    setLongTailKeywords(longTailMatch);
                                }
                            },
                            {
                                name: "8+ words",
                                onClick: () => {
                                    setSelectedTab("8+ words")
                                    let longTailPattern = new RegExp(regexList[4], "i");
                                    let longTailMatch = gscKeywordsData.filter((item) => longTailPattern.test(item.keyword));
                                    setLongTailKeywords(longTailMatch);
                                }
                            }
                        ]}
                    />
                </div>

                <div className="gsc-table w-100">
                    <AbunTable
                        tableContentName=""
                        tableName="Commercial Intent Queries"
                        tableDescription="Spotting Search Terms with Purchase Intent."
                        tableData={commercialIntentKeywords}
                        columnDefs={queryColumnDefs}
                        pageSizes={[10, 25, 50, 100, 200]}
                        initialPageSize={10}
                        noDataText="No Data Found."
                        enableSorting={true}
                        defaultSortingState={[{ id: "impressions", desc: true }]}
                    />
                </div>

                <div className="gsc-table w-100">
                    <AbunTable
                        tableContentName=""
                        tableName="Multilingual Queries"
                        tableDescription="Uncover global search intent."
                        tableData={multilingualKeywords}
                        columnDefs={queryColumnDefs}
                        pageSizes={[10, 25, 50, 100, 200]}
                        initialPageSize={10}
                        noDataText="No Data Found."
                        enableSorting={true}
                        defaultSortingState={[{ id: "impressions", desc: true }]}
                    />
                </div>

                <div className="gsc-table w-100">
                    <AbunTable
                        tableContentName=""
                        tableName="Question-Based Queries"
                        tableDescription="Optimizing for the Questions Your Audience Is Asking."
                        tableData={questionBasedKeywords}
                        columnDefs={queryColumnDefs}
                        pageSizes={[10, 25, 50, 100, 200]}
                        initialPageSize={10}
                        noDataText="No Data Found."
                        enableSorting={true}
                        defaultSortingState={[{ id: "impressions", desc: true }]}
                    />
                </div>
            </>
        );
    };

    const columnHelper = createColumnHelper<GSCKeyword>();

    const columnDefs: ColumnDef<GSCKeyword, any>[] = [
        columnHelper.accessor((row: GSCKeyword) => row.keyword, {
            id: 'keyword',
            header: "Query",
            cell: cellProps => {

                return (
                    <>
                        <span style={{ cursor: "default" }}>
                            {cellProps.row.original.keyword}
                        </span>
                    </>
                )

            },
            enableGlobalFilter: true,
            enableSorting: true,
        }),
        columnHelper.accessor((row: GSCKeyword) => row.url, {
            id: 'url',
            header: "URL",
            cell: cellProps => {

                return (
                    <>
                        <a className="underline-on-hover" href={`${cellProps.row.original.url}`}
                            style={{ color: "#000" }} target="_blank">
                            {cellProps.row.original.url}<svg width="24" height="12" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M432 320H400a16 16 0 0 0 -16 16V448H64V128H208a16 16 0 0 0 16-16V80a16 16 0 0 0 -16-16H48A48 48 0 0 0 0 112V464a48 48 0 0 0 48 48H400a48 48 0 0 0 48-48V336A16 16 0 0 0 432 320zM488 0h-128c-21.4 0-32.1 25.9-17 41l35.7 35.7L135 320.4a24 24 0 0 0 0 34L157.7 377a24 24 0 0 0 34 0L435.3 133.3 471 169c15 15 41 4.5 41-17V24A24 24 0 0 0 488 0z" /></svg>
                        </a>
                    </>
                )

            },
            enableGlobalFilter: true,
            enableSorting: false,
        }),
        columnHelper.accessor((row: GSCKeyword) => row.position, {
            id: 'position',
            header: "Position",
            cell: cellProps => {

                return (
                    <>
                        <span style={{ cursor: "default"}}>
                            {cellProps.row.original.position}
                        </span>
                    </>
                )

            },
            enableGlobalFilter: true,
            enableSorting: true,
        }),
    ];

    const columnHelperDeviceComparison = createColumnHelper<GSCKeyword>();

    const columnDefsDeviceComparison: ColumnDef<GSCKeyword, any>[] = [
        columnHelperDeviceComparison.accessor((row: GSCKeyword) => row.device, {
            id: 'device',
            header: "Device",
            cell: cellProps => {

                return (
                    <>
                        <span style={{ cursor: "default" }}>{cellProps.row.original.device}</span>
                    </>
                )

            },
            enableGlobalFilter: false,
            enableSorting: false,
        }),
        columnHelperDeviceComparison.accessor((row: GSCKeyword) => row.clicks, {
            id: 'clicks',
            header: "Clicks",
            cell: cellProps => {

                return (
                    <>
                        <span style={{ cursor: "default"}}>{cellProps.row.original.clicks}</span>
                    </>
                )

            },
            enableGlobalFilter: false,
            enableSorting: false,
        }),
        columnHelperDeviceComparison.accessor((row: GSCKeyword) => row.impressions, {
            id: 'impressions',
            header: "Impressions",
            cell: cellProps => {

                return (
                    <>
                        <span style={{ cursor: "default" }}>{cellProps.row.original.impressions}</span>
                    </>
                )

            },
            enableGlobalFilter: false,
            enableSorting: false,
        }),
        columnHelperDeviceComparison.accessor((row: GSCKeyword) => row.ctr, {
            id: 'ctr',
            header: "CTR",
            cell: cellProps => {

                return (
                    <>
                        <span style={{ cursor: "default"}}>{cellProps.row.original.ctr}%</span>
                    </>
                )

            },
            enableGlobalFilter: false,
            enableSorting: false,
        })

    ];

    const getNiceTicks = (maxValue, ticks = 6) => {
        if (maxValue === 0) return [0];

        const rawStep = maxValue / (ticks - 1);
        const magnitude = Math.pow(10, Math.floor(Math.log10(rawStep)));
        const normalized = rawStep / magnitude;

        let niceNormalized;
        if (normalized <= 1) niceNormalized = 1;
        else if (normalized <= 2) niceNormalized = 2;
        else if (normalized <= 5) niceNormalized = 5;
        else niceNormalized = 10;

        const niceStep = niceNormalized * magnitude;
        const niceMax = (Math.ceil(maxValue / niceStep) * niceStep) + niceStep;

        const result: number[] = [];
        for (let i = 0; i <= niceMax; i += niceStep) {
            result.push(i);
        }
        return result;
    };

    if (getGscData.isLoading || isProcessing) {
        return (
            <div className={"loadingData w-100 is-flex is-justify-content-center is-align-items-center"}>
                <AbunLoader show={isProcessing || getGscData.isLoading} height="20vh" />
            </div>
        )
    } else if (getGscData.isError) {
        let error = JSON.parse((getGscData.error as Error).message) || null;
        if (!error) error = { message: "Error fetching data from Google Search Console!" }
        return (
            <AbunModal active={mainModalActive}
                headerText={"Error fetching data"}
                closeable={true}
                hideModal={() => {
                    setMainModalActive(false);
                    props.handleBackBtnClick();
                }}>
                <div className="error-div has-text-centered">
                    <p className="my-2">{error.message}</p>
                    <GenericButton text={"Retry"}
                        type={"success"}
                        clickHandler={() => getGscData.refetch()}
                    />
                </div>
            </AbunModal>
        )
    }

    return (
        <div id="export-content">
            <div className="performance-header">
                {showLogo ? (
                    <a href="https://abun.com" target="_blank">
                        <img
                            className="my-2"
                            src={abunLogo}
                            alt="Abun Logo"
                            style={{ width: 150, height: "auto", margin: "auto" }}
                        />
                    </a>
                ) : (
                    <>
                        {/* This empty <h2> is left for layout purposes. */}
                        <h2></h2>
                        <div className="filter-btn">
                            <div className="filters">
                                {/* <span>
                                    Search type:
                                    <strong> Web</strong>
                                </span>
                                <span className="divider">|</span> */}
                                <span>
                                    Date:
                                    <strong>
                                        <select
                                            value={dateRange ? dateRange : "Last 30 days"}
                                            onChange={(e) => setDateRange(e.target.value)}
                                            className="filter-dropdown"
                                        >
                                            {dateOptions.map((option) => (
                                                <option key={option} value={option}>{option}</option>
                                            ))}
                                        </select>
                                    </strong>
                                </span>
                            </div>
                        </div>
                    </>
                )}
            </div>
            <div className="is-flex is-justify-content-end">
                <span className="mt-1">
                    Last Updated at:
                    <b>
                        {lastUpdatedAt}
                    </b>
                </span>
                <AbunButton
                    className={"is-primary is-small mx-2"}
                    style={{ background: '#2E64FE' }}
                    disabled={isProcessing}
                    type={"primary"} clickHandler={() => {
                        setRealoadGSCInsights(true);
                    }}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M370.7 133.3C339.5 104 298.9 88 255.8 88c-77.5 .1-144.3 53.2-162.8 126.9-1.3 5.4-6.1 9.2-11.7 9.2H24.1c-7.5 0-13.2-6.8-11.8-14.2C33.9 94.9 134.8 8 256 8c66.4 0 126.8 26.1 171.3 68.7L463 41C478.1 25.9 504 36.6 504 57.9V192c0 13.3-10.7 24-24 24H345.9c-21.4 0-32.1-25.9-17-41l41.8-41.7zM32 296h134.1c21.4 0 32.1 25.9 17 41l-41.8 41.8c31.3 29.3 71.8 45.3 114.9 45.3 77.4-.1 144.3-53.1 162.8-126.8 1.3-5.4 6.1-9.2 11.7-9.2h57.3c7.5 0 13.2 6.8 11.8 14.2C478.1 417.1 377.2 504 256 504c-66.4 0-126.8-26.1-171.3-68.7L49 471C33.9 486.1 8 475.4 8 454.1V320c0-13.3 10.7-24 24-24z" /></svg>
                    Reload Insights
                </AbunButton>
            </div>

            <div className="gsc-stats">
                <div className="stat-card blue">
                    <p>Clicks</p>
                    <h3>{metrics.totalClicks}</h3>
                    <span>Total clicks from Google Search</span>
                </div>
                <div className="stat-card purple">
                    <p>Impressions</p>
                    <h3>{metrics.totalImpressions}</h3>
                    <span>Times your site appeared in search results</span>
                </div>
                <div className="stat-card green">
                    <p>Average CTR</p>
                    <h3>{metrics.avgCTR}%</h3>
                    <span>Percentage of impressions that resulted in clicks</span>
                </div>
                <div className="stat-card orange">
                    <p>Average Position</p>
                    <h3>{metrics.avgPosition}</h3>
                    <span>Average ranking position in search results</span>
                </div>
            </div>

            <TreemapChart gscKeywordsData={gscKeywordsData} />

            <div className="chart-container">
                {["Position Insight"].map((title, index) => {
                    let xAxisLabel = "Position Range";
                    let yAxisLabel = "Number of clicks";
                    let tempTitle = "position";
                    let data = aggregated['position'];
                    let maxValue = Math.max(...data.map(item => item.totalClicks));
                    let niceTicks = getNiceTicks(maxValue);

                    const isEmpty = !data.length || data.every(d => d.totalClicks === 0);

                    return (
                        <div className="chart-card" key={index}>
                            <div className="chart-title">
                                <h1 className="has-text-centered" style={{ fontSize: "1.5rem", fontFamily: "Inter, sans-serif" }}>
                                    {title}
                                </h1>
                            </div>
                            {isEmpty ? (
                                <div style={{ height: 380, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                    <p>Not enough data</p>
                                </div>
                            ) : (
                                <ResponsiveContainer height={380}>
                                    <BarChart data={data} margin={{ bottom: 20 }} >
                                        <XAxis type="category" dataKey="range">
                                            <Label value={xAxisLabel} offset={-10} position="insideBottom" style={{ fontSize: 12 }} />
                                        </XAxis>
                                        <YAxis type="number" domain={[1, Math.max(...niceTicks)]} ticks={niceTicks}>
                                            <Label value={yAxisLabel} angle={-90} position="insideLeft" style={{ textAnchor: "middle", fontSize: 12 }} />
                                        </YAxis>
                                        <RechartsTooltip
                                            content={<CustomTooltipHover payload={aggregated[tempTitle.toLowerCase()]} />}
                                            cursor={{ stroke: "none" }}
                                            wrapperStyle={{ pointerEvents: "none", zIndex: 1000 }}
                                        />
                                        <Bar dataKey="totalClicks" fill="#80c2ff" barSize={25}>
                                            {data.map((_, index) => <Cell key={`cell-${index}`} />)}
                                            <LabelList dataKey="totalClicks" position="top" style={{ fontSize: 12, fill: "#000", fontWeight: 500 }} />
                                        </Bar>
                                    </BarChart>
                                </ResponsiveContainer>
                            )}
                        </div>
                    );
                })}


                <div className="chart-card">
                    <TopQueriesChart data={topKeywords} />
                </div>

            </div>

            {/* <div className="impression-number-stats">
                    <ByImpressionsAndNumbers gscKeywordsData={gscKeywordsData} />
                </div> */}
            <div className="gsc-table w-100">
                <AbunTable
                    tableContentName=""
                    tableName="Pages to Update for Quick Ranking Wins"
                    tableDescription="These pages are currently ranking on the 2nd & 3rd page of Google. Update the content on them to push them onto page 1 to drive more traffic."
                    tableData={quickRankingData}
                    columnDefs={columnDefs}
                    pageSizes={pageSizes}
                    initialPageSize={pageSizes[0]}
                    noDataText="No Data Found."
                    enableSorting={true}
                    defaultSortingState={[{ id: "position", desc: false }]}
                />
            </div>

            <div className="gsc-table w-100">
                <AbunTable
                    tableContentName=""
                    tableName="Device Performance Comparison"
                    tableData={devicePerformanceData}
                    columnDefs={columnDefsDeviceComparison}
                    pageSizes={[devicePerformanceData.length]}
                    initialPageSize={5}
                    hidePagination={true}
                    noDataText="No Data Found."
                />
            </div>

            <RegexKeywordSection data={gscKeywordsData} />

            {/* <div className="gsc-table w-100">
                <div className="content has-text-centered">
                    <h2 className="title is-4" style={{ color: "black", fontFamily: "Inter", fontWeight: 800 }}>
                        Opportunity Matrix
                    </h2>
                    <p className="subtitle is-6" style={{ color: "#666", fontFamily: "Inter" }}>
                        Quickly spot high-impact SEO opportunities across your content.
                    </p>

                    <div className="is-flex is-justify-content-center is-align-items-center">
                        <div className="columns is-multiline is-mobile mt-4 mx-1">
                            <div className="column is-half-mobile is-one-quarter-tablet has-text-left">
                                <span
                                    style={{
                                        backgroundColor: "#2ecc71",
                                        display: "inline-block",
                                        width: "12px",
                                        height: "12px",
                                        borderRadius: "50%",
                                        marginRight: "8px",
                                        verticalAlign: "middle"
                                    }}
                                ></span>
                                <b>Hidden Gems:</b> High impressions but low CTR
                            </div>

                            <div className="column is-half-mobile is-one-quarter-tablet has-text-left">
                                <span
                                    style={{
                                        backgroundColor: "#f39c12",
                                        display: "inline-block",
                                        width: "12px",
                                        height: "12px",
                                        borderRadius: "50%",
                                        marginRight: "8px",
                                        verticalAlign: "middle"
                                    }}
                                ></span>
                                <b>Underperformers:</b> Less visibility & less CTR
                            </div>

                        </div>
                        <div className="columns is-multiline is-mobile mt-4 mx-1">
                            <div className="column is-half-mobile is-one-quarter-tablet has-text-left">
                                <span
                                    style={{
                                        backgroundColor: "#f74216",
                                        display: "inline-block",
                                        width: "12px",
                                        height: "12px",
                                        borderRadius: "50%",
                                        marginRight: "8px",
                                        verticalAlign: "middle"
                                    }}
                                ></span>
                                <b>Content Stars:</b> High CTR and good visibility
                            </div>

                            <div className="column is-half-mobile is-one-quarter-tablet has-text-left">
                                <span
                                    style={{
                                        backgroundColor: "#9b59b6",
                                        display: "inline-block",
                                        width: "12px",
                                        height: "12px",
                                        borderRadius: "50%",
                                        marginRight: "8px",
                                        verticalAlign: "middle"
                                    }}
                                ></span>
                                <b>Long Tail Gems:</b> Low volume but high CTR
                            </div>
                        </div>
                    </div>
                </div>

                <OpportunityMatrixChart />
            </div> */}


            <ErrorAlert ref={failAlertRef} />
            <SuccessAlert ref={successAlertRef} />
        </div>
    );
};

export default GSCData;