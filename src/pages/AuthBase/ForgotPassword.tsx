import { faArrowRight, faStar } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useMutation } from "@tanstack/react-query";
import { useState } from "react";
import { Link } from "react-router-dom";
import LocalIcon from "../../components/Icon/Icon";
import { forgotPasswordMutation } from "../../utils/api";
import { pageURL } from "../routes";
import './ForgotPassword.min.css';



export default function ForgotPassword() {
	// ------------------------- STATES -------------------------
	const [emailInput, setEmailInput] = useState("");
	const [errorMsg, setErrorMsg] = useState("");
	const [successMsg, setSuccessMsg] = useState("");

	// ------------------------- MUTATIONS -------------------------
	const forgotPassword = useMutation(forgotPasswordMutation);

	// =============================================================
	// ------------------------- MAIN CODE -------------------------
	// =============================================================
	function emailIsValid(value: string): boolean {
		return /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(value);
	}

	function submitEmail() {
		setErrorMsg("");
		setSuccessMsg("");
		if (emailIsValid(emailInput)) {
			forgotPassword.mutate(emailInput, {
				onSuccess: () => {
					setSuccessMsg("An email containing the link to reset your account password has " +
						"been sent to this email id if it exists.");
				},
				onError: () => {
					setErrorMsg("Oops! Something went wrong :( Please try again or contact us for support.");
				}
			});
		} else {
			setErrorMsg("Please enter a valid email id (ex. <EMAIL>)");
		}
	}

    const stars = (
      <div className="stars is-flex is-justify-content-center" style={{marginBottom:'0.4rem', gap:'5px'}}>
        {[...Array(5)].map((_, i) => (
          <FontAwesomeIcon key={i} icon={faStar} style={{ fontSize: '1rem', color:'#FFD43B'}}/>
        ))}
      </div>
    );

	return (
		<div className="">
			<div className="forgot-container">
			<div className="card login-card mt-5">
				<div className="card-content p-5">
			        <h1 className="heading-text mt-3 has-text-black mx-3 epilogue">Forgot Password?</h1>

					<div className="content has-text-centered">
						{/* ------------------ EMAIL ID ------------------ */}
						<div className="field mt-5">
							<div className="control">
								<input value={emailInput}
											 className="input is-medium"
											 type="email"
											 name="password"
											 placeholder="Email ID" onChange={(e) => {
									setEmailInput(e.target.value)
								}}  style={{maxWidth:'366px'}}/>
							</div>
						</div>
						<button id="send" className="button mb-3 is-size-6 has-text-black forgot-btn is-large is-responsive mt-5"
										
										disabled={forgotPassword.isLoading}
										onClick={submitEmail}>
							
							    <span className="is-flex is-align-items-center">Send Password Reset Link
										{forgotPassword.isLoading ?
											<LocalIcon iconName={"spinner"} additionalClasses={["icon-white","ml-3"]} style={{filter:'invert(0)'}}/>
										:
											<FontAwesomeIcon icon={faArrowRight} className="ml-2 is-size-6"/>}
								</span> 
						</button>
						<p className="has-text-danger confirm-msg is-small has-text-centered mt-4">{errorMsg}</p>
						<p className="has-text-success confirm-msg is-small has-text-centered mt-4">{successMsg}</p>
					</div>
			        <p className="has-text-black has-text-centered  is-size-6 mt-5">
			          <div className="user-forgot-password">
			        	<Link to={pageURL['login']} className="has-text-black underline-hover">Login</Link>
			        	<span className="divider">|</span>
			        	<Link to={pageURL['signup']} className="has-text-black underline-hover">Create New Account</Link>
			          </div>
			        </p>
				</div>
			</div>
			</div>
		</div>
	)
}
