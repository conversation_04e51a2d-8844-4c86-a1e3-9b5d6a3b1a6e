@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

// bulma overrides
$card-content-padding: 4rem;
$input-shadow: unset;
$input-border-color: $grey-dark;
$input-color: $grey-darker;
$input-placeholder-color: $grey-darker;

@import "bulma/sass/utilities/all";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/button";
@import "bulma/sass/components/card";
@import "bulma/sass/form/all";


.forgot-container {
    padding-top: 0 !important;
    width: 100% ;
    height: 100%;
    min-height: 100vh;
    align-content: center;

    @media  (max-width: 600px) {
        zoom: 0.75;
    }

    .heading-text{
        font-size: 2.8rem;
        font-weight: 700;
        line-height: 1.2em;
        text-align: center;
    }

    .subtitle-text{
        font-size: 1.5rem;
        text-align: center;
        font-family: $primary-font !important;
    }

    .login-card{
        border-radius: 16px;
        box-shadow: 0 8px 17px rgba(0,0,0,.161);
        width: 100%;

        .forgot-btn{
          width: fit-content;
          color: #000 !important;
          background: #fac44b !important;
          border-radius: 15px;
          border: none !important;
          padding: 2rem !important;

          &:hover{
            background: #f3c254 !important;
          }
        }

        .confirm-msg{
            font-family: $primary-font;
            max-width: 400px;
            justify-self: center;
        }

        
        .underline-hover{
            text-decoration: none;
    
            &:hover {
              text-decoration: underline;
            }
        }
    }

    .divider{
        margin-left: 10px;
        margin-right: 10px;
        color: #000;
    }
}
