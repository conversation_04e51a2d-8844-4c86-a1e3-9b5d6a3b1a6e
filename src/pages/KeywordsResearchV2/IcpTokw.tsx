import { useQuery } from "@tanstack/react-query";
import { useEffect, useRef, useState } from "react";
import { Helmet } from 'react-helmet';
import { useLoaderData, useNavigate, useRouteLoaderData } from "react-router-dom";
import AbunButton from "../../components/AbunButton/AbunButton";
import AbunModal from "../../components/AbunModal/AbunModal";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import countries from "../../utils/constants/CountriesforSerp";
import Icon from "../../components/Icon/Icon";
import { BasePageData } from "../../pages/Base/Base";
import { makeApiRequest } from "../../utils/api";
import './IcpTokw.min.css'


interface CountryType {
	location_code: number;
	location_name: string;
	country_iso_code: string;
	suggested?: boolean;
}

export interface PageData {
	has_active_website: boolean;
	has_gsc_integration: boolean;
	has_wp_integration: boolean;
	current_plan_name: string;
	articles_generated: number;
	country_code: string;
	selected_gsc_domain: string;
	current_active_website: string;
	regenerate_competitor: boolean;
}

function IcpToKeyword() {
    // ----------------------------- LOADER -----------------------------
    const { pageData } = useLoaderData() as {
            pageData: PageData;
        };
    const basePageData: BasePageData = useRouteLoaderData("base") as BasePageData

    // -------------------------- STATES --------------------------
    const [topic, setTopic] = useState("")
    const [limit, setLimit] = useState<number | string>(10);
    const [isSpinner, setIsSpinner] = useState(false)
    const [isVerified, _] = useState(basePageData.user_verified || basePageData.user_has_ltd_plans);
    const [isLimitInvalid, setIsLimitInvalid] = useState(false);
    const navigate = useNavigate();    
    const [selectedLocation, setSelectedLocation] = useState<CountryType>({
            location_code: 1,
            location_name: 'Global',
            country_iso_code: 'ZZ',
        });

        
    // -------------------------- QUERIES --------------------------


    // -------------------------- REFS --------------------------
    const errorAlertRef = useRef<any>(null);
    const successAlertRef = useRef<any>(null);

    // ---------------------- EFFECTS ----------------------
    useEffect(() => {
            // Find the country that matches the `country_iso_code` with `pageData.country_code`
            if (pageData.country_code !== "ZZ") {
                const matchedCountry = countries.find(
                    (country) => country.country_iso_code === pageData.country_code.toUpperCase()
                );
                // If a match is found, update the selected location
                if (matchedCountry) {
                    setSelectedLocation(matchedCountry);
                }
            }
        }, [pageData.country_code]);

    // Creative Logic Implemented on keyword Click
    const handleSubmit = async (e: any) => {
        e.preventDefault();
        errorAlertRef.current?.close();
        // Check if the topic is empty
        if (!topic || !limit) {
            return;
        }
        setIsSpinner(true)
        try {
            const response = await makeApiRequest(
                `/api/frontend/icp-to-kw-research/`,
                'post',
                {
                    icp: topic,
                    count: limit,
                    selectedLocation: selectedLocation,
                }
            );

            const data = response.data;
            if (data.success) {
                const taskId = data.task_id
                const projectId = data.id
                localStorage.setItem(`task_${projectId}`, taskId);
                setIsSpinner(false)
                successAlertRef.current?.show('Successfully Fetched the keywords!');
                navigate(`/keyword-research/?keywordProjectId=${projectId}`);
            } else {
                if (data.reason === "blocked_keyword_used") {
                    setIsSpinner(false)
                    errorAlertRef.current?.show('You have reached your maximum limit for this plan. Please Upgrade !');
                }
                else if  (data.reason === "no_keywords_found") {
                    setIsSpinner(false)
                    errorAlertRef.current?.show('No keywords found. Please try with different keywords.');
                } else {
                    setIsSpinner(false)
                    errorAlertRef.current?.show('Failed to fetch queries from backend API.');
                }
            }
        } catch (err) {
            setIsSpinner(false)
            errorAlertRef.current?.show('An error occurred while fetching Keywords queries.');
        }
    }

    // ============================================================
    // --------------------- MAIN RENDER CODE ---------------------
    // ============================================================
        return (
            <>
                <div className="icp-container w-100">
                    <div className={""}>
                        <div className={" is-flex is-align-items-center is-flex-direction-column  icp-form-container"}>
                            <form className="icp-form w-100"
                                onSubmit={(e) => {
                                    e.preventDefault(); // Prevent the default form submission behavior                                    
                                        handleSubmit(e);                                    
                                }}>
                                <div className="field">
                                <label className="ca-label has-text-black label">Who is your ICP?</label>
                                <div className="control">
                                <input
                                    className="ca-input input"
                                    type="text"
                                    placeholder="Ex. Software Engineers"
                                    value={topic}
                                    onChange={(e) => setTopic(e.target.value)}
                                    style={{ marginTop: '3px', width: "70%" }}
                                    required
                                />
                                </div>
                                </div>
                                <div className="field">
                                <label className="ca-label has-text-black label">How many keywords do you want to generate?  (max 1000)</label>                                
                               <input
                                    className="ca-input input mb-3"
                                    type="number"
                                    placeholder="Enter limit"
                                    value={limit}
                                    onChange={(e) => {
                                            const value = e.target.value;
                                            if (value === "") {
                                                setLimit("");
                                                setIsLimitInvalid(false);
                                                return;
                                            }

                                            if (/^\d+$/.test(value)) {
                                                let intValue = parseInt(value, 10);
                                                if (intValue > 1000) {
                                                intValue = 1000;
                                                }
                                                setLimit(intValue);
                                                setIsLimitInvalid(false);
                                            } else {                                                
                                                setLimit(value);
                                                setIsLimitInvalid(true);
                                            }
                                        }}
                                    min={1}
                                    max={1000}
                                    step={1}
                                    maxLength={4} 
                                    style={{ marginTop: '3px', textAlign: 'center', width: '22%', border: isLimitInvalid ? "1px solid red" : undefined}}
                                    required
                                    />
                                </div>
                                <button
                                type="submit"
                                className="mt-2 button is-responsive is-link" style={{width:'fit-content'}}
                                disabled={!isVerified || !topic || !limit || isSpinner || isLimitInvalid }
                                >
                                <span style={{ marginRight: '0.5rem' }}>{isSpinner ? 'Processing...' : 'PROCEED'}</span>
                                {isSpinner ? (
                                    <Icon iconName="spinner" additionalClasses={["icon-white"]} />
                                ) : (
                                    '➜'
                                )}
                                </button>
                            </form>
                        </div>
                        <SuccessAlert ref={successAlertRef} />
                        <ErrorAlert ref={errorAlertRef} />
                    
                    </div>
                </div>
            </>
        );
    }

export default IcpToKeyword;
