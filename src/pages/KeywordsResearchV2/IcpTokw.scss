// GuestPostFinder.scss
@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/elements/container";
@import 'bulma/sass/form/_all';
@import "bulma/sass/components/tabs";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";

@import "../../assets/bulma-overrides/bulmaOverrides";

.guest-post-table {
    font-family: $primary-font;
 }

.guest-project-abun-table {
    .abun-table-responsive {

        tbody {
            color: #000;
        }
    }
}

.icp-form-container{    
    border-radius: 8px;
    width: fit-content;

    .ca-label {
        font-size: 18px;
        font-weight: 500;
    }
    
    .ca-input::placeholder {
        @media(max-width:480px) {
            font-size: 13px;
        }
    }
    
    .ca-input {
        font-size: 18px;
    }

     h3 {
        font-size: 1.4rem;
        font-weight: 600;
        padding: 1rem;
        align-self: start;

    }

    hr {
        background: #e7e7e7;
        height: 1px;
        margin: 0;
        width: 100%;
    }

}

.icp-form-container{
    font-family: $primary-font !important;

    .not-found-p{
        font-family: $secondary-font;
    }

    .icp-form{
        justify-items: center;
        display: flex;
        flex-direction: column;
        padding: 1rem;
        padding-left: 1.6rem;
    }
}
