import { lazy, Suspense } from 'react';
import AbunLoader from '../../../components/AbunLoader/AbunLoader';
import { KeywordsProjectData } from './KeywordProjectTypes';

// Lazy load the LoadKeywordProject component
const LoadKeywordProjectLazy = lazy(() => import('./LoadKeywordProject'));

// Wrapper component that handles the lazy loading with a fallback
export default function LazyLoadKeywordProject(props: KeywordsProjectData) {
  return (
    <Suspense fallback={
      <div className="keyword-project-content">
        <div className="loadingData w-100 is-flex is-justify-content-center is-align-items-center">
          <AbunLoader show={true} height="20vh" />
        </div>
      </div>
    }>
      <LoadKeywordProjectLazy {...props} />
    </Suspense>
  );
}
