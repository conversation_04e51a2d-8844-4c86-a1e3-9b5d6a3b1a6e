import React from 'react';
import AbunButton from "../../../components/AbunButton/AbunButton";
import AbunModal from "../../../components/AbunModal/AbunModal";
import { DeleteData } from './KeywordProjectTypes';
import { UseMutationResult } from '@tanstack/react-query';
import { AxiosResponse } from 'axios';

interface CreateCustomKeywordModalProps {
    showCreateCustomkeywordModal: boolean;
    customKeyword: string;
    setCustomKeyword: (keyword: string) => void;
    setShowCreateCustomkeywordModal: (show: boolean) => void;
    createCustomKeyword: UseMutationResult<AxiosResponse<any, any>, unknown, any, unknown>;
    projectId: string;
    refetch: () => Promise<any>;
    successAlertRef: React.MutableRefObject<any>;
    failAlertRef: React.MutableRefObject<any>;
}

export function CreateCustomKeywordModal({
    showCreateCustomkeywordModal,
    customKeyword,
    setCustomKeyword,
    setShowCreateCustomkeywordModal,
    createCustomKeyword,
    projectId,
    refetch,
    successAlertRef,
    failAlertRef
}: CreateCustomKeywordModalProps) {
    return (
        <AbunModal 
            active={showCreateCustomkeywordModal}
            headerText={"Write a custom keyword: " + "Unlimited design"}
            closeable={true}
            closeableKey={true}
            hideModal={() => {
                setShowCreateCustomkeywordModal(false);
                setCustomKeyword("");
            }}
        >
            <div className={"has-text-centered"}>
                <input 
                    type="text" 
                    className={"input "} 
                    placeholder={"Enter your custom keyword here..."} 
                    value={customKeyword} 
                    onChange={(e) => {
                        setCustomKeyword(e.target.value);
                    }} 
                />
                <AbunButton 
                    type={"success"}
                    className={"mt-4"}
                    disabled={createCustomKeyword.isLoading}
                    clickHandler={() => {
                        setShowCreateCustomkeywordModal(false);
                        createCustomKeyword.mutate({
                            projectId: projectId,
                            customKeyword: customKeyword
                        }, {
                            onSuccess: () => {
                                // Keyword generated successfully
                                setCustomKeyword("");
                                refetch();
                                successAlertRef.current?.show("Custom keyword added successfully!");
                                setTimeout(() => {
                                    successAlertRef.current?.close();
                                }, 5000);
                            },
                            onError: () => {
                                setCustomKeyword("");
                                failAlertRef.current?.show("Failed to add custom keyword. Please try again after some time.");
                                setTimeout(() => {
                                    failAlertRef.current?.close();
                                }, 5000);
                            }
                        });
                    }}
                >
                    Proceed
                </AbunButton>
            </div>
        </AbunModal>
    );
}

interface DeleteKeywordModalProps {
    showDeletePopUp: boolean;
    deleteData: DeleteData | null;
    setShowDeletePopUp: (show: boolean) => void;
    removeKeyword: UseMutationResult<AxiosResponse<any, any>, unknown, any, unknown>;
    refetch: () => Promise<any>;
    successAlertRef: React.MutableRefObject<any>;
    failAlertRef: React.MutableRefObject<any>;
    setTotalKeywords: React.Dispatch<React.SetStateAction<number>>;
    setTotalVeryEasyKeywords: React.Dispatch<React.SetStateAction<number>>;
    setTotalEasyKeywords: React.Dispatch<React.SetStateAction<number>>;
    setTotalModerateKeywords: React.Dispatch<React.SetStateAction<number>>;
    setTotalHardKeywords: React.Dispatch<React.SetStateAction<number>>;
    setTotalVeryHardKeywords: React.Dispatch<React.SetStateAction<number>>;
}

export function DeleteKeywordModal({
    showDeletePopUp,
    deleteData,
    setShowDeletePopUp,
    removeKeyword,
    refetch,
    successAlertRef,
    failAlertRef,
    setTotalKeywords,
    setTotalVeryEasyKeywords,
    setTotalEasyKeywords,
    setTotalModerateKeywords,
    setTotalHardKeywords,
    setTotalVeryHardKeywords
}: DeleteKeywordModalProps) {
    return (
        <AbunModal
            active={showDeletePopUp}
            headerText="Confirm Deletion"
            closeable={true}
            hideModal={() => setShowDeletePopUp(false)}
        >
            <div className={"has-text-centered"}>
                <p>Are you sure you want to delete the keyword: <strong>{deleteData?.keyword}</strong>?</p>
                <p>Note: All associated titles for this keyword will also be deleted.</p>
                <AbunButton 
                    type={"danger"}
                    className={"mt-4 "}
                    disabled={removeKeyword.isLoading}
                    clickHandler={() => {
                        setShowDeletePopUp(false);
                        if (deleteData) {
                            removeKeyword.mutate(
                                {
                                    keywordHash: deleteData.keywordHash,
                                    keyword: deleteData.keyword,
                                },
                                {
                                    onSuccess: () => {
                                        refetch();
                                        successAlertRef.current?.show("Keyword removed successfully!");
                                        setTotalKeywords(prev => prev - 1);
                                        if (deleteData.difficultyScore < 1000) {
                                            setTotalVeryEasyKeywords(prev => prev - 1);
                                        } else if (deleteData.difficultyScore >= 1000 && deleteData?.difficultyScore < 15000) {
                                            setTotalEasyKeywords(prev => prev - 1);
                                        } else if (deleteData.difficultyScore >= 15000 && deleteData?.difficultyScore < 35000) {
                                            setTotalModerateKeywords(prev => prev - 1);
                                        } else if (deleteData.difficultyScore >= 35000 && deleteData?.difficultyScore < 100000) {
                                            setTotalHardKeywords(prev => prev - 1);
                                        } else if (deleteData.difficultyScore >= 100000) {
                                            setTotalVeryHardKeywords(prev => prev - 1);
                                        }
                                        setTimeout(() => {
                                            successAlertRef.current?.close();
                                        }, 5000);
                                        setShowDeletePopUp(false); // Close the modal after deletion
                                    },
                                    onError: () => {
                                        failAlertRef.current?.show("Failed to remove keyword. Please try again later.");
                                        setTimeout(() => {
                                            failAlertRef.current?.close();
                                        }, 5000);
                                        setShowDeletePopUp(false); // Close the modal if there's an error
                                    },
                                }
                            );
                        }
                    }}
                >
                    Proceed
                </AbunButton>
            </div>
        </AbunModal>
    );
}
