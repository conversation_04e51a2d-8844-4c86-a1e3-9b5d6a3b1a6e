import { MutableRefObject } from 'react';

export interface KeywordsProjectData {
    projectName: string;
    totalKeywords: number;
    totalTrafficVolume: number;
    projectId: string;
    locationIsoCode: string;
    mostRecentArtTitleTimestamp: string;
    icpKeywordProject: boolean;
    dateCreated: string;
    failAlertRef: MutableRefObject<any>;
    successAlertRef: MutableRefObject<any>;
    setSelectedKeywordRow: React.Dispatch<React.SetStateAction<KeywordsProjectTableProps | null>>;
    setSelectedPage: React.Dispatch<React.SetStateAction<string>>;
}

export interface KeywordsProjectTableProps {
    keyword: string;
    keywordHash: string;
    keywordTraffic: number;
    difficultyScore: string;
    titlesGenerated: boolean;
    mostRecentArtTitleTimestamp: string;
    kwVolume: boolean;
}

export interface DeleteData {
    keywordHash: string;
    keyword: string;
    difficultyScore: number;
}

export interface CustomContextMenuProps {
    children: React.ReactNode;
    url: string;
    normalClick: () => void;
    CtrlOrMetaClick: () => void;
}

export interface CountryType {
	location_code: number;
	location_name: string;
	country_iso_code: string;
	suggested?: boolean;
}