import { useMutation, useQuery } from "@tanstack/react-query";
import { ColumnDef, createColumnHelper, RowData, RowModel } from "@tanstack/react-table";
import { useEffect, useMemo, useRef, useState } from "react";
import { useLoaderData, useLocation, useRevalidator, useRouteLoaderData } from "react-router-dom";
import { Tooltip } from "react-tooltip";
import articleIntegrationIcon from '../../assets/images/article-integration-logo.webp';
import ghlLogo from '../../assets/images/ghl-logo.webp';
import webflowIconPrimary from '../../assets/images/webflow-logo-primary.webp';
import webflowIconSuccess from '../../assets/images/webflow-logo.webp';
import wixIconPrimary from '../../assets/images/wix-logo-primary.webp';
import wordpressIconPrimary from '../../assets/images/wordpress-logo-primary.webp';
import wordpressIconSuccess from '../../assets/images/wordpress-logo.webp';
import AbunLoader from "../../components/AbunLoader/AbunLoader";
import AbunModal from "../../components/AbunModal/AbunModal";

import { faGear } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Link } from 'react-router-dom';
import AbunTable, { IndeterminateCheckbox } from "../../components/AbunTable/AbunTable";
import GenericButton from "../../components/GenericButton/GenericButton";
import Icon from "../../components/Icon/Icon";
import LinkButton from "../../components/LinkButton/LinkButton";
import './ResearchedKeywords.min.css';

import CustomContextMenu from "../../components/CustomContextMenu/CustomContextMenu";
import {
	archiveBulkArticleMutation,
	createCustomTitleForKeywordMutation,
	generateBulkV2ArticleMutation,
	GenerateTitlesFromKeyword,
	generateV2ArticleMutation,
	getArticleProgress,
	getLatestContext,
	getTaskProgress,
	postArticleMutation,
	postBulkArticleMutation,
	saveContextMutation
} from "../../utils/api";

import { useNavigate } from "react-router-dom";
import Select, { components } from 'react-select';
import AbunButton from "../../components/AbunButton/AbunButton";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { APIError, editTitleMutation, makeApiRequest, saveSettingsMutation } from "../../utils/api";
import allSupportedLanguagesOptions from "../../utils/constants/allSupportedLanguagesOptions";
import '../Articles/Articles.min.css';
import { AllIntegrations } from "../Articles/ShowV2Articles";
import { BasePageData } from "../Base/Base";
import { CreateArticlePageData } from "../CreateArticle/CreateArticle";
import { pageURL } from "../routes";
import { Language } from '../Settings/ArticleDetails';

interface ServerData {
	titles_data: Array<ArticleTitle>
	all_integrations: any
	google_search_console_integrated: boolean;
	user_verified: boolean;
	keyword: string;
	keywordHash: string;
	keywordTraffic: number;
	difficultyScore: string;
	locationIsoCode: string;
	keyword_project_id: string;
	pattern: string;
	total: string;
}

interface ParamsData {
	keywordProjectId: string;
	keywordHash: string;
}

// type for edit btn for seo title table 
interface EditableTitle {
	title: string;
	isEditing: boolean;
	articleUID: string;
	originalTitle: string;
}

interface ArticleTitle {
	articleUID: string
	articleTitle: string
	keyword: string
	keywordHash: string;
	keywordTraffic: number | null
	internalLinks: number | null
	externalLinks: number | null
	images: number | null
	wordCount: number | null
	isProcessing: boolean
	isGenerated: boolean
	isPosted: boolean
	isFailed: boolean
	isArchived: boolean
	postLink: string
	isUserAdded: boolean
	postedTo: string
}

interface InitialSortingState {
	id: string;
	desc: boolean;
}


export default function ShowTitlesForKeyword() {
	// ----------------------- REFS -----------------------
	const navigate = useNavigate();
	const location = useLocation();
	const tableRef = useRef<{ refetchData: () => Promise<void> }>(null);
	const hasStartedPolling = useRef({});

	// ----------------------- NON STATE CONSTANTS -----------------------
	const pageSizes = [5, 10, 15, 30, 50, 100];

	// ----------------------- STATES -----------------------
	const [tableData, setTableData] = useState<Array<ArticleTitle>>([]);
	const [requestModalActive, setRequestModalActive] = useState(false);
	const [integrationModalActive, setIntegrationModalActive] = useState(false);
	const [modalText, setModalText] = useState("");
	const [integrationWithUniqueID, setIntegrationWithUniqueID] = useState<Array<AllIntegrations>>([]);
	const [selectedIntegrationUniqueID, setSelectedIntegrationUniqueID] = useState("");
	const [selectedIntegration, setSelectedIntegration] = useState("");
	const [showCreateCustomTitleModal, setShowCreateCustomTitleModal] = useState(false);
	const [showAddContextModal, setShowAddContextModal] = useState(false);
	const [customTitle, setCustomTitle] = useState("");
	const [isPseoTitleRedirect, SetIsPseoTitleRedirect] = useState(false)
	const [initialSortingState, setInitialSortingState] = useState<InitialSortingState[]>([{
		id: "wordCount", desc: true
	}])
	const [userVerified, setUserVerified] = useState(false);
	const [titlesGenerated, setTitlesGenerated] = useState(false);
	const [openDropdown, setOpenDropdown] = useState("");
	const [ArticleGenProgressMap, setArticleGenProgressMap] = useState({}); // Object to track progress of each task
	const [ArticleGenProgressMessageMap, setArticleGenProgressMessageMap] = useState({}); // Object to track messages of each task
	const [saveArticleContext, setSaveArticleContext] = useState("")

	// ----------------------- REFS -----------------------
	const successAlertRef = useRef<any>(null);
	const failAlertRef = useRef<any>(null);
	const taskPollingIntervals = useRef({});  // To track intervals for each task
	const navigatedArticles = useRef(new Set<string>());

	// ----------------------------- LOADER -----------------------------
	const { pageData, createArticlePageData, params } = useLoaderData() as {
		pageData: ServerData;
		createArticlePageData: CreateArticlePageData;
		params: ParamsData;
	};
	const { revalidate } = useRevalidator();

	// ----------------------- BASE DATA -----------------------
	const basePageData = useRouteLoaderData("base") as BasePageData;

	// ----------------------- MUTATIONS -----------------------
	const generateArticle = useMutation(generateV2ArticleMutation);
	const postArticle = useMutation(postArticleMutation);
	const postSelectedArticles = useMutation(postBulkArticleMutation);
	const archiveSelectedArticles = useMutation(archiveBulkArticleMutation);
	const createCustomTitleForKeyword = useMutation(createCustomTitleForKeywordMutation);
	const titlesForKeywordMutation = useMutation(GenerateTitlesFromKeyword);

	// --------- For seo programmatic ----------
	const isListOfTitlesRoute = location.pathname.includes("/pseo-list-of-titles");
	const [editableTitles, setEditableTitles] = useState<EditableTitle[]>([]);
	const [selectedRows, setSelectedRows] = useState<RowModel<RowData>>();
	const [bulkActionsEnabled, setBulkActionsEnabled] = useState(true);
	const generateSelectedArticles = useMutation(generateBulkV2ArticleMutation);
	const [articleContext, setArticleContext] = useState("");
	const [totalRow, setTotalRow] = useState(pageData.total)
	const [pattern, setPattern] = useState(pageData.pattern)
	const [oldContext, setOldContext] = useState("")
	const allBulkOptions = useMemo(() => [
		{
			text: "Generate Article",
			key: "generate"
		},
		{
			text: "Publish",
			key: "publish",
		},

	], []);
	const [availableBulkActions, setAvailableBulkActions] = useState(allBulkOptions);
	const [languagePreference, setLanguagePreference] = useState("");
	const saveSettings = useMutation(saveSettingsMutation);
	const saveInstructionContext = useMutation(saveContextMutation)
	const editTitle = useMutation(editTitleMutation);

	const { data, isFetching, error, refetch } = useQuery({
		queryKey: ['getLatestContext'],
		queryFn: () => getLatestContext(),
		cacheTime: 0,
		refetchOnWindowFocus: false,
	});

	useEffect(() => {
		const fetchArticleLanguagePreference = async () => {
			try {
				const response = await makeApiRequest(
					'/api/frontend/get-article-language-preference/',
					'get'
				);

				const data = response.data;
				setArticleContext(data.article_context)
				if (data.article_language_preference) {
					setLanguagePreference(data.article_language_preference);
				} else {
					setLanguagePreference("American English (en-us)");
				}
			} catch (err) {
				if (err instanceof APIError) {
					if (err.statusCode === 401) {
						failAlertRef.current?.show('Unauthorized. Please Refresh the Page.');
					} else {
						failAlertRef.current?.show('An error occurred while fetching language preference.');
					}
				}
			}
		};

		fetchArticleLanguagePreference();
	}, []);

	useEffect(() => {
		if (isListOfTitlesRoute && selectedRows?.rows.length) {
			// Map rows to their original data which matches ArticleTitle
			const rows = selectedRows.rows.map(row => row.original) as ArticleTitle[];

			// Check if any row has `isGenerated: true` and any row has `isGenerated: false`
			const hasGenerated = rows.some(row => row.isGenerated);
			const hasNotGenerated = rows.some(row => !row.isGenerated);
			const hasNotPosted = rows.some(row => row.isPosted);
			//   console.log(rows.some(row => row.isPosted))
			const filteredActions = allBulkOptions.filter(option => {
				// If any row has isGenerated = false, show "Generate"
				if (option.key === "generate" && hasNotGenerated) return true;

				// If any row has isGenerated = true and isPosted = false, show "Publish"
				if (option.key === "publish" && hasGenerated && !hasNotPosted) return true;

				return false;
			});

			// Set the filtered available bulk actions
			setAvailableBulkActions(filteredActions);
		} else {
			setAvailableBulkActions([]);
		}
	}, [isListOfTitlesRoute, selectedRows, allBulkOptions]);

	useEffect(() => {
		const projectId = params.keywordProjectId;
		const storedTaskId = localStorage.getItem(`task_${projectId}`);
		const storedPattern = localStorage.getItem(`pattern_${projectId}`);

		if (storedTaskId && storedPattern && !hasStartedPolling.current[projectId]) {
			hasStartedPolling.current[projectId] = true;
			SetIsPseoTitleRedirect(true);
			setPattern(storedPattern);
			pollTaskProgress(storedTaskId, projectId);
		}
	}, [isListOfTitlesRoute, params.keywordProjectId]);


	const pollTaskProgress = (taskId, projectId) => {
		const interval = setInterval(() => {
			getTaskProgress(taskId)
				.then((res) => {
					const status = res.data.status;
					if (status === "success") {
						clearInterval(interval);
						// setTimeout(() => successAlertRef.current?.close(), 5000);
						tableRef.current?.refetchData().then(() => {
							SetIsPseoTitleRedirect(false);
						});
						localStorage.removeItem(`task_${projectId}`);
						localStorage.removeItem(`pattern_${projectId}`);
						localStorage.removeItem(`kwHash_${projectId}`);
					} else if (status === "failure") {
						clearInterval(interval);
						failAlertRef.current?.show("Task failed. Please try again.");
						setTimeout(() => failAlertRef.current?.close(), 5000);
						SetIsPseoTitleRedirect(false)
					}
				})
				.catch((err) => {
					console.error("Error fetching task progress:", err);
					clearInterval(interval);
					failAlertRef.current?.show("Error fetching task progress.");
					setTimeout(() => failAlertRef.current?.close(), 5000);
					SetIsPseoTitleRedirect(false)
				});
		}, 2000);
	};


	const handleDataTransformed = (data) => {
		if (isListOfTitlesRoute && data.data) {
			const formattedTitles = data.data.map((title) => ({
				title: title.articleTitle,
				isEditing: false,
				articleUID: title.articleUID,
				originalTitle: title.articleTitle,
			}));
			setEditableTitles(formattedTitles);
		}
	};

	const toggleEdit = (index: number) => {
		setEditableTitles((prev) =>
			prev.map((item, i) =>
				i === index ? { ...item, isEditing: !item.isEditing } : item
			)
		);
	};

	const handleTitleChange = (index: number, value: string) => {
		setEditableTitles((prev) =>
			prev.map((item, i) => (i === index ? { ...item, title: value } : item))
		);
	};

	const handleSave = (index: number) => {
		const item = editableTitles[index];
		if (!item) return;
		const { title, originalTitle, articleUID } = item;
		if (title.trim() === originalTitle.trim()) {
			// Just exit edit mode
			setEditableTitles((prev) =>
				prev.map((item, i) =>
					i === index ? { ...item, isEditing: false } : item
				)
			);
			return;
		}
		setEditableTitles((prev) =>
			prev.map((item, i) =>
				i === index
					? { ...item, isEditing: false, originalTitle: title } // sync originalTitle
					: item
			)
		);
		editTitle.mutate(
			{ articleUID, title },
			{
				onSuccess: () => {
					console.log("Title updated");
				},
				onError: (error) => {
					console.error("Error updating title:", error);
				},
			}
		);
	};

	const handleBulkActionSuccess = (message: string) => () => {
		setRequestModalActive(false);
		revalidate()
		successAlertRef.current?.show(message);
	};

	const handleBulkActionError = (message: string) => () => {
		setRequestModalActive(false);
		failAlertRef.current?.show(message);
	};


	const integrationLogoPrimary = {
		"wordpress": wordpressIconPrimary,
		"webflow": webflowIconPrimary,
		"wix": wixIconPrimary,
		"ghl": ghlLogo,
	}

	const CustomMenuList = (props) => {
		return (
			<div>
				<div
					style={{
						padding: "10px",
						cursor: "pointer",
						borderBottom: "1px solid #ddd",
						textAlign: "center",
						background: "#f9f9f9"
					}}
					onClick={() => {
						props.setShowAddContextModal(true);
						setSaveArticleContext("");
					}}

				>
					<span> + Add Context & Instructions</span>
				</div>
				<components.MenuList {...props} />
			</div>
		);
	};

	// Custom styles for truncation
	const customStyles = {
		control: (provided) => ({
			...provided,
			width: '300px',
			minHeight: '40px',
		}),
		option: (provided) => ({
			...provided,
			whiteSpace: 'nowrap',
			overflow: 'hidden',
			textOverflow: 'ellipsis',
			width: '100%',
			display: 'block',
		}),
		singleValue: (provided) => ({
			...provided,
			whiteSpace: 'nowrap',
			overflow: 'hidden',
			textOverflow: 'ellipsis',
			maxWidth: '100%',
		}),
	};

	// Custom Option Component
	const CustomOption = (props) => {
		const { data, innerRef, innerProps } = props;
		const [isHovered, setIsHovered] = useState(false);

		return (
			<div
				ref={innerRef}
				{...innerProps}
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}
				style={{
					display: "flex",
					justifyContent: "space-between",
					alignItems: "center",
					padding: "10px",
					cursor: "pointer",
					background: isHovered ? "#f0f0f0" : "transparent",
					position: "relative",
				}}
			>
				<span style={{ flex: 1, overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap", fontSize: '16px' }}>
					{data.label.length > 50 ? `${data.label.substring(0, 50)}...` : data.label}
				</span>

				{/* Show edit button only when hovered */}
				{isHovered && (
					<button
						onClick={(e) => {
							e.stopPropagation(); // Prevent dropdown from closing
							handleEditContext(data.value); // Call edit function
						}}
						className="internal-link-edit-button"
						style={{
							border: "none",
							background: "transparent",
							cursor: "pointer",
							fontSize: "14px",
							marginLeft: "10px",
						}}
					>
						✏️
					</button>
				)}
			</div>
		);
	};


	const options = data?.data?.contexts?.map((context) => ({
		label: context,
		value: context,
	})) || [];

	const contextOptions = [{ value: '', label: 'No Context' }, ...options];
	// const options = [
	// 	{ label: 'No Context', value: '' }, // First option
	// 	...(data?.data?.contexts?.map((context) => ({
	// 	  label: context,
	// 	  value: context,
	// 	})) || [])
	//   ];


	function saveContext(context: string, oldContext: string) {

		saveInstructionContext.mutate({ context, oldContext: oldContext || "" }, {
			onSuccess: () => {
				setSaveArticleContext("")
				setShowAddContextModal(false)
				successAlertRef.current.show("Context Saved!");
				refetch()
				setTimeout(() => {
					try {
						if (successAlertRef.current) {
							successAlertRef.current.close();
						}
					} catch (e) { }
				}, 3000);
			},
			onError: () => {
				failAlertRef.current?.show("Oops! Something went wrong :( Please try again later or contact us for further support.");
				setShowAddContextModal(false)
			}
		});
	}

	function handleContextChange(Context: Language) {
		const selectedContext = Context?.value || "";
		if (selectedContext == articleContext) {
			return;
		}
		setArticleContext(selectedContext);

		// Trigger save settings immediately
		saveSettings.mutate({
			settingsToSave: [
				{ settingName: 'article_context', settingValue: selectedContext },
			]
		}, {
			onSuccess: () => {
				successAlertRef.current.show("Changes Saved!");
				setTimeout(() => {
					try {
						if (successAlertRef.current) {
							successAlertRef.current.close();
						}
					} catch (e) { }
				}, 3000);
			},
			onError: () => {
				failAlertRef.current?.show("Oops! Something went wrong :( Please try again later or contact us for further support.");
			}
		});
	}

	function handleEditContext(context) {
		setSaveArticleContext(context)
		setOldContext(context)
		setShowAddContextModal(true)
	}
	// ----------------------- EFFECTS -----------------------
	useEffect(() => {
		document.title = "Articles | Abun"
	}, []);

	useEffect(() => {
		if (pageData) {
			if (pageData['titles_data']) {
				setTitlesGenerated(true);
				// filter out the articles that do not have props.keyword.keyword
				// pageData['titles_data'] = pageData['titles_data'].filter((article: ArticleTitle) => {
				// 	return article.keyword === pageData.keyword || article.keywordHash === pageData.keywordHash;
				// })

				// sort the data according to the descending order of keyword traffic
				// pageData['titles_data'].sort((a: ArticleTitle, b: ArticleTitle) => {
				// 	if (a.keywordTraffic === null) {
				// 		return 1;
				// 	} else if (b.keywordTraffic === null) {
				// 		return -1;
				// 	} else {
				// 		return b.keywordTraffic - a.keywordTraffic;
				// 	}
				// });

				// sorting the data so that user added titles are shown first
				// pageData['titles_data'].sort((a: ArticleTitle, b: ArticleTitle) => {
				// 	if (a.isUserAdded && !b.isUserAdded) {
				// 		return -1;
				// 	} else if (!a.isUserAdded && b.isUserAdded) {
				// 		return 1;
				// 	} else {
				// 		return 0;
				// 	}
				// });
			}

			if (pageData['all_integrations_with_unique_id'] && pageData['all_integrations_with_unique_id'].length > 0) {
				const integrations = pageData['all_integrations_with_unique_id'];

				setIntegrationWithUniqueID(integrations);
				setSelectedIntegration(integrations[0].integrationName || "");
				setSelectedIntegrationUniqueID(integrations[0].integrationUniqueID || "");
			}
			setUserVerified((pageData as any)['user_verified']);

			// hide the table column according to the selected tab
			const tab_to_hide = document.querySelector("#article-titles-table .hide-column-child")?.parentElement || document.querySelector("#article-titles-table .hide-column");

			// filter the data according to the selected tab
			while (tab_to_hide && tab_to_hide.classList.contains("hide-column")) {
				tab_to_hide.classList.remove("hide-column");
			}
			// setTableData((pageData as any)['titles_data'].filter((article: ArticleTitle) => {
			// 	return !article.isArchived;
			// }));			
			// if any of the article is generating, and windows.href does not contains `/articles/edit/` we refetch the data after 15 seconds
			// if ((pageData as any)['titles_data'].some((article: ArticleTitle) => article.isProcessing)) {
			// 	setTimeout(() => {
			// 		if (!window.location.href.includes("/articles/edit/")) {
			// 			revalidate();
			// 		}
			// 	}, 15000);
			// }
		}
	}, [pageData]);

	const keywordResearchURL = `${pageURL['keywordResearch']}?keywordProjectId=${pageData.keyword_project_id}`;

	const handleBackClick = () => {
		const targetURL = isListOfTitlesRoute ? '/programmatic-seo' : keywordResearchURL;
		navigate(targetURL);
	};

	// =========================================================
	// ----------------------- MAIN CODE -----------------------
	// =========================================================
	function generateArticleHandler(articleUID: string, articleContext?: string) {
		// Step 1: Start the article generation process
		localStorage.setItem(`articleGen-${articleUID}`, articleUID);

		setArticleGenProgressMap(prev => {
			const updatedMap = { ...prev, [articleUID]: 1 };
			localStorage.setItem("articleGenProgressMap", JSON.stringify(updatedMap)); // Persist
			return updatedMap;
		});

		setArticleGenProgressMessageMap(prev => {
			const updatedMap = { ...prev, [articleUID]: "In Queue..." };
			localStorage.setItem("articleGenProgressMessageMap", JSON.stringify(updatedMap)); // Persist
			return updatedMap;
		});
		generateArticle.mutate({ articleUID, context: articleContext ?? "" }, {
			onSuccess: (data) => {
				const responseData = data?.data;
				if (responseData?.status === "sent_for_processing") {
					setArticleContext("")
					// Store articleUID in localStorage for tracking
					localStorage.setItem(`articleGen-${articleUID}`, articleUID);

					setArticleGenProgressMap(prev => {
						const updatedMap = { ...prev, [articleUID]: 1 };
						localStorage.setItem("articleGenProgressMap", JSON.stringify(updatedMap)); // Persist
						return updatedMap;
					});

					setArticleGenProgressMessageMap(prev => {
						const updatedMap = { ...prev, [articleUID]: "In Queue..." };
						localStorage.setItem("articleGenProgressMessageMap", JSON.stringify(updatedMap)); // Persist
						return updatedMap;
					});

					// Start polling for progress using articleUID
					pollArticleProgress(articleUID);
				} else if (responseData?.status === "rejected") {
					// Handle rejection cases
					cleanupArticleProgress(articleUID);
					const reason = responseData.reason;
					if (reason === "max_limit_reached") {
						failAlertRef.current?.show(
							"Article generation request failed. " +
							"You have reached your max article generation limit for the month."
						);
					} else {
						failAlertRef.current?.show(`Article generation request failed. Error ID: ${reason}`);
					}
				} else {
					failAlertRef.current?.show(
						`Article generation request returned unknown status ${responseData?.status}. Please contact us if there's any issue.`
					);
				}
				setInitialSortingState(initialSortingState);
			},
			onError: (error: Error) => {
				console.error("Article generation request error:", error);
				cleanupArticleProgress(articleUID);
				failAlertRef.current?.show("Article generation request failed. Please try again later");
				setTimeout(() => {
					failAlertRef.current?.close();
				}, 5000);
			},
		});
	}

	// Step 2: Poll for article generation progress using articleUID
	const pollArticleProgress = (articleUID: string) => {
		if (taskPollingIntervals.current[articleUID]) return; // Avoid duplicate polling
		// Poll every 2 seconds
		taskPollingIntervals.current[articleUID] = setInterval(() => {
			getArticleProgress(articleUID).then((res) => {
				if (res.data.progress) {
					// Update progress and message
					const progressInfo = res.data.progress;
					setArticleGenProgressMap(prev => {
						const updatedMap = { ...prev, [articleUID]: progressInfo.percent };
						localStorage.setItem("articleGenProgressMap", JSON.stringify(updatedMap)); // Persist
						return updatedMap;
					});
					setArticleGenProgressMessageMap(prev => {
						const updatedMap = { ...prev, [articleUID]: progressInfo.description };
						localStorage.setItem("articleGenProgressMessageMap", JSON.stringify(updatedMap)); // Persist
						return updatedMap;
					});

					if (progressInfo.percent === 100) {
						// Task is complete
						clearInterval(taskPollingIntervals.current[articleUID]);
						setTimeout(() => {							
							// 	navigate(`/articles/edit/${articleUID}/`);							
							setTimeout(() => {
								cleanupArticleProgress(articleUID);
								tableRef.current?.refetchData()
							}, 15000);
						}, 3000);
					}
				}
			}).catch((error) => {
				console.error("Error fetching article progress:", error);
				clearInterval(taskPollingIntervals.current[articleUID]);
				failAlertRef.current?.show("Error tracking article generation progress. Please try again later.");
				cleanupArticleProgress(articleUID);
			});
		}, 2000);
	};

	useEffect(() => {
		const storedProgress = JSON.parse(localStorage.getItem("articleGenProgressMap") || "{}");
		const storedMessages = JSON.parse(localStorage.getItem("articleGenProgressMessageMap") || "{}");

		// console.log("storedMessages--", storedMessages)
		setArticleGenProgressMap(storedProgress);
		setArticleGenProgressMessageMap(storedMessages);

		// Restart polling or handle navigation
		Object.keys(storedProgress).forEach((articleUID) => {
			const progress = storedProgress[articleUID];
			if (progress < 100) {
				pollArticleProgress(articleUID); // Restart polling
			} else if (progress === 100 && !navigatedArticles.current.has(articleUID)) {
				navigatedArticles.current.add(articleUID);				
				// navigate(`/articles/edit/${articleUID}/`);
				setTimeout(() => {
					cleanupArticleProgress(articleUID);
					tableRef.current?.refetchData()
					}, 15000);
			}
		});
	}, []);

	//Step 3: Cleanup after completion or error
	const cleanupArticleProgress = (articleUID: string) => {
		setArticleGenProgressMap(prev => {
			const updatedMap = { ...prev };
			delete updatedMap[articleUID];
			return updatedMap;
		});

		setArticleGenProgressMessageMap(prev => {
			const updatedMap = { ...prev };
			delete updatedMap[articleUID];
			return updatedMap;
		});


		// Clear the interval
		if (taskPollingIntervals.current[articleUID]) {
			clearInterval(taskPollingIntervals.current[articleUID]);
			delete taskPollingIntervals.current[articleUID];
		}

		// // Remove the articleUID from localStorage
		localStorage.removeItem(`articleGen-${articleUID}`);
		localStorage.removeItem("articleGenProgressMap");
		localStorage.removeItem("articleGenProgressMessageMap");

	};

	function postToBlogHandler(articleUID: string) {
		failAlertRef.current?.close();
		successAlertRef.current?.close();

		setModalText("Posting to your website blog. Please wait...");
		setRequestModalActive(true);
		postArticle.mutate({ articleUID: articleUID, selectedIntegration: selectedIntegration, selectedIntegrationUniqueID: "", selectedCategories: 1 }, {
			onSuccess: () => {
				setRequestModalActive(false);
				revalidate();
				successAlertRef.current?.show(`Article ${articleUID} was posted to your site successfully!`);
			},
			onError: () => {
				setRequestModalActive(false);
				failAlertRef.current?.show(
					"Publishing failed! WordPress rejected the request. A security plugin or Cloudflare might be blocking it. Here are some quick troubleshooting methods: [Fix the issue](https://abun.com/help/why-is-my-abun-article-not-publishing-to-wordpress) or contact support via live chat."
				);
			}
		})
	}

	function setIntegrationAndHideDropDownContent(integrate: string) {
		setSelectedIntegration(integrate);
		setOpenDropdown("");
	}

	const openUrlInNewTab = (url: string) => {
		window.open(url, "_blank");
	}

	function getIntegrationName(integration: string) {
		if (integration.includes("wordpress")) {
			return "wordpress";
		} else if (integration.includes("webflow")) {
			return "webflow";
		} else if (integration.includes("ghl")) {
			return "ghl";
		} else {
			return "wix";
		}
	}

	function truncateSiteDomain(domain: string) {
		if (domain.includes("Webflow")) {
			domain = domain.replace(new RegExp("Webflow - ", 'g'), '');
		} else if (domain.includes("Wix")) {
			domain = domain.replace(new RegExp("Wix - ", 'g'), '');
		} else {
			domain = domain.replace(new RegExp("Wordpress - ", 'g'), '');
		}

		if (domain.length <= 20) {
			return domain;
		} else {
			domain = domain.substring(0, 17) + "...";
		}

		return domain;
	}

	function handleLanguageChange(selectedOption: Language | null) {
		const newLanguage = selectedOption?.value || "";
		setLanguagePreference(newLanguage);
		saveSettings.mutate(
			{
				settingsToSave: [{ settingName: "article_language_preference", settingValue: newLanguage }],
			},
			{
				onSuccess: () => {
					successAlertRef.current?.show("Article Language Changes Saved!");
					setTimeout(() => successAlertRef.current?.close(), 3000);
				},
				onError: () => {
					failAlertRef.current?.show("Oops! Something went wrong. Please try again later.");
				},
			}
		);
	}

	// ================== Generate table data and render AbunTable component ==================
	const columnHelper = createColumnHelper<ArticleTitle>();

	const columnDefs: ColumnDef<any, any>[] = [
		columnHelper.accessor((row: ArticleTitle) => row.articleTitle, {
			id: 'articleTitle',
			header: "Article Title",
			cell: props => {
				if (isListOfTitlesRoute) { // on condition true on hover show edit btn
					// Editable titles logic for the `listoftitles` route
					const index = props.row.index;
					const row = editableTitles[index];
					return row?.isEditing ? (
						<input
							type="text"
							value={row.title}
							onChange={(e) => handleTitleChange(index, e.target.value)}
							onBlur={() => handleSave(index)}
							onKeyDown={(e) => {
								if (e.key === "Enter") handleSave(index);
							}}
							autoFocus
							style={{
								width: `${row.title.length - 5}ch`,
							}}
						/>
					) : (
						<div>
							<span style={{ color: '#000' }}>{row?.title}{" "}</span>
							<button
								onClick={() => toggleEdit(index)}
								className="seo-titles-edit-button"
							>
								✏️
							</button>
						</div>
					);
				} else {
					if (props.row.original.isGenerated) {
						// Default behavior for other routes	
						return (

							<CustomContextMenu
								url={`/articles/edit/${props.row.original.articleUID}`}
								CtrlOrMetaClick={() => {
									openUrlInNewTab(`/articles/edit/${props.row.original.articleUID}`);
								}}
								normalClick={() => {
									navigate(`/articles/edit/${props.row.original.articleUID}`);
								}}
							>
								<span style={{ color: '#000' }}>{props.row.original.articleTitle}</span>
							</CustomContextMenu>


						);
					}
					else {
						return (
							<span style={{ color: '#000' }}>{props.row.original.articleTitle}</span>
						);
					}


				}
			},
			enableGlobalFilter: true,
			enableSorting: false,
		}),
		columnHelper.accessor((row: ArticleTitle) => row.wordCount, {
			id: 'wordCount',
			header: "Words",
			cell: (info) => {
				const value = info.getValue();

				if (value === null || value === 0) {
					return "---";
				}

				return (
					<span style={{ color: '#000' }}>
						{value}
					</span>
				);
			},
			enableGlobalFilter: false,
			meta: {
				align: 'center'
			}
		}),
		columnHelper.display({
			id: 'generate_articles',
			header: () => "Action",
			cell: cellProps => {
				let processing: boolean = cellProps.row.original.isProcessing;
				let generated: boolean = cellProps.row.original.isGenerated;
				let failed: boolean = cellProps.row.original.isFailed;
				if (generated) {
					return (
						<CustomContextMenu
							url={`/articles/edit/${cellProps.row.original.articleUID}`}
							CtrlOrMetaClick={() => {
								openUrlInNewTab(`/articles/edit/${cellProps.row.original.articleUID}`);
							}}
							normalClick={() => {
								navigate(`/articles/edit/${cellProps.row.original.articleUID}`);
							}}>
							<button
								style={{ width: "100px" }}
								className="button is-success is-outlined is-small more-rounded-borders"
							>
								View
							</button>
						</CustomContextMenu>
					)
				} else if (processing) {
					const progressMessage = ArticleGenProgressMessageMap[cellProps.row.original.articleUID];
					if (!progressMessage) {
						return (
							<LinkButton
								linkTo={`/articles/edit/${cellProps.row.original.articleUID}/`}
								text={"Generating..."}
								type={"primary"}
								width={"100px"}
								outlined={true}
								additionalClassList={["is-small", "more-rounded-borders"]}
							/>
						);
					}

					return (
						<AbunButton
							className={"is-outlined is-small comp-research-table-button"}
							type={
								ArticleGenProgressMap[cellProps.row.original.articleUID] < 100
									? "primary"
									: "success"
							}
							clickHandler={() => {
								if (!ArticleGenProgressMessageMap[cellProps.row.original.articleUID]) {
									generateArticleHandler(cellProps.row.original.articleUID, articleContext);
								}
							}}
							disabled={generateArticle.isLoading || !userVerified}
							progress={ArticleGenProgressMap[cellProps.row.original.articleUID] || 0} // Using progress from your map
							progressColor="#007BFF" // Define your progress color
							width={"100px"}
						>
							{progressMessage}
						</AbunButton>
					);
				} else if (failed) {
					return <GenericButton text={"Failed. Retry"}
						type={"danger"}
						width={"100px"}
						disable={generateArticle.isLoading}
						additionalClassList={["is-small", "more-rounded-borders"]}
						clickHandler={() => {
							if (!ArticleGenProgressMessageMap[cellProps.row.original.articleUID]) {
								generateArticleHandler(cellProps.row.original.articleUID, articleContext);
							}
						}} />
				} else {
					return <div data-tooltip-id="generate-article" data-tooltip-content="Verify email to create article">
						<AbunButton
							className={"is-outlined is-small comp-research-table-button"}
							type={
								ArticleGenProgressMap[cellProps.row.original.articleUID] < 100
									? "primary"
									: "primary"
							}
							clickHandler={() => {
								if (!ArticleGenProgressMessageMap[cellProps.row.original.articleUID]) {
									generateArticleHandler(cellProps.row.original.articleUID, articleContext);
								}
							}}
							disabled={generateArticle.isLoading || (!userVerified && !basePageData.user_has_ltd_plans)}
							progress={ArticleGenProgressMap[cellProps.row.original.articleUID] || 0} // Using progress from your map
							progressColor="#007BFF" // Define your progress color
							width={"100px"}
						>
							{ArticleGenProgressMessageMap[cellProps.row.original.articleUID] || "Create Article"}
						</AbunButton>
						{(!userVerified && !basePageData.user_has_ltd_plans) && <Tooltip id="generate-article" />}
					</div>
				}
			},
			enableGlobalFilter: false,
			meta: {
				align: 'center',
			}
		}),
		columnHelper.display({
			header: "Publish",
			id: 'post_article',
			cell: cellProps => {
				let posted: boolean = cellProps.row.original.isPosted;
				let generated: boolean = cellProps.row.original.isGenerated;
				let postedTo: string = cellProps.row.original.postedTo;
				let archived: boolean = cellProps.row.original.isArchived;
				let articleUID: string = cellProps.row.original.articleUID;
				if (posted) {
					return <>
						<a className={"view-on-blog-link"} href={cellProps.row.original.postLink} data-tooltip-id="my-tooltip" target="_blank" rel="noreferrer" data-tooltip-content="View on Blog" >
							<img className={"image"} src={postedTo === "wordpress" ? wordpressIconSuccess : webflowIconSuccess} alt={postedTo === "wordpress" ? "wordpress-icon" : "webflow-icon"} />
						</a>
						<Tooltip id="my-tooltip" />
					</>
				} else if (generated) {
					return <>
						<div className="publish-container is-flex is-align-items-center is-justify-content-center">
							<button className={"publish-to-blog-btn is-flex is-align-items-center is-justify-content-center"} data-tooltip-id="publish-to-blog" data-tooltip-content={integrationWithUniqueID.length > 0 ? "Publish to Blog" : "Connect & Publish Now"}
								onClick={() => {
									if (integrationWithUniqueID.length > 0) {
										postToBlogHandler(articleUID);
									} else {
										setIntegrationModalActive(true);
									}
								}}>
								<img className={"image"} src={integrationWithUniqueID.length === 0 ? articleIntegrationIcon : integrationLogoPrimary[getIntegrationName(selectedIntegration)]} alt={selectedIntegration === "webflow" ? "webflow-icon" : "wordpress-icon"} />
							</button>
							{integrationWithUniqueID.length > 1 &&
								<div className="dropdown">
									<button className={`dropdown-icon ${openDropdown === articleUID ? "rotate" : ""}`} onClick={() => setOpenDropdown(openDropdown === articleUID ? "" : articleUID)}>&#9662;</button>
									<div className={`dropdown-content ${openDropdown === articleUID ? "show" : ""}`}>
										<h6 className={"dropdown-header"}>
											Choose Integration
										</h6>
										{
											integrationWithUniqueID.map((integration, index) => (
												<div key={index} className={"dropdown-item"} onClick={() => { setIntegrationAndHideDropDownContent(integration.integrationName); setSelectedIntegrationUniqueID(integration.integrationUniqueID) }}>
													<input type="radio" name="integration" id={integration.integrationName} checked={selectedIntegration === integration.integrationName} onChange={() => { }} />
													{
														<p>{truncateSiteDomain(integration.integrationName.charAt(0).toUpperCase() + integration.integrationName.slice(1))}</p>
													}
												</div>
											))
										}
									</div>
								</div>
							}
						</div>
						<Tooltip id="publish-to-blog" />
					</>
				} else if (archived) {
					return <GenericButton text={"Unarchive"}
						type={"success"}
						disable={archiveSelectedArticles.isLoading}
						additionalClassList={["is-small", "more-rounded-borders"]}
						clickHandler={() => {
							archiveSelectedArticles.mutate({ articlesUID: [cellProps.row.original.articleUID], archiveType: "unarchive" }, {
								onSuccess: () => {
									successAlertRef.current?.show("Article was unarchived successfully!");
									revalidate();
								},
								onError: () => {
									failAlertRef.current?.show("Failed to unarchive article. Please try again after some time.");
								}
							});
						}} />
				} else {
					return <></>
				}
			},
			enableGlobalFilter: false,
			meta: {
				align: 'center'
			}
		})
	]

	if (isListOfTitlesRoute) { // on condition true show bulk action
		if (bulkActionsEnabled) {
			// Add checkbox if bulk actions are enabled
			columnDefs.splice(0, 0, columnHelper.accessor((row: ArticleTitle) => row.articleTitle, {
				id: 'checkbox',
				header: ({ table }) => (
					<IndeterminateCheckbox
						{...{
							checked: table.getIsAllRowsSelected(),
							indeterminate: table.getIsSomeRowsSelected(),
							onChange: table.getToggleAllRowsSelectedHandler(),
						}}
					/>
				),
				cell: ({ row }) => (
					<IndeterminateCheckbox
						{...{
							checked: row.getIsSelected(),
							disabled: !row.getCanSelect(),
							indeterminate: row.getIsSomeSelected(),
							onChange: row.getToggleSelectedHandler(),
						}}
						name={"articleSelection"}
						value={row.original.articleUID}
					/>
				),
				enableGlobalFilter: true,
				enableSorting: false,
			}));
		} else {
			// Remove the checkbox if bulk actions are disabled
			columnDefs.splice(0, 0);
		}
	}

	function selectedRowsSetter(rowModel: RowModel<RowData>) {
		setSelectedRows(rowModel);
	}

	return (
		<div className={"keyword-project-container is-flex w-100 is-align-items-center is-flex-direction-column"}>
			<div className={"keyword-project-header"}>
				<svg className={"back-btn"} onClick={handleBackClick} stroke="#bcbcbc" fill="#bcbcbc" width="28" height="24" viewBox="0 0 28 24">
					<path d="M27.5 12H2M2 12L13 1M2 12L13 23" stroke="black" strokeOpacity="0.5" strokeWidth="2" />
				</svg>
			</div>
			{/* ------------------------------ INTEGRATION MODAL ------------------------------ */}
			<AbunModal active={integrationModalActive}
				headerText={""}
				closeable={true}
				hideModal={() => {
					setIntegrationModalActive(false)
				}}>
				<div className={"has-text-centered"}>
					<h1 className={"is-size-3"}>Publish articles to your blog page!</h1>
					<p className={"mt-4"}>
						Start publishing articles to your blog page by setting up an Integration for your website.
						You can find it under <b>Settings</b> {">"} <b>Integration & Scheduling</b>
					</p>
					<LinkButton text={"Go to Settings"}
						linkTo={pageURL['settings']}
						type={"primary"}
						additionalClassList={["mt-5"]} />
				</div>
			</AbunModal>

			<div className={"is-flex is-flex-direction-column is-align-items-center is-justify-content-center"} style={titlesGenerated ? { height: "auto", width: "100%" } : { height: "70vh", width: "100%" }}>
				<div className="keyword-title-table-header">
					<h1>Choose Title to create Article for</h1>
					{isListOfTitlesRoute ? (<><h2>Pattern:</h2><span> {pattern}</span></>) : (<><h2>Keyword:</h2><span> {pageData.keyword}</span></>)}
					<p>
						{isListOfTitlesRoute ? `` : `Total Keyword Traffic: ${pageData.keywordTraffic}`}
						{!isListOfTitlesRoute && (<img
							loading="lazy"
							width="20"
							style={pageData.locationIsoCode === "zz" ? { position: "absolute", marginLeft: "3px" } : { marginLeft: "5px" }}
							srcSet={pageData.locationIsoCode !== "zz" ? `https://flagcdn.com/32x24/${pageData.locationIsoCode.toLowerCase()}.png 2x` : "https://img.icons8.com/?size=100&id=3685"}
							src={pageData.locationIsoCode !== "zz" ? `https://flagcdn.com/16x12/${pageData.locationIsoCode.toLowerCase()}.png` : "https://img.icons8.com/?size=100&id=3685"}
							alt={pageData.locationIsoCode}
						/>)}
					</p>
				</div>

				{/* Below TSX component conditionally renders UI elements based on the state of article title generation.
				1. If titles have not been generated, it shows buttons to either generate article titles or write a custom title.
					- The "Generate Article Titles" button triggers an asynchronous operation to generate titles based on a keyword hash.
					- The "Write Custom Title" button opens a modal for custom title input.
				2. If loading or no table data is present, it displays a loading indicator.
				3. On error, it shows an error message.
				4. If titles are generated and data is available, it displays a table (`AbunTable`) of article titles with sorting and pagination options, and a button to write a custom title. 
				*/}
				{
					totalRow == "0" && !isPseoTitleRedirect ?
						localStorage.getItem(`titleGenTask-${pageData.keywordHash}`) ?
							<div className={"is-flex is-justify-content-center mt-4"}>
								<h2 style={{ marginTop: "40%" }} className="is-size-3">Generating Article Titles... <Icon additionalClasses={["ml-2 mb-2"]} iconName="spinner" /> </h2>
							</div> :
							// Display buttons to generate article titles or write custom title
							<div className={"is-flex is-justify-content-center mt-4"}>
								<GenericButton text={"Generate Article Titles"}
									type={"primary"}
									disable={titlesForKeywordMutation.isLoading}
									additionalClassList={["is-small"]}
									clickHandler={() => {
										setModalText("Generating article titles. Please wait...");
										setRequestModalActive(true);
										titlesForKeywordMutation.mutate({
											keyword_hash: pageData.keywordHash,
											location: pageData.locationIsoCode.toLowerCase()
										}, {
											onSuccess: () => {
												setRequestModalActive(false);
												setTitlesGenerated(true);
												revalidate();
												successAlertRef.current?.show("New Titles have been generated successfully!");
												setTimeout(() => {
													successAlertRef.current?.close();
												}, 5000);
											},
											onError: ((error: Error) => {
												setRequestModalActive(false);
												if (error.message) {
													failAlertRef.current?.show(
														`Failed to generate new titles. ${error.message}`
													);
												} else {
													failAlertRef.current?.show(
														`Failed to generate new titles. Please try again later.`
													);
												}
												setTimeout(() => {
													failAlertRef.current?.close();
												}, 5000);
											})
										});
									}}
								/>
								<span className="mr-5 ml-5">or</span>
								<GenericButton text={"Write Custom Title"}
									type={"primary"}
									disable={createCustomTitleForKeyword.isLoading}
									clickHandler={() => setShowCreateCustomTitleModal(true)}
									additionalClassList={["is-small"]} />
							</div>
						// : error ? // Display error message if an error occurs
						// <div>
						// 	<p>Error! Failed to load data. Please try again later.</p>
						// </div>
						// : (isLoading || tableData.length === 0) ? // Display loading indicator if data is loading or no data is present
						// <div className={"loadingDataCard mt-4"} style={{ width: "100%" }}>
						// 	<div className={"card-content"}>
						// 		<div className={"content is-flex is-justify-content-center"}>
						// 			<p style={{ textAlign: "center", fontSize: "1.3rem" }}>
						// 				Loading Data...<Icon iconName={"spinner"} marginClass={"ml-5"} />
						// 			</p>
						// 		</div>
						// 	</div>
						// </div>
						: // Display the table of article titles if data is available

						(
							<>
								{isListOfTitlesRoute && ( // loading for list of title
									<AbunModal
										active={requestModalActive || createCustomTitleForKeyword.isLoading}
										headerText={""}
										closeable={false}
										hideModal={() => setRequestModalActive(false)}
									>
										<div className="loadingData w-100 is-flex is-justify-content-center is-align-items-center">
											<AbunLoader show={requestModalActive || createCustomTitleForKeyword.isLoading} height="20vh" />
										</div>
										<p className="is-size-4 has-text-centered mb-4">{modalText}</p>
									</AbunModal>
								)}
								<div className="language-article-context">
									<div style={{ marginTop: '1.7rem' }} className={"large-screen-gear-icon"}>
										<Link to="/settings">
											<FontAwesomeIcon icon={faGear} style={{ cursor: 'pointer', fontSize: '1.6rem', color: 'grey' }}
												data-tooltip-id="article-setting-tooltip"
												data-tooltip-content="Article Settings" />
										</Link>
										<Tooltip id="article-setting-tooltip" />
									</div>
									<div className={"is-flex is-flex-direction-column is-align-items-center language-selection"}>
										<span>Select Article Language</span>
										<Select
											className="w-100 language-select"
											options={allSupportedLanguagesOptions}
											value={allSupportedLanguagesOptions.find(option => option.value === languagePreference) || null}
											onChange={handleLanguageChange}
											formatOptionLabel={(e, { context }) =>
												context === "value" ? ` ${e.label}` : e.label
											}
											styles={{
												control: (provided) => ({
													...provided,
													width: '100%',
												}),
											}}
										/>
									</div>
									<div className={"article-context-selection is-align-items-center is-flex is-flex-direction-row"}>
										<div className={"is-flex is-flex-direction-column is-align-items-center"}>
											<span>Add Context & Instructions</span>
											<Select
												className="select-context"
												options={contextOptions}
												value={options.find(option => option.value.trim() === articleContext?.trim()) || null}
												styles={{
													...customStyles,
													control: (provided) => ({
														...provided,
														width: '240px',
													}),
												}}
												onChange={handleContextChange}
												components={{
													Option: CustomOption,
													MenuList: (props) => <CustomMenuList {...props} setShowAddContextModal={setShowAddContextModal} />
												}}
											/>
										</div>
										<div style={{ marginTop: '1.7rem', marginLeft: '10px' }} className={"smaill-screen-gear-icon"}>
											<Link to="/settings">
												<FontAwesomeIcon icon={faGear} style={{ cursor: 'pointer', fontSize: '1.6rem', color: 'grey' }}
													data-tooltip-id="article-setting-tooltip"
													data-tooltip-content="Article Settings" />
											</Link>
											<Tooltip id="article-setting-tooltip" />
										</div>
									</div>
								</div>
								<AbunTable
									ref={tableRef}
									serverSide={true}
									apiUrl="/api/frontend/keyword-project/keyword-titles"
									queryParams={{
										keyword_project_id: params.keywordProjectId,
										keyword_hash: params.keywordHash,
									}}
									tableContentName={isListOfTitlesRoute ? "Seo Titles" : "Article Titles"}  // condition table render for list of title for seo and article title. 
									id="article-titles-table"
									columnDefs={columnDefs}
									pageSizes={pageSizes}
									initialPageSize={isListOfTitlesRoute ? pageSizes[5] : pageSizes[2]}
									enableSorting={true}
									defaultSortingState={initialSortingState}
									noDataText={
										isListOfTitlesRoute
											? "No SEO titles found. Generate more titles to manage SEO."
											: "No articles found. Generate more titles to create articles."
									}
									rowCheckbox={isListOfTitlesRoute ? true : undefined}
									selectedRowsSetter={isListOfTitlesRoute ? selectedRowsSetter : undefined}
									bulkActions={isListOfTitlesRoute && selectedRows?.rows.length ? availableBulkActions : []}
									bulkActionsEnabled={(isListOfTitlesRoute && userVerified) ? bulkActionsEnabled : false}
									isListOfTitlesRoute={isListOfTitlesRoute}
									showLoaderPseoRedirect={isListOfTitlesRoute ? isPseoTitleRedirect : undefined}
									buttonStyle={{ display: "flex" }}
									transformResponse={(rawData: any) => {

										// Filter titles based on keyword or keywordHash
										let filteredTitles = (rawData.titles_data || []).filter((article: ArticleTitle) => {
											return article.keyword === rawData.keyword || article.keywordHash === rawData.keywordHash;
										});

										setTotalRow(rawData.total)

										// Remove archived articles
										filteredTitles = filteredTitles.filter((article: ArticleTitle) => !article.isArchived);

										// Sort: isGenerated first, then isUserAdded, then keywordTraffic descending
										filteredTitles.sort((a: ArticleTitle, b: ArticleTitle) => {
											// isGenerated = true first
											if (a.isGenerated !== b.isGenerated) {
												return a.isGenerated ? -1 : 1;
											}
											if (a.isProcessing !== b.isProcessing) {
												return a.isProcessing ? -1 : 1;
											}
											// Then isUserAdded = true
											if (a.isUserAdded !== b.isUserAdded) {
												return a.isUserAdded ? -1 : 1;
											}
											// Then keywordTraffic descending
											const aTraffic = a.keywordTraffic ?? -1;
											const bTraffic = b.keywordTraffic ?? -1;
											return bTraffic - aTraffic;
										});

										return {
											data: filteredTitles,
											total: rawData.total,
											all_integrations: rawData.all_integrations,
											google_search_console_integrated: rawData.google_search_console_integrated,
											user_verified: rawData.user_verified,
											keyword: rawData.keyword,
											keywordHash: rawData.keywordHash,
											keywordTraffic: rawData.keywordTraffic,
											difficultyScore: rawData.difficultyScore,
											locationIsoCode: rawData.locationIsoCode,
											keyword_project_id: rawData.keyword_project_id,
											pattern: rawData.pattern,
										};
									}}
									onDataTransformed={isListOfTitlesRoute ? handleDataTransformed : undefined}
									applyAction={
										isListOfTitlesRoute
											? (action) => {
												if (action === "Generate Article") {
													const articleUIDs: Array<string> =
														selectedRows?.rows
															.map((row) => row.original as ArticleTitle)
															.filter((article) => !article.isGenerated)
															.map((article) => article.articleUID)
														|| [];
													articleUIDs.forEach((articleUID) => {
														localStorage.setItem(`articleGen-${articleUID}`, articleUID);

														setArticleGenProgressMap((prev) => {
															const updatedMap = { ...prev, [articleUID]: 1 };
															localStorage.setItem("articleGenProgressMap", JSON.stringify(updatedMap));
															return updatedMap;
														});

														setArticleGenProgressMessageMap((prev) => {
															const updatedMap = { ...prev, [articleUID]: "In Queue..." };
															localStorage.setItem("articleGenProgressMessageMap", JSON.stringify(updatedMap));
															return updatedMap;
														});
													});
													setSelectedRows(undefined);
													generateSelectedArticles.mutate(articleUIDs, {
														onSuccess: (data) => {
															const responseData = data?.data;

															if (responseData?.status === "sent_for_processing") {
																setArticleContext("");

																// Loop through all articleUIDs
																articleUIDs.forEach((articleUID) => {
																	// Track each article
																	localStorage.setItem(`articleGen-${articleUID}`, articleUID);

																	setArticleGenProgressMap((prev) => {
																		const updatedMap = { ...prev, [articleUID]: 1 };
																		localStorage.setItem("articleGenProgressMap", JSON.stringify(updatedMap));
																		return updatedMap;
																	});

																	setArticleGenProgressMessageMap((prev) => {
																		const updatedMap = { ...prev, [articleUID]: "In Queue..." };
																		localStorage.setItem("articleGenProgressMessageMap", JSON.stringify(updatedMap));
																		return updatedMap;
																	});

																	// Start polling for each
																	pollArticleProgress(articleUID);
																});

															} else if (responseData?.status === "rejected") {
																articleUIDs.forEach((articleUID) => cleanupArticleProgress(articleUID));
																const reason = responseData.reason;
																if (reason === "max_limit_reached") {
																	failAlertRef.current?.show(
																		"Article generation request failed. You have reached your max article generation limit for the month."
																	);
																} else {
																	failAlertRef.current?.show(`Article generation request failed. Error ID: ${reason}`);
																}
															} else {
																failAlertRef.current?.show(
																	`Article generation request returned unknown status "${responseData?.status}". Please contact us if there's any issue.`
																);
															}

															setInitialSortingState(initialSortingState);
															revalidate()
															successAlertRef.current?.show("Articles are being generated. Please wait for a moment.");
														},
														onError: () => {
															articleUIDs.forEach((articleUID) => cleanupArticleProgress(articleUID));
															failAlertRef.current?.show("Failed to generate articles. Please try again after some time.")
														}
													});
												}
												else if (action === "Publish") {
													setModalText("Processing request. Please wait...");
													setRequestModalActive(true);
													const articleUIDs: Array<string> =
														selectedRows?.rows
															.map((row) => row.original as ArticleTitle)
															.filter((article) => article.isGenerated && !article.isPosted)
															.map((article) => article.articleUID)
														|| [];
													postSelectedArticles.mutate({
														articlesUID: articleUIDs,
														selectedIntegration: selectedIntegration,
														selectedIntegrationUniqueId: selectedIntegrationUniqueID
													}, {
														onSuccess: handleBulkActionSuccess("Articles was posted to your site successfully!"),
														onError: handleBulkActionError("Failed to post articles. Please try again after some time.")
													})
												}
											}
											: undefined
									}
									buttons={[
										{
											text: isListOfTitlesRoute ? (
												"Add Custom Title"
											) : (
												<>
													<svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ marginRight: '0.2rem', verticalAlign: 'middle', marginBottom: '0.1rem', height: 'auto', width: '16px', stroke: '#fff' }}>
														<path d="M15.75 9.5C15.75 9.64918 15.6907 9.79226 15.5852 9.89775C15.4798 10.0032 15.3367 10.0625 15.1875 10.0625H9.5625V15.6875C9.5625 15.8367 9.50324 15.9798 9.39775 16.0852C9.29226 16.1907 9.14918 16.25 9 16.25C8.85082 16.25 8.70774 16.1907 8.60225 16.0852C8.49676 15.9798 8.4375 15.8367 8.4375 15.6875V10.0625H2.8125C2.66332 10.0625 2.52024 10.0032 2.41475 9.89775C2.30926 9.79226 2.25 9.64918 2.25 9.5C2.25 9.35082 2.30926 9.20774 2.41475 9.10225C2.52024 8.99676 2.66332 8.9375 2.8125 8.9375H8.4375V3.3125C8.4375 3.16332 8.49676 3.02024 8.60225 2.91475C8.70774 2.80926 8.85082 2.75 9 2.75C9.14918 2.75 9.29226 2.80926 9.39775 2.91475C9.50324 3.02024 9.5625 3.16332 9.5625 3.3125V8.9375H15.1875C15.3367 8.9375 15.4798 8.99676 15.5852 9.10225C15.6907 9.20774 15.75 9.35082 15.75 9.5Z" fill="white"></path>
													</svg>
													<span style={{ paddingRight: '12px' }}> Write Custom Title</span>
												</>
											),
											type: "primary",
											isDisabled: false,
											clickHandler: () => setShowCreateCustomTitleModal(true),
											extraClassName: "is-small is-justify-content-space-between",
										},
									]}
								/>
							</>
						)
				}
			</div>

			{/* ------------------------------ CREATE CUSTOM TITLE MODAL ------------------------------ */}
			<AbunModal active={showCreateCustomTitleModal}
				headerText={"Write a custom title keyword: " + pageData.keyword}
				closeable={true}
				closeableKey={true}
				hideModal={() => {
					setShowCreateCustomTitleModal(false)
				}}>
				<div className={"has-text-centered"}>
					<input type="text" className={"input "} placeholder={"Enter your custom title here..."} onChange={(e) => {
						setCustomTitle(e.target.value);
					}} />
					<AbunButton type={"success"}
						className={"mt-4"}
						disabled={createCustomTitleForKeyword.isLoading}
						clickHandler={() => {
							setShowCreateCustomTitleModal(false);
							createCustomTitleForKeyword.mutate({
								keyword: pageData.keyword,
								keywordHash: pageData.keywordHash,
								customArticleTitle: customTitle
							}, {
								onSuccess: () => {
									setTitlesGenerated(true);
									tableRef.current?.refetchData();
									successAlertRef.current?.show("Custom title added successfully!");
									setTimeout(() => {
										successAlertRef.current?.close();
									}, 5000);
								},
								onError: () => {
									failAlertRef.current?.show("Failed to add custom title. Please try again after some time.");
									setTimeout(() => {
										failAlertRef.current?.close();
									}, 5000);
								}
							});
						}}>
						Proceed
					</AbunButton>
				</div>
			</AbunModal>

			{/* ------------------------------ CREATE CONTEXT MODAL ------------------------------ */}
			{/* <AbunModal active={showAddContextModal}
				headerText={"Write a context for article"}
				closeable={true}
				closeableKey={true}
				hideModal={() => {
					setShowAddContextModal(false)
				}}>
				<div className={"has-text-centered create-context-modal"}>
					<textarea
						rows={10}
						className="context-input"
						value={articleContext}
						placeholder="Include MyCompany.com at the top of the list. Also talk more about how MyCompany.com helps with This, This & That"
						onChange={(e) => setArticleContext(e.target.value)}
						required
					/>
					<AbunButton type={"success"}
						className={"mt-4"}
						disabled={false}
						clickHandler={() => {
							setShowAddContextModal(false);
						}}>
						Proceed
					</AbunButton>
				</div>
			</AbunModal> */}
			{/* ------------------------------ CREATE CONTEXT MODAL ------------------------------*/}
			<div className={"context-instruction-modal"}>
				<AbunModal active={showAddContextModal}
					headerText={"Add instruction & context for better articles."}
					closeable={true}
					closeableKey={true}
					hideModal={() => {
						setShowAddContextModal(false)
					}}>
					<div className={"has-text-centered create-context-modal"}>
						<textarea
							rows={10}
							className="context-input"
							value={saveArticleContext}
							placeholder="Include MyCompany.com at the top of the list. Also talk more about how MyCompany.com helps with This, This & That"
							onChange={(e) => setSaveArticleContext(e.target.value)}
							required
						/>
						<AbunButton type={"success"}
							className={"mt-4"}
							disabled={saveArticleContext !== "" ? false : true}
							clickHandler={() => {
								saveContext(saveArticleContext, oldContext)
							}}>
							Proceed
						</AbunButton>
					</div>
				</AbunModal>
			</div>

			<SuccessAlert ref={successAlertRef} />
			<ErrorAlert ref={failAlertRef} />
		</div>
	)
}
