import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { gscfetchConnectedDomains } from '../../../utils/api';
import AbunModal from '../../../components/AbunModal/AbunModal';
import AbunLoader from '../../../components/AbunLoader/AbunLoader';
import AbunButton from '../../../components/AbunButton/AbunButton';
import GenericButton from '../../../components/GenericButton/GenericButton';
import { MutableRefObject } from 'react';

export interface GSCDomainSelectionProps {
  failAlertRef: MutableRefObject<any>;
  setSelectedDomain: (domain: string) => void;
  handleBackBtnClick: () => void;
}

export default function LoadGSCDomainsList(props: GSCDomainSelectionProps) {
  // ----------------------- QUERIES -----------------------
  const getDomainListData = useQuery(gscfetchConnectedDomains());

  // ----------------------- STATES -----------------------
  const [mainModalActive, setMainModalActive] = useState(true);

  if (getDomainListData.isLoading) {
    return (
      <AbunModal active={true}
        headerText={"Loading Google Search Console Data"}
        closeable={false}
        hideModal={() => { }}>
        <div className={"loadingData w-100 is-flex is-justify-content-center is-align-items-center"}>
          <AbunLoader show={getDomainListData.isLoading} height="20vh" />
        </div>
      </AbunModal>
    );
  } else if (getDomainListData.isError) {
    let error = JSON.parse((getDomainListData.error as Error).message) || null;
    if (!error) error = { message: "Error fetching data from Google Search Console!" };
    return (
      <AbunModal active={mainModalActive}
        headerText={"Error fetching data"}
        closeable={true}
        closeableKey={true}
        hideModal={() => {
          setMainModalActive(false);
          props.handleBackBtnClick();
        }}>
        <div className="error-div has-text-centered">
          <p className="my-2">{error.message}</p>
          <GenericButton text={"Retry"}
            type={"success"}
            clickHandler={() => getDomainListData.refetch()} />
        </div>
      </AbunModal>
    );
  } else {
    const sites = (getDomainListData.data as any[]).map((site) => {
      return {
        domain: site.siteUrl.split(":")[1],
        permissionLevel: site.permissionLevel === "siteUnverifiedUser" ? "Unverified" : "Verified"
      };
    });
    if ((getDomainListData.data as any[]).length === 0) {
      return (
        <AbunModal active={mainModalActive}
          headerText={"No Domains Found"}
          closeable={true}
          closeableKey={true}
          hideModal={() => {
            setMainModalActive(false);
            props.handleBackBtnClick();
          }}>
          <div className="error-div has-text-centered">
            <p className="my-2">No domains found in Google Search Console!</p>
          </div>
        </AbunModal>
      );
    } else {
      return (
        <AbunModal active={mainModalActive}
          headerText={"Select Domain to Import Keywords from"}
          closeable={true}
          closeableKey={true}
          hideModal={() => {
            setMainModalActive(false);
            props.handleBackBtnClick();
          }}>
          <div className={"gsc-domains-list"}>
            {sites.map((domain, index) => {
              return (
                <div className={"gsc-domain-item"} key={index}>
                  <div className={"gsc-domain-item-content"}>
                    <h1>{domain.domain.replaceAll("https://", "").replaceAll("http://", "").replaceAll("/", "")}</h1>
                    <p className={"gsc-domain-item-verified"}>{domain.permissionLevel}</p>
                  </div>
                  <AbunButton className={"gsc-domain-item-button"} type={"primary"}
                    clickHandler={() => {
                      props.setSelectedDomain(domain.domain.replaceAll("https://", "").replaceAll("http://", "").replaceAll("/", ""));
                      setMainModalActive(false);
                    }}>
                    Import Keywords
                  </AbunButton>
                </div>
              );
            })}
          </div>
        </AbunModal>
      );
    }
  }
}
