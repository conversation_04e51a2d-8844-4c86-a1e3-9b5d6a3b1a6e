import React, { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { googleIntegrationMutation } from '../../../utils/api';
import AbunButton from '../../../components/AbunButton/AbunButton';
import googleSearchConsoleLogo from '../../../assets/images/google-search-console.webp';
import { MutableRefObject } from 'react';

interface GSCIntegrationProps {
  failAlertRef: MutableRefObject<any>;
}

interface GoogleIntegrationResponse {
  success: boolean;
  authorization_endpoint: string;
}

export default function LoadGSCIntegration(props: GSCIntegrationProps) {
  const [GSCIntegrationProcessing, setGSCIntegrationProcessing] = useState(false);
  const integrationMutation = useMutation(googleIntegrationMutation);

  /**
   * Starts process to connect google search console, google analytics or google drive to user website.
   */
  function googleIntegration() {
    const integrationType = "google-search-console";
    props.failAlertRef.current?.close();
    setGSCIntegrationProcessing(true);
    integrationMutation.mutate(integrationType, {
      onSuccess: (data) => {
        const response: GoogleIntegrationResponse = data['data'];
        if (response.success) {
          localStorage.setItem('integration-type', integrationType);
          window.location.href = response.authorization_endpoint;
        } else {
          setGSCIntegrationProcessing(false);
          props.failAlertRef.current?.show(
            "Oops! Something went wrong :( Please try " +
            "again later or contact us for further support."
          );
          setTimeout(() => {
            props.failAlertRef.current?.close();
          }, 7000);
        }
      },
      onError: () => {
        setGSCIntegrationProcessing(false);
        props.failAlertRef.current?.show(
          "Oops! Something went wrong :( Please try " +
          "again later or contact us for further support."
        );
        setTimeout(() => {
          props.failAlertRef.current?.close();
        }, 7000);
      }
    });
  }

  return (
    <div className={"GSC-integration"}>
      <div className={"gs-integration-item"}>
        <img src={googleSearchConsoleLogo} className={"integration-item-logo"} alt="Google search console logo" />
        <div className={"integration-item-content"}>
          <h1>Google Search Console</h1>
          <p className={"integration-item-not-installed"}>Not Connected</p>
        </div>
      </div>
      <AbunButton className={"integration-button"} type={"primary"}
        clickHandler={googleIntegration}>
        {GSCIntegrationProcessing ? "Connecting..." : "Connect GSC"}
      </AbunButton>
    </div>
  );
}
