import React, { lazy, Suspense } from 'react';
import AbunLoader from '../../../components/AbunLoader/AbunLoader';
import AbunModal from '../../../components/AbunModal/AbunModal';
import { MutableRefObject } from 'react';

// Lazy load the GSC components
const LoadGSCIntegrationLazy = lazy(() => import('./LoadGSCIntegration'));
const LoadGSCDomainsListLazy = lazy(() => import('./LoadGSCDomainsList'));

// Props interfaces
interface GSCIntegrationProps {
  failAlertRef: MutableRefObject<any>;
}

export interface GSCDomainSelectionProps {
  failAlertRef: MutableRefObject<any>;
  setSelectedDomain: (domain: string) => void;
  handleBackBtnClick: () => void;
}

// Wrapper component for LoadGSCIntegration with lazy loading
export function LazyLoadGSCIntegration(props: GSCIntegrationProps) {
  return (
    <Suspense fallback={
      <div className="loadingData w-100 is-flex is-justify-content-center is-align-items-center">
        <AbunLoader show={true} height="20vh" />
      </div>
    }>
      <LoadGSCIntegrationLazy {...props} />
    </Suspense>
  );
}

// Wrapper component for LoadGSCDomainsList with lazy loading
export function LazyLoadGSCDomainsList(props: GSCDomainSelectionProps) {
  return (
    <Suspense fallback={
      <AbunModal active={true}
        headerText={"Loading Google Search Console Data"}
        closeable={false}
        hideModal={() => { }}>
        <div className="loadingData w-100 is-flex is-justify-content-center is-align-items-center">
          <AbunLoader show={true} height="20vh" />
        </div>
      </AbunModal>
    }>
      <LoadGSCDomainsListLazy {...props} />
    </Suspense>
  );
}
