import React, { lazy, Suspense } from 'react';
import { CSVUploadProps } from './CSVUpload';
import AbunLoader from '../../../components/AbunLoader/AbunLoader';

// Lazy load the CSVUpload component
const CSVUploadComponent = lazy(() => import('./CSVUpload'));

export default function LazyCSVUpload(props: CSVUploadProps) {
  return (
    <Suspense fallback={
      <div className="ai-keyword-research-content">
        <div className="loadingData w-100 is-flex is-justify-content-center is-align-items-center">
          <AbunLoader show={true} height="20vh" />
        </div>
      </div>
    }>
      <CSVUploadComponent {...props} />
    </Suspense>
  );
}
