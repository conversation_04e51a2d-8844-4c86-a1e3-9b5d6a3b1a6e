import { useState, useEffect, useRef, ChangeEvent } from 'react';
import { useMutation } from '@tanstack/react-query';
import { uploadKeywordsMutationV2 } from '../../../utils/api';
import { Autocomplete, Box, TextField } from '@mui/material';
import GenericButton from '../../../components/GenericButton/GenericButton';
import EarthFlag from '../../../assets/images/earth-flag.webp';
import uploadIcon from '../../../assets/images/icons/cloud-upload.svg';
import countries from '../../../utils/constants/CountriesforSerp';
import Papa, { ParseResult } from 'papaparse';
import { MutableRefObject } from 'react';

export interface CountryType {
  location_code: number;
  location_name: string;
  country_iso_code: string;
}

export interface CSVUploadProps {
  addKeywordsDone: (total_count?: number, connected_count?: number, existing_count?: number, new_count?: number) => void;
  failAlertRef: MutableRefObject<any>;
  successAlertRef: MutableRefObject<any>;
  countryCode: string;
}

interface ColumnSelectionProps {
  columns: Array<string>;
  data: Array<any>;
  addKeywordsDone: (total_count?: number, connected_count?: number, existing_count?: number, new_count?: number) => void;
  fileName: string;
  selectedLocation: CountryType;
  failAlertRef: MutableRefObject<any>;
  successAlertRef: MutableRefObject<any>;
}

export default function CSVUpload(props: CSVUploadProps) {
  // ------------------- STATES -------------------
  const [fileName, setFileName] = useState("");
  const [columns, setColumns] = useState<Array<string>>([]);
  const [data, setData] = useState<Array<any>>([]);
  const [selectedLocation, setSelectedLocation] = useState<CountryType>({
    "location_code": 1,
    "location_name": "Global",
    "country_iso_code": "ZZ"
  });

  // ------------------- EFFECTS -------------------
  useEffect(() => {
    // Find the country that matches the `country_iso_code` with `pageData.country_code`
    if (props.countryCode !== "ZZ") {
      const matchedCountry = countries.find(
        (country) => country.country_iso_code === props.countryCode.toUpperCase()
      );
      // If a match is found, update the selected location
      if (matchedCountry) {
        setSelectedLocation(matchedCountry);
      }
    }
  }, [props.countryCode]);

  // ------------------- REFS -------------------
  const fileInput = useRef<HTMLInputElement>(null);

  function onFileSelected(e: ChangeEvent<HTMLInputElement>) {
    // set filename text
    let filename: string = (e.target.value.split(/([\\/])/g).pop() as string);
    setFileName(filename);

    // read file and update column names
    if (e.target.files) {
      let file = e.target.files[0]
      if (file) {
        Papa.parse((file as any), {
          complete(results: ParseResult<any>) {
            let columns = results.data[0];
            let csvData = results.data.splice(1, results.data.length);
            setColumns(columns);
            setData(csvData);
          }
        })
      }
    }
  }

  return (
    <div className="keywords-csv-upload">
      <div className={"location-input"}>
        <Autocomplete
          id="csv-location-select-autocomplete"
          sx={{ width: 225 }}
          options={countries}
          value={selectedLocation}
          autoHighlight
          getOptionLabel={(option) => option.country_iso_code !== "ZZ" ? `${option.location_name} (${option.country_iso_code})` : option.location_name}
          isOptionEqualToValue={(option, value) => option.location_code === value.location_code}
          renderOption={(props, option) => (
            <Box component="li" sx={{ '& > img': { mr: 2, flexShrink: 0 } }} {...props}>
              <img
                loading="lazy"
                width="20"
                srcSet={option.country_iso_code !== "ZZ" ? `https://flagcdn.com/w40/${option.country_iso_code.toLowerCase()}.png 2x` : EarthFlag}
                src={option.country_iso_code !== "ZZ" ? `https://flagcdn.com/w20/${option.country_iso_code.toLowerCase()}.png` : EarthFlag}
                alt=""
              />
              {option.location_name} ({option.country_iso_code})
            </Box>
          )}
          renderInput={(params) => (
            <TextField
              {...params}
              label="Location"
              inputProps={{
                ...params.inputProps,
                // disable autocomplete and autofill and suggestion
                autoComplete: 'off',
              }}
            />
          )}
          onChange={(_event, option) => {
            if (option) {
              setSelectedLocation(option);
            }
          }}
        />
      </div>
      <div className="csv-div"
        //  remove onclick to prevent file opener to open twice
        // onClick={openFileExplorer}
        onDragOver={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onDrop={(e) => {
          e.stopPropagation();
          e.preventDefault();

          if (e.dataTransfer.items) {
            const file = e.dataTransfer.items[0].getAsFile();
            if (file) {
              setFileName(file.name);
              // read using papaparse
              Papa.parse((file as any), {
                complete(results: ParseResult<any>) {
                  let columns = results.data[0];
                  let csvData = results.data.splice(1, results.data.length);
                  setColumns(columns);
                  setData(csvData);
                }
              });
            }
          }
        }}>
        <img src={uploadIcon} alt="upload-icon" />
        <p>Click to <b>choose a file</b> or <b>drag it here</b> with a column containing your Keywords</p>
        <p className={"csv-filename"}>
          {fileName ? fileName : "No file selected"}
        </p>
        <input type="file" accept={"text/csv"} ref={fileInput} onChange={onFileSelected} />
      </div>
      {columns.length > 0 ?
        <ColumnSelection
          columns={columns}
          data={data}
          addKeywordsDone={props.addKeywordsDone}
          fileName={fileName.length > 10 ? fileName.substring(0, 10) + "..." : fileName}
          selectedLocation={selectedLocation}
          failAlertRef={props.failAlertRef}
          successAlertRef={props.successAlertRef}
        /> : <></>}
    </div>
  )
}

function ColumnSelection(props: ColumnSelectionProps) {
  // ------------------- STATES -------------------
  const [columnIndex, setColumnIndex] = useState(0);

  // ------------------- EFFECTS -------------------
  const keywordsMutation = useMutation(uploadKeywordsMutationV2);

  return (
    <div className={"column-selection-div"}>
      <label className="label">Choose column with keyword data</label>
      <div className="select">
        <select value={columnIndex}
          onChange={e => setColumnIndex(parseInt(e.target.value))}>
          {props.columns.map((columnName, index) => (
            <option value={index} key={`${columnName}-${index}`}>{columnName}</option>
          ))}
        </select>
      </div>
      <GenericButton
        text={keywordsMutation.isLoading ? "Uploading Keywords. Please Wait..." : "Upload Keywords"}
        type={"success"}
        additionalClassList={["mt-6"]}
        disable={keywordsMutation.isLoading}
        clickHandler={() => {
          let keywords: Array<string> = props.data.map(row => row[columnIndex]);
          // ensure that keywords are unique and not empty, null or undefined
          keywords = keywords.filter((keyword, index) => keyword && keywords.indexOf(keyword) === index);
          keywordsMutation.mutate({
            keywords: keywords,
            selectedLocation: props.selectedLocation,
            projectName: props.fileName + " - CSV Keywords Upload",
            keywordsAddedUsing: "csv"
          }, {
            onSuccess: (data) => {
              let responseData = (data as any)["data"];
              if (responseData["status"] === "rejected") {
                if (responseData["reason"] === "max_limit_reached") {
                  props.failAlertRef.current?.show("Keywords generation request failed. " +
                    "You have reached your max Keywords generation limit for the month.");
                } else if (responseData["reason"] === "blocked_keyword_used") {
                  props.failAlertRef.current?.show(responseData["message"]);
                } else if (responseData["reason"] === "no_keywords_found") {
                  props.failAlertRef.current?.show("No keywords found. Please try with different keywords.");
                } else {
                  props.failAlertRef.current?.show(
                    `Keywords generation request failed. Error ID: ${responseData["reason"]}`
                  );
                }
                setTimeout(() => {
                  props.failAlertRef.current?.close();
                }, 7000);
              } else {
                props.addKeywordsDone(
                  data['data']['total_count'],
                  data['data']['connected_count'],
                  data['data']['existing_count'],
                  data['data']['new_count'],
                );
                props.successAlertRef.current?.show("Keywords uploaded successfully.");
                setTimeout(() => {
                  props.successAlertRef.current?.close();
                }, 5000);
              }
            }
          });
        }}
      />
    </div>
  )
}
