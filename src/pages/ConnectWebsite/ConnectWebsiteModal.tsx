import { useQuery } from "@tanstack/react-query";
import React, { MutableRefObject, useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { connectWebsiteQuery } from "../../utils/api";
import { BasePageData } from "../Base/Base";
import { pageURL } from "../routes";
import "./ConnectWebsite.min.css";
import ConnectWebsiteLoading from "./ConnectWebsiteLoading";
import WebsiteDomain from "./WebsiteDomain";

interface WebsiteData {
	domain: string
	protocol: string
	blog: string
	title: string
	description: string
	industry: string
	icp: string
	language: string
	competitors: Array<string>
	generate_content_plan: boolean
}

interface ConnectWebsiteProps {
	setShowConnectWebsiteModal: React.Dispatch<React.SetStateAction<boolean>>
	successAlertRef: MutableRefObject<any>
	failAlertRef: MutableRefObject<any>
	navigateOrReload: "navigate" | "reload"
	basePageData?: BasePageData
}

export function ConnectWebsiteModal(props: ConnectWebsiteProps) {
	// -------------------- STATES --------------------
	const [stage, setStage] = useState(1);  // start from first stage
	const [called, setCalled] = useState(false);
	const [domain, setDomain] = useState("");
	const [closePop, setClosePop] = useState(() => {
		if (props.basePageData?.website_list && props.basePageData.website_list.length >= 1) {
			const domain = props.basePageData.website_list[0]?.domain;
			return domain && !domain.startsWith("default-") && !domain.endsWith(".xyz");
		}
		return false;
	});

	// -------------------- CONSTANTS --------------------
	const navigate = useNavigate();
	const stages = [
		<p></p>,
		<WebsiteDomain getWebsiteData={getWebsiteData} updateWebsiteData={updateWebsiteData} nextStage={nextStage} getWebsiteTempData={getWebsiteTempData} updateWebsiteTempData={updateWebsiteTempData} />,  // domain & blog
		// <WebsiteTitle getWebsiteData={getWebsiteData} updateWebsiteData={updateWebsiteData} nextStage={nextStage} getWebsiteTempData={getWebsiteTempData} updateWebsiteTempData={updateWebsiteTempData} setStage={setStage} />, // title & description
		// <WebsiteICP getWebsiteData={getWebsiteData} updateWebsiteData={updateWebsiteData} nextStage={nextStage} getWebsiteTempData={getWebsiteTempData} updateWebsiteTempData={updateWebsiteTempData} setStage={setStage} />,  // icp text & industry
		// <WebsiteCompetitors getWebsiteData={getWebsiteData} updateWebsiteData={updateWebsiteData} nextStage={nextStage} setStage={setStage} />  // competitors
	];

	// -------------------- REFS --------------------
	const connectWebsiteData = useRef<WebsiteData>({
		domain: '',
		protocol: '',
		blog: '',
		title: '',
		description: '',
		industry: '',
		icp: '',
		language: '',
		competitors: [],
		generate_content_plan: false,
	});

	// Temporary Website Data this used when user go back to previous steps
	const websiteTempData = useRef<WebsiteData>({
		domain: '',
		protocol: '',
		blog: '',
		title: '',
		description: '',
		industry: '',
		icp: '',
		language: '',
		competitors: [],
		generate_content_plan: false,
	});

	const {
		data,
		refetch,
		status,
		fetchStatus
	} = useQuery(connectWebsiteQuery(connectWebsiteData.current));

	useEffect(() => {
		if (domain) {
			refetch();
		}
	}, [domain, refetch]);


	function getWebsiteData(): WebsiteData {
		return connectWebsiteData.current;
	}

	function updateWebsiteData(key: keyof WebsiteData, value: any) {
		// Update the domain state if the key is 'domain' and the value is not empty
		if (key === "domain" && value) {
			setDomain(value);
		}
		if (key !== "generate_content_plan") {
			connectWebsiteData.current[key] = value;
		}
	}

	function getWebsiteTempData(): WebsiteData {
		return websiteTempData.current;
	}
	function updateWebsiteTempData(key: keyof WebsiteData, value: any) {
		if (key !== 'generate_content_plan') websiteTempData.current[key] = value;
	}

	/**
	 * Use to move to next stage.
	 */
	function nextStage() {
		let newStage = stage + 1;
		if (newStage < stages.length) {
			setStage(stage + 1);
		} else {
			// Last stage is done. Time to call connect website api.
			if (connectWebsiteData.current.competitors.length > 0 && !called) {
				refetch().then();
				setCalled(true);
			}
		}
	}

	useEffect(() => {
		if (status === 'success') {
			props.setShowConnectWebsiteModal(false);

			if (props.navigateOrReload === "navigate") {
				// take users to  competitorResearch page
				navigate(pageURL['competitorResearch']);
			} else {
				window.location.reload();
			}

			props.successAlertRef.current?.show("Website connected successfully.");
			setTimeout(() => {
				props.successAlertRef.current?.close();
			}, 5000);
		}
	}, [status, props, navigate]);


	useEffect(() => {
		if (stage === 0) {
			props.failAlertRef.current?.show("An error occurred while connecting the website. Please try again.");
			setTimeout(() => {
				props.failAlertRef.current?.close();
			}, 3000);
		}
	}, [stage, props.failAlertRef]);

	return (
		<>
			<div className={"modal connect-website-modal is-active"}>
				<div className={"modal-background"}></div>
				<div className={"modal-content"}>
					<div className={"modal-card"}>
						<header className={"modal-card-head is-flex is-align-items-center is-justify-content-space-between"}>
							<p className={"modal-card-title"} style={{ opacity: 0, width: 0, height: 0, pointerEvents: 'none' }} />
							<p className={"modal-card-title"}>
								Create Project
							</p>
							{ // show only if stage is not 0
								stage <= 1 && closePop && <button type="button" className={"delete"} aria-label="close" onClick={() => props.setShowConnectWebsiteModal(false)} />
							}
						</header>
						<section className={"modal-card-body"}>
							<div className="container is-flex is-flex-direction-column is-align-items-center" style={{ maxHeight: "100vh", overflow: "none" }}>
								{fetchStatus !== 'idle' && stage !== stages.length - 1 ? <ConnectWebsiteLoading text={"Finishing setup. Just a moment..."} /> : stages[stage]}
							</div>
						</section>
					</div>
				</div>
			</div>
		</>
	)
}

export default ConnectWebsiteModal;
