import { lazy, Suspense } from 'react';
import AbunLoader from '../../components/AbunLoader/AbunLoader';

// Lazy load the ConnectWebsiteModal component
const ConnectWebsiteModalLazy = lazy(() => import('./ConnectWebsiteModal'));

// Props interface matching the original component
interface ConnectWebsiteProps {
  setShowConnectWebsiteModal: React.Dispatch<React.SetStateAction<boolean>>;
  successAlertRef: React.MutableRefObject<any>;
  failAlertRef: React.MutableRefObject<any>;
  navigateOrReload: "navigate" | "reload";
  basePageData?: any;
}

// Wrapper component that handles the lazy loading with a fallback
export function ConnectWebsite(props: ConnectWebsiteProps) {
  return (
    <Suspense fallback={
      <div className="modal connect-website-modal is-active">
        <div className="modal-background"></div>
        <div className="modal-content">
          <div className="modal-card">
            <header className="modal-card-head is-flex is-align-items-center is-justify-content-space-between">
              <p className="modal-card-title" style={{ opacity: 0, width: 0, height: 0, pointerEvents: 'none' }} />
              <p className="modal-card-title">
                Create Project
              </p>
            </header>
            <section className="modal-card-body">
              <div className="container is-flex is-flex-direction-column is-align-items-center" style={{ maxHeight: "100vh", overflow: "none" }}>
                <div className="loadingData w-100 is-flex is-justify-content-center is-align-items-center">
                  <AbunLoader show={true} height="20vh" />
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    }>
      <ConnectWebsiteModalLazy {...props} />
    </Suspense>
  );
}

export default ConnectWebsite;
