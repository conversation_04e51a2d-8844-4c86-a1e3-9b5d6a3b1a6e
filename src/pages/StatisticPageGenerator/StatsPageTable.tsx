import { useState, useRef, useEffect } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import { useQuery, useMutation } from '@tanstack/react-query';
import { getAIStatsPagesQuery, getAIStatsPageDataQuery } from "../../utils/api";
import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import AbunTable from "../../components/AbunTable/AbunTable";
import Icon from "../../components/Icon/Icon";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { withAdminAndProductionCheck } from "../../utils/adminAndProductionCheck";
import { pageURL } from "../routes";
import './StatsPageTable.min.css'; 
import AbunLoader from "../../components/AbunLoader/AbunLoader";

interface AIStatsPage {
    id: number;
    stats_id: string;
    stats_type: string;
    stats_topic: string;
    is_verified: boolean;
    version_count: number;
    created_on: string;
    created_on_relative: string;
    original_keyword?: string;
    original_ideas?: string[];
    selected_idea_index?: number;
}

function StatsPageTable() {
    // --------------------------- CONSTANTS ---------------------------
    const pageSizes = [5, 10, 15, 30, 50, 100, 500];

    // --------------------------- STATES ---------------------------
    const [statsPages, setStatsPages] = useState<AIStatsPage[]>([]);
    const [loadingRowId, setLoadingRowId] = useState<string | null>(null);
    const [selectedStatsId, setSelectedStatsId] = useState<string | null>(null);

    // --------------------------- REFS ---------------------------
    const errorAlertRef = useRef<any>(null);
    const successAlertRef = useRef<any>(null);

    // --------------------------- HOOKS ---------------------------
    const navigate = useNavigate();
    const { statsId, taskId } = useParams<{ statsId?: string; taskId?: string }>();
    const location = useLocation();
    const [currentStatsId, setCurrentStatsId] = useState<string | null>(null);

    // --------------------------- QUERIES ---------------------------
    
    // Query for getting all stats pages
    const {
        isLoading: isLoadingPages,
        error: pagesError,
        data: pagesData
    } = useQuery({
        ...getAIStatsPagesQuery()
    });

    // Query for getting specific stats page data (only runs when selectedStatsId is set)
    const {
        isLoading: isLoadingStatsData,
        error: statsDataError,
        data: statsData,
        refetch: refetchStatsData
    } = useQuery({
        ...getAIStatsPageDataQuery(selectedStatsId || ''),
        enabled: !!selectedStatsId, // Only run when we have a statsId
    });

    // Handle pages data processing
    useEffect(() => {
        if (pagesData) {
            let pages: AIStatsPage[] = [];
            
            if ((pagesData as any)?.status === 'success' && (pagesData as any)?.data?.pages) {
                pages = (pagesData as any).data.pages;
            } else if ((pagesData as any)?.data?.status === 'success' && (pagesData as any)?.data?.data?.pages) {
                pages = (pagesData as any).data.data.pages;
            } else if (Array.isArray((pagesData as any)?.data)) {
                pages = (pagesData as any).data;
            } else if (Array.isArray((pagesData as any)?.pages)) {
                pages = (pagesData as any).pages;
            }
            
            setStatsPages(pages);
        }
    }, [pagesData]);

    // Handle pages error
    useEffect(() => {
        if (pagesError) {
            console.error('Error loading stats pages:', pagesError);
            errorAlertRef.current?.show("Failed to load stats pages");
        }
    }, [pagesError]);

    // Handle stats data success
    useEffect(() => {
        if (statsData && selectedStatsId) {
            setLoadingRowId(null);
            
            if ((statsData as any)?.data?.status === 'success' && (statsData as any)?.data?.stats_data) {
                const statsDataObj = (statsData as any).data.stats_data;
                successAlertRef.current?.show(`Loaded stats page: ${statsDataObj.stats_topic}`);
                
                navigate(`${pageURL['staticPageGenerator']}/${statsDataObj.stats_id}`, {
                    state: {
                        statsId: statsDataObj.stats_id,
                        statsType: statsDataObj.stats_type,
                        statsTopic: statsDataObj.stats_topic,
                        statsData: statsDataObj,
                        fromExisting: true,
                        navigationTimestamp: Date.now()
                    },
                    replace: false
                });
                
                // Reset the selected stats ID after navigation
                setSelectedStatsId(null);
            } else {
                errorAlertRef.current?.show("Invalid response format");
                setSelectedStatsId(null);
            }
        }
    }, [statsData, selectedStatsId, navigate]);

    // Handle stats data error
    useEffect(() => {
        if (statsDataError && selectedStatsId) {
            setLoadingRowId(null);
            setSelectedStatsId(null);
            
            console.error('API Error:', statsDataError);
            let errorMessage = "Failed to load stats page data";
            
            if ((statsDataError as any).response?.data?.message) {
                errorMessage = (statsDataError as any).response.data.message;
            }
            
            errorAlertRef.current?.show(errorMessage);
        }
    }, [statsDataError, selectedStatsId]);

    // Handle URL params on component mount
    useEffect(() => {
        if (statsId) {
            setCurrentStatsId(statsId);
            if (!location.state?.statsData) {
                fetchStatsPageData(statsId);
            }
        } else if (location.state?.statsId) {
            setCurrentStatsId(location.state.statsId);
        }
    }, [statsId, location.state]);
    
    // --------------------------- HANDLERS ---------------------------
    const handleCreateNewStatsPage = () => {
        navigate(pageURL['staticPageGenerator']);
    };

    const handleViewStatsPage = (statsPage: AIStatsPage) => {
        if (!statsPage.stats_id) {
            errorAlertRef.current?.show("Invalid stats page data");
            return;
        }
        
        setLoadingRowId(statsPage.stats_id);
        fetchStatsPageData(statsPage.stats_id.trim());
    };

    const fetchStatsPageData = (id: string) => {
        setSelectedStatsId(id);
        // The query will automatically run due to the enabled condition
    };

    // --------------------------- UTILITY FUNCTIONS ---------------------------
    const formatRelativeTime = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMs = now.getTime() - date.getTime();
        const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
        const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
        const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

        // Less than 5 minutes
        if (diffInMinutes < 5) {
            return "few minutes ago";
        }
        
        // 5-59 minutes (in 10-minute intervals)
        if (diffInMinutes < 60) {
            const roundedMinutes = Math.floor(diffInMinutes / 10) * 10;
            return `${roundedMinutes} mins ago`;
        }
        
        // 1 hour
        if (diffInHours === 1) {
            return "an hour ago";
        }
        
        // 2-23 hours
        if (diffInHours < 24) {
            return `${diffInHours} hours ago`;
        }
        
        // 1-29 days
        if (diffInDays < 30) {
            return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;
        }
        
        // 30+ days - show formatted date (13, May 25)
        const day = date.getDate();
        const monthNames = [
            "Jan", "Feb", "Mar", "Apr", "May", "Jun",
            "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
        ];
        const month = monthNames[date.getMonth()];
        const year = date.getFullYear().toString().slice(-2); // Get last 2 digits
        
        return `${day}, ${month} ${year}`;
    };

    // Helper function to truncate text with ellipsis on last 3 characters
    const truncateText = (text: string, maxLength: number = 80) => {
        if (text.length <= maxLength) {
            return text;
        }
        
        // Show ellipsis only on the last 3 characters
        const truncated = text.substring(0, maxLength - 3);
        return truncated + '...';
    };

    // --------------------------- TABLE CONFIG ---------------------------
    const columnHelper = createColumnHelper<AIStatsPage>();

    const columnDefs: ColumnDef<AIStatsPage, any>[] = [
        columnHelper.accessor('stats_topic', {
            header: "Stats Page Title",
            cell: (info) => {
                const fullText = info.getValue();
                const row = info.row.original;
                const isRowLoading = loadingRowId === row.stats_id;
                const displayText = truncateText(fullText, 80);
                
                return (
                    <div 
                        className={`stats-title-cell ${isRowLoading ? 'loading' : ''}`}
                        style={{
                            maxWidth: '600px',
                            display: 'flex',
                            alignItems: 'center',
                            opacity: isRowLoading ? 0.6 : 1,
                            pointerEvents: 'none', 
                            width: '100%',
                            height: '100%'
                        }}
                        title={fullText} 
                    >
                        {isRowLoading && (
                            <Icon iconName="spinner" marginClass="mr-2" />
                        )}
                        <span style={{ pointerEvents: 'none' }}>{displayText}</span>
                    </div>
                );
            }
        }),
        columnHelper.accessor('created_on', {
            header: "Created On",
            cell: (info) => {
                const row = info.row.original;
                const isRowLoading = loadingRowId === row.stats_id;
                
                return (
                    <div style={{ 
                        opacity: isRowLoading ? 0.6 : 1,
                        pointerEvents: 'none',
                        width: '100%',
                        height: '100%'
                    }}>
                        {formatRelativeTime(info.getValue())}
                    </div>
                );
            }
        })
    ];

    return (
        <div className="ai-stats-table-tp-container">
            <div className="seo-project-header"></div>
            <h1>Create AI Stats Page</h1>
            
            <div className="menu-btns AI-keyword-research-btn" onClick={handleCreateNewStatsPage}>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                    <path d="m9 9 5 12 1.774-5.226L21 14 9 9z"></path>
                </svg>
                <span className="menu-btn-text">Create New AI Stats Page</span>
            </div>
            
            <hr className="horizontal-rule" />
            
            <div className="seo-project-abun-table">
                <h1>Your AI Stats Pages</h1>
                
                <div className="table-container">
                    {isLoadingPages ? (
                        <AbunLoader show={isLoadingPages} height="400px" />
                    ) : (
                        <AbunTable
                            tableContentName="AI Stats Pages"
                            tableData={statsPages}
                            columnDefs={columnDefs}
                            pageSizes={pageSizes}
                            initialPageSize={pageSizes[1]}
                            noDataText="No Stats Pages Found. Create your first stats page to get started!"
                            searchboxPlaceholderText="Search stats pages..."
                            handleRowClick={(row) => handleViewStatsPage(row)}
                        />
                    )}
                </div>
            </div>
            
            <ErrorAlert ref={errorAlertRef} />
            <SuccessAlert ref={successAlertRef} />
        </div>
    );
}

export default withAdminAndProductionCheck(StatsPageTable);