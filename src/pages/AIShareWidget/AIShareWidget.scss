@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";
@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/elements/container";
@import 'bulma/sass/form/_all';
@import "bulma/sass/components/tabs";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";
@import "../../assets/bulma-overrides/bulmaOverrides";

.ai-share-widget-table {
    font-family: $primary-font;
}

.ai-widget-abun-table {
    .abun-table-responsive {
        tbody {
            color: #000;
        }
    }
}

.widget-create-container {
    display: flex;
    gap: 2rem;
    align-items: flex-start;
    
    @media (max-width: 1024px) {
        flex-direction: column;
        gap: 1.5rem;
    }
}

.widget-form-section {
    flex: 1;
    max-width: 800px;
}

.widget-preview-section {
    flex: 1;
    position: sticky;
    top: 2rem;
    
    @media (max-width: 1024px) {
        flex: 1;
        position: relative;
        top: auto;
    }
}

.preview-header {
    margin-bottom: 0;
    padding-bottom: 0.5rem;
    
    .preview-title {
        color: #333;
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0 0 0.5rem 0;
    }
    
    hr {
        margin: 0;
    }
}

.preview-container {
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 0;
    min-height: 200px;
    background: #ffffff;
    overflow: hidden;
}

.preview-modes {
    padding: 1rem 1rem 1rem 1rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;

    hr {
        background: #e7e7e7;
        height: 1px;
        margin: 0;
        width: 100%;
    }
}

.preview-mode {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e7e7e7;
    
    &:has(.dark-mode) {
        border: 1px solid #2d3748;
        background: #000;
    }
}

.mode-label {
    padding: 0.75rem 1rem;
    font-weight: 600;
    font-size: 0.875rem;
    margin: 0;
    
    .preview-mode:has(.light-mode) & {
        background: #f8f9fa;
        border-bottom: 1px solid #e7e7e7;
        color: #333;
    }
    
    .preview-mode:has(.dark-mode) & {
        background: #0000007c;
        border-bottom: 1px solid #000000a2;
        color: #ffffff;
    }
}

.widget-preview {
    font-family: 'Inter', sans-serif;
    
    &.light-mode {
        background: #ffffff;
        color: #000;
    }
    
    &.dark-mode {
        background: #000;
        color: #ffffff;
        
        .text {
            color: #ffffff !important;
        }
    }
    
}

.widget-container {
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    width: 100%;
    position: relative;
    overflow: visible;
    z-index: 1;

    .field:has(select) {
        .select {
            select {
                option {
                    white-space: normal;
                    padding: 8px 12px;
                    line-height: 1.4;
                }
            }
        }
    }

    .field {
        input[type="text"] {
            width: 60%;
            max-width: 400px;
        }
        
        .select {
            width: 60%;
            max-width: 300px;
            
            &.is-fullwidth {
                width: 60% !important;
            }
        }
    }

    .ca-label {
        font-size: 18px;
        font-weight: 500;
        color: #000 !important;
    }

    .ca-input::placeholder {
        color: #666 !important;
        
        @media(max-width:480px) {
            font-size: 13px;
        }
    }

    .ca-input {
        font-size: 14px;
        color: #000 !important;
    }

    h3 {
        font-size: 1.4rem;
        font-weight: 600;
        padding: 1rem;
        align-self: start;
        color: #000 !important;
    }

    hr {
        background: #e7e7e7;
        height: 1px;
        margin: 0;
        width: 100%;
    }

    .llm-checkbox-grid {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 10px; 
    
    @media (max-width: 768px) {
        flex-direction: column;
        gap: 10px;
    }
    
    @media (min-width: 769px) and (max-width: 1024px) {
        justify-content: flex-start;
        
        .checkbox-item {
            flex: 0 0 calc(50% - 5px);
            max-width: calc(50% - 5px);
        }
    }
    
    @media (min-width: 1025px) {
        justify-content: flex-start;
        
        .checkbox-item {
            flex: 0 0 auto;
        }
    }
    
    .checkbox-item {
        .checkbox {
            display: flex;
            align-items: center;
            cursor: pointer;
            gap: 12px;
            padding: 8px 12px;
            border-radius: 6px;
            transition: background-color 0.2s ease;
            
            &:hover {
                background-color: #f8f9fa;
            }
            
            input[type="checkbox"] {
                margin: 0;
                width: 18px;
                height: 18px;
                cursor: pointer;
                accent-color: #3273dc;
            }
            
            img {
                width: auto; 
                height: 35px; 
                object-fit: contain;
                transition: opacity 0.2s ease;
                
                @media (max-width: 480px) {
                    width: auto; 
                    height: 35px; 
                }
                
                @media (min-width: 1200px) {
                    width: auto; 
                    height: 35px; 
                }
            }
        }
    }
}

    .prompt-type-radio {
        display: flex;
        gap: 3rem;
        align-items: center;

        .radio {
            display: flex;
            align-items: center;
            cursor: pointer;

            input[type="radio"] {
                margin-right: 0.75rem;
                width: 18px;
                height: 18px;
                cursor: pointer;
                accent-color: #3273dc;

                &:checked {
                    accent-color: #3273dc;
                }
            }

            label {
                cursor: pointer;
                font-size: 16px;
                font-weight: 500;
                margin: 0;
                color: #363636 !important;
                
                &:hover {
                    color: #3273dc !important;
                }
            }
        }

        .radio:hover {
            input[type="radio"] {
                accent-color: #2366d1;
            }
        }
    }
}

.ai-share-widget-container {
    font-family: $primary-font !important;

    .widget-header {
        h2 {
            font-family: $primary-font !important;
            font-size: 2rem !important;
            font-weight: 600 !important;
            margin-bottom: 4px;
        }

        .widget-p {
            font-family: $secondary-font !important;
            font-size: 1.125rem !important;
        }
    }

    .not-found-p {
        font-family: $secondary-font;
    }

    .widget-form {
        justify-items: center;
        display: flex;
        flex-direction: column;
        padding: 1rem;
        padding-left: 1.6rem;
        
        input, 
        textarea, 
        select,
        .input,
        .textarea,
        .select select {
            color: #000 !important;
            
            &::placeholder {
                color: #666 !important;
            }
            
            option {
                color: #000 !important;
                background: #fff !important;
            }
        }
        
        .field {
            .label {
                color: #000 !important;
            }
            
            .control {
                input,
                textarea,
                select {
                    color: #000 !important;
                    
                    &:focus,
                    &:active {
                        color: #000 !important;
                    }
                }
            }
        }
    }
}

.ai-share-widget-view-table {
    h1 {
        font-size: 2em;
        font-weight: normal;
        text-align: center;
        font-family: $primary-font !important;
    }
}

.preset-prompt-radio {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-width: 100%;
}

.preset-prompt-radio .radio-option {
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    padding: 12px;
    transition: all 0.2s ease;
    background-color: #fff;
    display: flex;
    align-items: flex-start;
    width: 100%;
}

.preset-prompt-radio .radio-option:hover {
    border-color: #3273dc;
    background-color: #f8f9fa;
}

.preset-prompt-radio .radio-option input:checked + label {
    color: #3273dc;
    border-color: #3273dc;
}

.preset-prompt-radio .radio-option input[type="radio"] {
    margin-right: 12px;
    margin-top: 4px;
    flex-shrink: 0;
    width: 16px;
    height: 16px;
}

.preset-prompt-radio .radio-option .radio-label {
    display: block;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
    flex: 1;
    width: 100%;
}

.preset-prompt-radio .radio-option .radio-text {
    display: block;
    word-wrap: break-word;
    color: #4a4a4a;
}

.preset-prompt-radio .radio-option:has(input:checked) {
    border-color: #3273dc;
    background-color: #f8f9fa;
}


//widget view
.ai-share-widget-view {
  font-family: $primary-font !important;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  border-radius: 10px;
  max-width: 100%;
  height: 100vh;
  margin: 0;
  overflow: auto;
  scrollbar-width: none;
  position: relative;
  top: -20px;
}

.ai-share-widget-view-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
  border-bottom: none;
  box-shadow: none;
  height: 6rem;
  padding: 2rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.left-header-section {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: nowrap;
  margin-left: 0;
  flex: 1;
  min-width: 0;
}

.back-btn {
  cursor: pointer;
  margin-right: 10px;
  flex-shrink: 0;

  svg {
    width: 30px;
    height: 24px;
  }
}

.Tabs {
  margin: 0 0 0 1rem;
  flex-shrink: 0;

  .Tab {
    &.active {
      font-family: $secondary-font;
      font-size: 2.5rem;
      font-weight: 600;
      color: #000000;
      opacity: 1;
      white-space: nowrap;
      border-bottom: none;

      .widget-date {
        font-weight: 400;
        font-size: 0.875rem;
        color: #666666;
      }
    }
  }
}

.ai-share-widget-container {
  padding: 1rem;
  width: 100%;
}

.ai-share-stats-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  justify-content: flex-start;
  margin: 1.5rem 0 2rem 0;
  color: #000 !important;

  .ai-share-stat-card {
    display: flex;
    flex-direction: column;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    width: 220px;
    flex-shrink: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
      border-color: #d1d5db;
    }

    .stat-header {
      display: flex;
      align-items: center;
      margin-bottom: 0.5rem;
      
      .stat-icon {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.5rem;
        flex-shrink: 0;

        img {
          width: 16px;
          height: 16px;
          object-fit: contain;
        }

        svg {
          width: 16px;
          height: 16px;
          color: #6b7280;
        }
      }

      .stat-label {
        font-family: $secondary-font;
        font-size: 0.875rem;
        font-weight: 500;
        color: #000000;
        line-height: 1.2;
        text-transform: none;
        flex: 1;
      }
    }

    .stat-value {
      font-family: $primary-font;
      font-size: 1.75rem;
      font-weight: 700;
      color: #000000;
      line-height: 1.2;
      letter-spacing: -0.025em;
      text-align: left;
    }

    &.chatgpt-card {
      .stat-icon {
        background: transparent;
      }
    }
  }
}

.widget-view-layout {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
  min-height: calc(100vh - 14rem);

  .widget-right-section {
    display: none !important;
  }

  &.analytics-layout {
    display: block;
    width: 100%;
    min-height: calc(100vh - 14rem);

    .widget-left-section {
      width: 100%;
      max-width: none;
    }
  }
}

.widget-left-section {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  align-self: stretch;
}

.tabs-container {
  flex: 1;
  display: flex;
  flex-direction: column;

  .tabs {
    margin-bottom: 0;
    border-bottom: 1px solid #e7e7e7;

    ul {
      border-bottom: none;

      li {
        a {
          border: none;
          border-bottom: 3px solid transparent;
          padding: 1rem 1.5rem;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            border-bottom-color: #3273dc;
            background: none;
          }
        }

        &.is-active a {
          color: #3273dc;
          border-bottom-color: #3273dc;
          background: none;
        }
      }
    }
  }
}

.tab-content {
  border-top: none;
  border-radius: 0 0 8px 8px;
  background: white;
  min-height: 400px;
  flex: 1;

  .tab-pane {
    padding: 0;
    height: 100%;

    &.embed-tab {
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    &.analytics-tab {
      padding: 1rem;
      
      .analytics-content {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        .analytics-stats-row {
          width: 100%;

          .chart-container {
            padding: 1rem;
            border-radius: 8px;
            width: 100%;
          }
        }
      }
    }
  }
}

.integration-instructions {
  background-color: #e0f2f1;
  font-family: $secondary-font;
  padding: 1rem 1.5rem;
  margin: 1rem 0;
  border-radius: 8px;
  width: 100%;
  color: #0f766e;
  max-width: 850px;
  margin-top: 40px;
  
  p {
    margin: 0 0 0.5rem 0;
    font-weight: 600;
    color: #0f766e;
    font-size: 0.9rem;
    
    strong {
      color: #0f766e !important;
    }
  }
  
  ul {
    margin: 0;
    padding-left: 1.2rem;
    list-style-type: disc;
    
    li {
      color: #0f766e;
      margin-bottom: 0.25rem;
      font-size: 0.85rem;
      line-height: 1.4;
      display: list-item;
    }
  }
}

.embed-layout-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 1rem;
  min-height: 600px;
  padding: 0;
  margin: 0;
}

.light-mode-section {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
  margin: 0;
  padding: 0;
}

.light-preview-container {
  flex: 2;
  margin: 0;
  padding: 0;

  .preview-widget {
    height: 220px;
    border-radius: 6px;
    overflow: hidden;
    background: white;
    margin: 0;
    padding: 0;

    iframe {
      width: 100% !important;
      height: 100% !important;
      border: none !important;
    }

    .preview-loading,
    .preview-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #64748b;

      p {
        margin-top: 0;
        font-size: 0.875rem;
      }
    }
  }
}

.light-script-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  padding: 1rem;

  .script-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 1rem 0;
  }

  .script-code-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;

    .script-textarea {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      height: 100px;
      line-height: 1.4;
      background-color: #f9fafb;
      color: #374151;
      resize: none;
      overflow-y: auto;
      
      &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }

    .copy-script-button {
      align-self: flex-end;
      padding: 0.5rem 1rem;
      background-color: #3b82f6;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #2563eb;
      }

      &:active {
        background-color: #1d4ed8;
      }
    }
  }
}

.dark-mode-section {
  display: flex;
  gap: 1rem;
  min-height: 180px;
  margin: 0;
  padding: 0;
  margin-top: 20px;
}

.dark-preview-container {
  flex: 2;
  margin: 0;
  padding: 0;

  .preview-widget {
    height: 220px;
    border-radius: 6px;
    overflow: hidden;
    margin: 0;
    padding: 0;

    iframe {
      width: 100% !important;
      height: 100% !important;
      border: none !important;
    }

    .preview-loading,
    .preview-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #94a3b8;

      p {
        margin-top: 1rem;
        font-size: 0.875rem;
      }
    }
  }
}

.dark-script-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;

  .script-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 1rem 0;
  }

  .script-code-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;

    .script-textarea {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      height: 100px;
      line-height: 1.4;
      background-color: #f9fafb;
      color: #374151;
      resize: none;
      overflow-y: auto;
      
      &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }

    .copy-script-button {
      align-self: flex-end;
      padding: 0.5rem 1rem;
      background-color: #3b82f6;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #2563eb;
      }

      &:active {
        background-color: #1d4ed8;
      }
    }
  }
}

.widget-right-section {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  align-self: stretch;
  border: none !important;
  
  .preview-container {
    background: white;
    min-height: 400px;
    border: none !important;
    box-shadow: none !important;
    
    .preview-content {
      padding: 1rem;
      border: none !important;
      
      .preview-mode {
        margin-bottom: 0;
        border: none !important;
        
        iframe {
          border: none !important;
        }
      }
    }
  }
}

.analytics-tab .abun-table-content {
  justify-content: flex-end !important;
}

.analytics-tab .abun-table-content .balancer {
  display: none !important;
}

@media (max-width: 1024px) {
  .ai-share-widget-view {
    .ai-share-widget-view-header {
      padding: 2rem 0.75rem;

      .left-header-section {
        .Tabs {
          margin: 0 0 0 1.5rem;

          .Tab.active {
            font-size: 1.125rem;
          }
        }
      }
    }

    .ai-share-widget-container {
      padding: 0.75rem;
    }
  }

  .widget-view-layout {
    flex-direction: column;
    gap: 1rem;
    min-height: auto;

    .embed-layout-container {
      flex-direction: column !important;
      
      .embed-preview-section {
        border-right: none !important;
        border-bottom: 1px solid #e2e8f0;
      }
    }
  }

  .embed-layout-container {
    .light-mode-section,
    .dark-mode-section {
      flex-direction: column;
      min-height: auto;
      gap: 1rem;

      .light-preview-container,
      .dark-preview-container,
      .light-script-container,
      .dark-script-container {
        .preview-widget {
          height: 200px;
        }

        .script-code-wrapper .script-textarea {
          min-height: 100px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .ai-share-widget-view {
    .ai-share-widget-view-header {
      padding: 2rem 0.5rem;

      .left-header-section {
        .back-btn {
          margin-right: 0.5rem;

          svg {
            width: 24px;
            height: 20px;
          }
        }

        .Tabs {
          margin: 0 0 0 1rem;

          .Tab.active {
            font-size: 1rem;
          }
        }
      }
    }

    .ai-share-widget-container {
      padding: 0.5rem;
    }
  }

  .ai-share-stats-cards {
    flex-direction: column;
    gap: 0.5rem;

    .ai-share-stat-card {
      width: 100%;
      padding: 0.875rem;
    }
  }

  .embed-layout-container {
    flex-direction: column !important;
    min-height: auto !important;
    
    .embed-preview-section {
      border-right: none !important;
      border-bottom: 1px solid #e2e8f0;
      
      .preview-widget {
        min-height: 200px !important;
      }
    }
    
    .embed-scripts-section {
      .script-container {
        .script-code-wrapper {
          .script-textarea {
            min-height: 10f0px !important;
            font-size: 11px;
          }
        }
      }
    }

    gap: 1.5rem;

    .light-mode-section,
    .dark-mode-section {
      .light-preview-container,
      .dark-preview-container,
      .light-script-container,
      .dark-script-container {
        .preview-widget {
          height: 180px;
        }

        .script-code-wrapper .script-textarea {
          min-height: 80px;
          font-size: 11px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .ai-share-widget-view {
    .ai-share-widget-view-header {
      padding: 2rem 0.25rem;

      .left-header-section {
        .back-btn {
          margin-right: 0.25rem;
        }

        .Tabs {
          margin: 0 0 0 0.5rem;

          .Tab.active {
            font-size: 0.9rem;
          }
        }
      }
    }

    .ai-share-widget-container {
      padding: 0.25rem;
    }
  }
}