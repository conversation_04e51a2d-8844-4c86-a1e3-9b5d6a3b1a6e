import { useEffect } from 'react';
import { useNavigate, useRouteLoaderData } from 'react-router-dom';
import { BasePageData } from '../pages/Base/Base';

/**
 * A hook that checks if the current user is an admin.
 * If not, it redirects to the 404 page.
 *
 * @returns {boolean} - Whether the user is an admin
 */
export const useAdminAndProductionCheck = (): boolean => {
  const baseData = useRouteLoaderData('base') as BasePageData;
  const navigate = useNavigate();

  // Check if user is admin
  const isAdmin = baseData?.is_admin;

  // Check if we're in production
  const isProduction = process.env.REACT_APP_DRF_DOMAIN === "https://api.abun.com";

  useEffect(() => {
    if (!isAdmin && isProduction) {
      navigate('/404', { replace: true });
    }
  }, [isAdmin, navigate]);

  return isAdmin;
};

/**
 * A higher-order component that wraps a component and only renders it if the user is an admin.
 * If the user is not an admin, it redirects to the 404 page.
 *
 * @param Component - The component to wrap
 * @returns A new component that checks admin status before rendering
 */
export const withAdminAndProductionCheck = (Component: React.ComponentType<any>) => {
  return (props: any) => {
    const isAdmin = useAdminAndProductionCheck();
    const isProduction = process.env.REACT_APP_DRF_DOMAIN === "https://api.abun.com";

    if (!isAdmin && isProduction) {
      return null; // Will redirect in the hook
    }

    return <Component {...props} />;
  };
};
