import { Language } from "../../pages/Settings/ArticleDetails";

const allSupportedLanguagesOptions: Language[] = [
   {
      value: "albanian",
      label: "Albanian",
   },
   {
      value: "arabic",
      label: "Arabic"
   },
   {
      value: "armenian",
      label: "Armenian",
   },
   {
      value: "azerbaijani",
      label: "Azerbaijani",
   },
   {
      value: "basque",
      label: "Basque",
   },
   {
      value: "belarusian",
      label: "Belarusian",
   },
   {
      value: "bengali",
      label: "Bengali",
   },
   {
      value: "bhojpuri",
      label: "Bhojpuri",
   },
   {
      value: "bosnian",
      label: "Bosnian",
   },
   {
      value: "bulgarian",
      label: "Bulgarian",
   },
   {
      value: "catalan",
      label: "Catalan",
   },
   {
      value: "chinese",
      label: "Chinese"
   },
   {
      value: "croatian",
      label: "Croatian",
   },
   {
      value: "czech",
      label: "Czech",
   },
   {
      value: "danish",
      label: "Danish",
   },
   {
      value: "dogri",
      label: "Dogri",
   },
   {
      value: "dutch",
      label: "Dutch",
   },
   {
      value: "Australian English (en-au)",
      label: "English (AUS)"
   },
   {
      value: "British English (en-gb)",
      label: "English (UK)"
   },
   {
      value: "American English (en-us)",
      label: "English (US)"
   },
   {
      value: "estonian",
      label: "Estonian",
   },
   {
      value: "finnish",
      label: "Finnish",
   },
   {
      value: "french",
      label: "French"
   },
   {
      value: "galician",
      label: "Galician",
   },
   {
      value: "georgian",
      label: "Georgian",
   },
   {
      value: "german",
      label: "German"
   },
   {
      value: "greek",
      label: "Greek",
   },
   {
      value: "gujarati",
      label: "Gujarati",
   },
   {
      value: "hindi",
      label: "Hindi"
   },
   {
      value: "hungarian",
      label: "Hungarian",
   },
   {
      value: "indonesian",
      label: "Indonesian",
   },
   {
      value: "irish",
      label: "Irish",
   },
   {
      value: "italian",
      label: "Italian"
   },
   {
      value: "japanese",
      label: "Japanese"
   },
   {
      value: "javanese",
      label: "Javanese",
   },
   {
      value: "kannada",
      label: "Kannada",
   },
   {
      value: "kazakh",
      label: "Kazakh",
   },
   {
      value: "konkani",
      label: "Konkani",
   },
   {
      value: "korean",
      label: "Korean"
   },
   {
      value: "kyrgyz",
      label: "Kyrgyz",
   },
   {
      value: "latvian",
      label: "Latvian",
   },
   {
      value: "lithuanian",
      label: "Lithuanian",
   },
   {
      value: "macedonian",
      label: "Macedonian",
   },
   {
      value: "maithili",
      label: "Maithili",
   },
   {
      value: "malay",
      label: "Malay",
   },
   {
      value: "maltese",
      label: "Maltese",
   },
   {
      value: "marathi",
      label: "Marathi",
   },
   {
      value: "mongolian",
      label: "Mongolian",
   },
   {
      value: "nepali",
      label: "Nepali",
   },
   {
      value: "norwegian",
      label: "Norwegian",
   },
   {
      value: "pashto",
      label: "Pashto",
   },
   {
      value: "persian",
      label: "Persian", 
   },
   {
      value: "polish",
      label: "Polish",
   },
   {
      value: "portuguese",
      label: "Portuguese"
   },
   {
      value: "punjabi",
      label: "Punjabi",
   },
   {
      value: "romanian",
      label: "Romanian",
   },
   {
      value: "russian",
      label: "Russian"
   },
   {
      value: "sanskrit",
      label: "Sanskrit",
   },
   {
      value: "serbian",
      label: "Serbian",
   },
   {
      value: "sindhi",
      label: "Sindhi",
   },
   {
      value: "slovak",
      label: "Slovak",
   },
   {
      value: "slovenian",
      label: "Slovenian",
   },
   {
      value: "spanish",
      label: "Spanish"
   },
   {
      value: "swedish",
      label: "Swedish",
   },
   {
      value: "thai",
      label: "Thai",
   },
   {
      value: "turkish",
      label: "Turkish",
   },
   {
      value: "ukrainian",
      label: "Ukrainian",
   },
   {
      value: "urdu",
      label: "Urdu",
   },
   {
      value: "uzbek",
      label: "Uzbek",
   },
   {
      value: "vietnamese",
      label: "Vietnamese",
   },
   {
      value: "welsh",
      label: "Welsh",
   }
];

export default allSupportedLanguagesOptions;
