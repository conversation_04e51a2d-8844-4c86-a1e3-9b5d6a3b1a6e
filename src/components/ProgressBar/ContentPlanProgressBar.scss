@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/components/card";

@import "../../assets/bulma-overrides/bulmaOverrides";

//content plan progress bar
.content-plan-progress-bar {
    position: fixed;
    bottom: 2rem;
    width: max-content;
    display: flex;
    align-items: center;

    @mixin gradient-striped($color: rgba(255, 255, 255, .15), $angle: 45deg) {
        background-image: -webkit-linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);
        background-image: -o-linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);
        background-image: linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);
    }

    @-webkit-keyframes progress-bar-stripes {
        from {
            background-position: 40px 0;
        }

        to {
            background-position: 0 0;
        }
    }

    @keyframes progress-bar-stripes {
        from {
            background-position: 40px 0;
        }

        to {
            background-position: 0 0;
        }
    }

    @mixin animation($animation) {
        -webkit-animation: $animation;
        -o-animation: $animation;
        animation: $animation;
    }


    .progress-background {
        width: 100%;
        height: 1rem;
    }

    .progress-bar {
        @include animation(progress-bar-stripes 2s linear infinite);
        @include gradient-striped;
        background-size: 40px 40px;
        background-color: #64DD17;
        height: 0.6rem;
        margin: 0 !important;
        margin-bottom: 0 !important;
    }

    @include until($tablet) {
        width: 100%;
    }

    &.full-height {
        width: 100% !important;
    }

    .progress-bar-container {
        position: relative;
        width: 25rem;
        height: 6rem;
        margin: 0;
        padding: 1rem;
        padding-top: 2rem;
        border-radius: 4px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .progress-bar-text {
            width: 100%;
            text-align: center;
            font-size: 0.8rem;
            color: $black;

            span span {
                color: $primary;
            }
        }
    }
}