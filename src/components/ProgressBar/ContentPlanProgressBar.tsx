import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { contentPlanDonePollingQuery } from "../../utils/api";
import './ContentPlanProgressBar.min.css'

export default function ContentPlanProgressBar() {
   const [contentPlanProgress, setContentPlanProgress] = useState<number>(0);
   const [contentPlanStatus, setContentPlanStatus] = useState<string>("");

   const { data } = useQuery(contentPlanDonePollingQuery());

   useEffect(() => {
      if (data) {
         const status = (data as any)["data"]["status"];
         const progress = (data as any)["data"]["progress"] as number;
         setContentPlanProgress(progress);
         setContentPlanStatus(status);
      }
   }, [data]);

   if (contentPlanStatus !== "processing") {
      return <></>;
   }


   return (
      <div className="content-plan-progress-bar">
         <div className="card progress-bar-container">
            <div className="progress-background">
               <div
                  className="progress-bar"
                  style={{ width: `${contentPlanProgress}%` }}
               ></div>
            </div>
            <div className="progress-bar-text">
               <span>
                  Content Plan Generation in Progress:{" "}
                  <span className="percent">{contentPlanProgress}%</span> done
               </span>
            </div>
         </div>
      </div>
   );
}