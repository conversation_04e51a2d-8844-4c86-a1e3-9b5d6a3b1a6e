.progress-bar-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;

  &.visible {
    opacity: 1;
  }

  .progressbar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // background-color: rgba(255, 255, 255, 0.5);
  }

  .progressbar {
    background: linear-gradient(90deg, #2E64FE, hsla(0, 0%, 63%, 0.604));
    border-radius: 0;
    height: 5px;
    opacity: 0;
    overflow: hidden;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 10000;

    &.visible {
      opacity: 1;
      transition: opacity 0.5s ease-in-out;
    }

    &:before {
      animation: waveAnimation 1.5s linear infinite;
      background: linear-gradient(90deg, hsla(0, 0%, 63%, 0.604), #2E64FE);
      content: "";
      display: block;
      height: 5px;
      position: absolute;
      width: 100%;
    }

  }
}

@keyframes waveAnimation {
  0% {
    transform: translateX(-100%);
  }

  to {
    transform: translateX(0);
  }
}