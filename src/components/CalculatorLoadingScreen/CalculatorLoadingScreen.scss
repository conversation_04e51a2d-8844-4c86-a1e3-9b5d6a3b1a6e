.calculator-loading-screen {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 2rem;
    
    .calculator-loading-container {
        background: white;
        border-radius: 20px;
        padding: 3rem 2rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        max-width: 600px;
        width: 100%;
        text-align: center;
        
        .calculator-loading-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2rem;
        }
    }
    
    .calculator-icon-container {
        position: relative;
        
        .calculator-icon {
            width: 150px;
            height: 180px;
            animation: float 3s ease-in-out infinite;
            
            .calculator-screen {
                animation: screenGlow 2s ease-in-out infinite alternate;
            }
            
            .calculator-display {
                animation: textBlink 1.5s ease-in-out infinite;
            }
            
            .calc-button {
                animation: buttonPulse 2s ease-in-out infinite;
                
                &.operator {
                    animation: operatorGlow 1.8s ease-in-out infinite alternate;
                }
                
                &.equals {
                    animation: equalsGlow 2.2s ease-in-out infinite alternate;
                }
            }
            
            .steam-container {
                .steam {
                    opacity: 0;
                    animation: steamRise 3s ease-in-out infinite;
                    
                    &.steam-1 {
                        animation-delay: 0s;
                    }
                    
                    &.steam-2 {
                        animation-delay: 0.5s;
                    }
                    
                    &.steam-3 {
                        animation-delay: 1s;
                    }
                }
            }
        }
    }
    
    .loading-text-container {
        .loading-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            line-height: 1.3;
        }
        
        .loading-subtitle {
            font-size: 1.1rem;
            color: #7f8c8d;
            margin-bottom: 0.5rem;
        }
        
        .loading-message {
            font-size: 1rem;
            color: #95a5a6;
            margin-bottom: 1.5rem;
        }
        
        .loading-dots {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            
            .dot {
                width: 12px;
                height: 12px;
                background: #2E64FE;
                border-radius: 50%;
                animation: dotBounce 1.4s ease-in-out infinite both;
                
                &:nth-child(1) { animation-delay: -0.32s; }
                &:nth-child(2) { animation-delay: -0.16s; }
                &:nth-child(3) { animation-delay: 0s; }
            }
        }
    }
}

// Animations
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes screenGlow {
    0% {
        fill: #000;
    }
    100% {
        fill: #1a1a1a;
        filter: drop-shadow(0 0 5px #00FF00);
    }
}

@keyframes textBlink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.3;
    }
}

@keyframes buttonPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

@keyframes operatorGlow {
    0% {
        fill: #FFD700;
    }
    100% {
        fill: #FFA500;
        filter: drop-shadow(0 0 3px #FFD700);
    }
}

@keyframes equalsGlow {
    0% {
        fill: #FF6B6B;
    }
    100% {
        fill: #FF4757;
        filter: drop-shadow(0 0 3px #FF6B6B);
    }
}

@keyframes steamRise {
    0% {
        opacity: 0;
        transform: translateY(0) scale(1);
    }
    50% {
        opacity: 0.7;
        transform: translateY(-10px) scale(1.1);
    }
    100% {
        opacity: 0;
        transform: translateY(-20px) scale(0.8);
    }
}

@keyframes dotBounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

// Responsive Design
@media (max-width: 768px) {
    .calculator-loading-screen {
        padding: 1rem;
        
        .calculator-loading-container {
            padding: 2rem 1.5rem;
        }
        
        .calculator-icon-container .calculator-icon {
            width: 120px;
            height: 144px;
        }
        
        .loading-text-container {
            .loading-title {
                font-size: 1.5rem;
            }
            
            .loading-subtitle {
                font-size: 1rem;
            }
            
            .loading-message {
                font-size: 0.9rem;
            }
        }
    }
}
