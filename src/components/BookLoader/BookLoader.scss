.spinner-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 10rem;

    @keyframes tri-spinner {
        100% {
            transform: rotate(1turn);
        }
    }

    .book-loader-container {
        display: flex;
        margin-top: 1rem;
    }

    .book-loader {
        color: #4a4a4a;
        font-family: "Poppins", sans-serif;
        font-weight: 500;
        font-size: 25px;
        -webkit-box-sizing: content-box;
        box-sizing: content-box;
        height: 40px;
        padding: 10px 10px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        flex-direction: column;
        border-radius: 8px;
    }


    .words {
        overflow: hidden;
    }

    .word {
        display: block;
        height: 100%;
        padding-left: 6px;
        color: #299fff;
        animation: cycle-words 5s infinite;
    }

    .book-wrapper {
        width: 150px;
        height: fit-content;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        position: relative;
    }

    .book {
        width: 100%;
        height: auto;
        filter: drop-shadow(10px 10px 5px rgba(0, 0, 0, 0.137));
    }

    .book-wrapper .book-page {
        width: 50%;
        height: auto;
        position: absolute;
        animation: paging 1.20s linear infinite;
        transform-origin: left;
    }

    @keyframes paging {
        0% {
            transform: rotateY(0deg) skewY(0deg);
        }

        50% {
            transform: rotateY(90deg) skewY(-20deg);
        }

        100% {
            transform: rotateY(180deg) skewY(0deg);
        }
    }

    @keyframes cycle-words {
        10% {
            -webkit-transform: translateY(-105%);
            transform: translateY(-105%);
        }

        25% {
            -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
        }

        35% {
            -webkit-transform: translateY(-205%);
            transform: translateY(-205%);
        }

        50% {
            -webkit-transform: translateY(-200%);
            transform: translateY(-200%);
        }

        60% {
            -webkit-transform: translateY(-305%);
            transform: translateY(-305%);
        }

        75% {
            -webkit-transform: translateY(-300%);
            transform: translateY(-300%);
        }

        85% {
            -webkit-transform: translateY(-405%);
            transform: translateY(-405%);
        }

        100% {
            -webkit-transform: translateY(-400%);
            transform: translateY(-400%);
        }
    }
}