import { Link } from "react-router-dom";
import './ErrorAlert.min.css';
import { forwardRef, useImperativeHandle, useState } from "react";
import ReactMarkdown from 'react-markdown';

type NextStep = {
   nextStepLinkText: string,
   nextStepLinkUrl: string,
}

interface ErrorAlertProps {
	style?: React.CSSProperties;
}

export default forwardRef(function ErrorAlert(props: ErrorAlertProps, ref) {
   const [errorMessage, setErrorMessage] = useState("");
   const [nextStep, setNextStep] = useState<NextStep>();

   useImperativeHandle(
      ref,
      () => {
         return {
            show,
            close,
         };
      },
      []
   );

   // optionally accepts a link
   function show(errorMessage: string, nextStep: NextStep) {
      setErrorMessage(errorMessage);
      nextStep && setNextStep(nextStep);
   }

   function close() {
      setErrorMessage("");
   }

   if (errorMessage) {
      return (
         <div className="notification is-warning position-bottom" style={props.style}>
            <button className="delete" onClick={close}></button>
            <ReactMarkdown>{errorMessage}</ReactMarkdown>
            {" "}
            {nextStep?.nextStepLinkUrl && <Link to={nextStep.nextStepLinkUrl}>{nextStep.nextStepLinkText}</Link>}
         </div>
      );
   } else {
      return null;
   }
});
