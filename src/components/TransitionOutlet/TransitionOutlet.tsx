import React, { Suspense } from 'react';
import { Outlet } from 'react-router-dom';
import AbunLoader from '../AbunLoader/AbunLoader';
import './TransitionOutlet.min.css';

const TransitionOutlet: React.FC = () => {
  return (
    <Suspense fallback={
      <div className="transition-outlet-loading">
        <AbunLoader show={true} height="20vh" />
      </div>
    }>
      <Outlet />
    </Suspense>
  );
};

export default TransitionOutlet;
