import './LinkButton.min.css';
import {<PERSON>} from "react-router-dom";

interface LinkButtonProps {
	text: string,
	linkTo: string,
	type: "primary" | "success" | "warning" | "danger",
	openInNewTab?: boolean,
	width?: string,
	height?: string,
	outlined?: boolean,
	additionalClassList?: string[],
	disabled?: boolean, 
}

export default function LinkButton(props: LinkButtonProps) {
	let buttonClassList = ['button', `is-${props.type}`];
	if (props.outlined) buttonClassList.push('is-outlined');
	if (props.additionalClassList) buttonClassList.push(...props.additionalClassList);

	return (
		<Link to={props.linkTo}
					target={props.openInNewTab ? "_blank" : ""}
					className={buttonClassList.join(' ')}
					style={{width: props.width, height: props.height, pointerEvents: props.disabled ? "none" : "auto", opacity: props.disabled ? 0.6 : 1}}>
			{props.text}
		</Link>
	)
}
