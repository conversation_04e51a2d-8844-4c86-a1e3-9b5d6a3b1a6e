$page-sides-spacing: 8rem;
$page-sides-spacing-sm: 2rem;
$page-sides-spacing-xl: 18rem;

// bulma overrides
$navbar-padding-vertical: 0.6rem;
$navbar-item-img-max-height: unset;

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/components/navbar";

.navbar.is-spaced {
  padding: 0.8rem $page-sides-spacing;

  @include from(2500px) {
    padding: 0.8rem $page-sides-spacing-xl;
  }

  @include until(1500px) {
    padding: 0.3rem $page-sides-spacing-sm;
  }
}

.navbar-item-font {
  font-size: 1.3rem;
  font-weight: normal;
}

.navbar-item-spacing {
  padding-left: 1.45rem;
  padding-right: 1.45rem;
}