/* eslint-disable react-hooks/exhaustive-deps */
import './ProTips.min.css';
import {useEffect, useState} from "react";

export interface Tip {
	tip_id: number,
	text: string
}

interface ProTipsProps {
	tips: Array<Tip>
	additional_classes?: Array<string>
}

export default function ProTips(props: ProTipsProps) {
	// Load a random one as the starting tip
	const [currentTip, setCurrentTip] = useState<Tip>(props.tips[Math.floor(Math.random() * props.tips.length)]);

	const TIPS_CYCLE_TIME = 5000 // milliseconds

	let mainClassList = ["notification", "is-primary"]
	if (props.additional_classes) {
		mainClassList.push(...props.additional_classes);
	}

	// Needed to make sure the interval clears when react renders each time.
	useEffect(() => {
		const switchTipsInterval = setInterval(switchTips, TIPS_CYCLE_TIME);
		return () => clearInterval(switchTipsInterval);
	}, []);

	/**
	 * Selects and sets next tip which is different from the current one, only if there are more than one tips
	 */
	function switchTips() {
		let nextTip: Tip;

		// to prevent infinite do...while loop
		if (props.tips.length > 1) {
			do {
				nextTip = props.tips[Math.floor(Math.random() * props.tips.length)]  // selects a tip at random from array
			} while (nextTip.tip_id === currentTip.tip_id);
			setCurrentTip(nextTip);
		}
	}

	if (props.tips.length > 0) {
		return (
			<div className={mainClassList.join(' ')}>
				<b>Pro Tip:</b> {currentTip.text}
			</div>
		)
	} else {
		console.log("No Pro Tips have been set up");
		return <></>
	}
}