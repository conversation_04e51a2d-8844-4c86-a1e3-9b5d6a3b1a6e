import { CSSProperties, ReactNode } from "react";

interface CardProps {
	children: ReactNode
	className?: string
	cardHeaderTitle?: string
	style?: CSSProperties;
}

export default function Card(props: CardProps) {
	return (
		<div className={`card ${props.className ? props.className : ""}`} style={props.style}>
			{props.cardHeaderTitle && <div className={"card-header"}>
				<div className={"card-header-title"}>
					{props.cardHeaderTitle}
				</div>
			</div>}
			<div className={"card-content"}>
				<div className={"content"}>
					{props.children}
				</div>
			</div>
		</div>
	)
}
