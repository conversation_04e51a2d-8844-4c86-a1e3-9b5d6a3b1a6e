@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";


@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/components/menu";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/helpers/spacing";
@import "bulma/sass/components/dropdown";

@import "../../assets/bulma-overrides/bulmaOverrides";
@import "../../assets/custom-global-styles/customGlobablStyles";

$sidebar-edge-spacing: .5rem;
$sidebar-top-extra: 0.8rem;
$sidebar-header-height: 3.75rem;
$sidebar-footer-height: 4rem;

.new-sidebar * {
  font-family: $primary-font !important;
}

.new-sidebar {
  display: inline-flex;
  flex-direction: column;
  justify-content: space-between;
  width: 18rem;
  height: 100%;
  background-color: #f2f6fc;
  // box-shadow: -12px 0px 46px #0000000F;
  overflow: hidden !important;
  position: fixed;
  z-index: 6; // sidebar should not overlap with subscription page
  // transition: ease-in-out 100ms;
  zoom: 100%;
  // transition: width 0.3s ease;

  section {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;

    /*Right pointing*/
    .triangle-btn {
      position: fixed;
      top: 1rem;
      left: 17rem;
      width: 1rem;
      height: 0;
      display: none;
      padding-top: 1rem;
      padding-bottom: 1rem;
      overflow: hidden;
      z-index: 1;
      cursor: pointer;

      @media (max-width:1098px){
        left:14.5rem;
      }
    }

    .triangle-btn:after {
      content: "";
      display: block;
      width: 0;
      height: 0;
      margin-top: -500px;
      border-top: 500px solid transparent;
      border-bottom: 500px solid transparent;
      border-right: 500px solid #ddd;
      ;
    }
  }

  &.collapsed {
    width: 5.5rem;
    transition: ease-in-out 100ms;

    section {

      .arrow {
        display: none;
        position: absolute;
        background-color: inherit;
        transform: translateY(-50%) rotate(134deg);
        z-index: 4;
        top: 50%;
      }

      .logo-container {
        padding: 0;
        position: relative;
        display: flex;
        justify-content: center;
        // background-color: $white;
        cursor: pointer;

        .hamburger {
          display: none;
        }

        .abun-logo {
          width: 3rem;
          height: auto;
          cursor: pointer;
          display: block;
        }

        /*Right pointing*/
        .triangle-btn {
          position: fixed;
          top: 1rem;
          left: 5.5rem;
          width: 0;
          height: 0;
          padding-top: 1rem;
          padding-bottom: 1rem;
          padding-left: 1rem;
          overflow: hidden;
          z-index: 1;
          cursor: pointer;
        }

        .triangle-btn:after {
          margin-left: -500px;
          border-left: 500px solid #ddd;
        }
      }

      .link-text {
        display: none;
      }

      .drop-down-arrow{
        display: none !important;
      }

      .subcategory-menu a{
        display: inline-flex !important;
      }

      .subcategory-menu {
        position: relative;

        &:hover {

          .link-text {
            display: inline;
            position: fixed;
            width: max-content;
            left: 4rem;
            padding: 0.5rem;
            background-color: $white;
            border-radius: 0.25rem;
            z-index: 5;
            border: none;
            box-shadow: 0 0.5em 1em -0.125em rgba(0, 0, 0, 0.1), 0 0px 0 1px rgba(0, 0, 0, 0.02);
          }
        }
      }

      .dropdown-menu {
        position: fixed;
        top: 4rem;
        left: 3.5rem;
        width: 10.7rem !important;
      }

    .subcategory-menu{
        .dropdown-menu-container{
          display: inline;
          position: fixed;
          width: max-content;
          background-color: $white;
          border-radius: 10px;
          box-shadow: 0 0.5em 1em -0.125em rgba(0, 0, 0, 0.1), 0 0px 0 1px rgba(0, 0, 0, 0.02);
          left: 4rem;
          z-index: 6;
          border-left: none;
          margin-top: -0.4rem !important;
          padding: 0.7rem 0 0 0 !important;
        }
      }
    }
  }

  @include until(1100px) {
    width: 15.5rem;
  }

  // Sidebar header logo & hamburger
  @include until($tablet) {
    width: 100%;
    height: 5rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
    overflow: hidden;
    background-color: #fff;
    justify-content: unset;
  }

  &.full-height {
    height: 100% !important;

    @include until($tablet) {
     background-color: $white;
    }
  }

  .mobile-extra {
    padding-left: $sidebar-edge-spacing;
    display: none;

    .mobile-logo {
      width: auto;
      height: 3em;
    }

    .hamburger {
      display: flex;
      align-items: center;
      cursor: pointer;
    }

    @include until($tablet) {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }

  .sidebar-header {
    position: relative;
    min-width: 0rem;
    max-width: 100%;
    height: $sidebar-header-height;
    // background-color: $white;
    z-index: 3;
    display: flex;
    flex-direction: column;
    align-items: center;

    .logo-container {
      position: relative;
      width: 100%;
      height: auto;
      min-height: 3.75rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      @include until($tablet) {
        display: none;
      }

      .hamburger {
        width: 40%;
        height: auto;
      }

      img {
        width: 6rem;
        height: auto;
      }
    }

    ul {
      width: 90%;
      margin: 0 !important;
      margin-top: .25rem !important;
    }

  }

  .sidebar-items {
    position: relative;
    min-width: 0rem;
    max-width: 18rem;
    height: calc(100% - $sidebar-header-height - $sidebar-footer-height);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: transparent transparent;
    z-index: 2;
    display: flex;
    flex-direction: column;
    // justify-content: space-between;

    // show scrollbar only on hover
    &:hover {
      transition: ease-in-out 250ms;
      scrollbar-width: thin;
      scrollbar-color: #eaeff7 transparent;
    }

    @include until($tablet) {
      max-width: 100%;
    }
    
  }

  .sidebar-footer {
    // should be fixed at the bottom of the sidebar
    position: relative;
    width: 18rem;
    height: $sidebar-footer-height;
    // background-color: white;
    margin: 0;
    padding-top: 5px;
    z-index: 3;

    border-top: 1px solid $grey;

    li,
    a {
      margin: 0 !important;
    }

    &.footer-position{
      @include until($tablet) {
        position: static;
        width:100%;
        bottom: 1px;
        margin-bottom: 2.3rem;
        background-color: $white;
        // display: none;
      }
    }
  }

  hr {
    margin: 0 $sidebar-edge-spacing;
    background-color: $grey;
    height: 1px;
  }

  a.settings {
    display: block;
    text-align: left;
  }


  .align-image-text-vertically {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
  }
  
  .sidebar-user-website-list-item {
    &:hover {
      //background-color: $light !important;
      cursor: pointer !important;
    }
  }
  
  .sidebar-connect-website-list-item {
    // background-color: $success;
    color: $black !important;
    border-bottom: 1px solid #e6e7f3;
    // img {
    //   filter: invert(100%) sepia(0%) saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%);
    // }

    svg {
      border: 1px solid #e6e7f3;
      border-radius: 7px;
      color: #52516e;
      padding: 0.1rem;
    }
  
    &:hover {
      background-color: #fafafd  !important;
    }
  }

  .profile-dropdown-conatiner{
    display: flex;
    gap: 10px;
    padding: 0 !important;
    flex-direction: column;
    align-items: normal !important;

    &:hover{
      background-color: $white !important;
    }
  }
  
  .sidebar-website-list-logo {
    vertical-align: middle;
  }
  
  
  .subcategory-menu {
    a {
      display: flex !important;
      align-items: center !important;
      font-size: 0.9rem;
      border-radius: 10px;
      color: #000 !important;
      padding: 0.3em 0.7em !important;
  
      &:hover,
      &.is-active {
         background-color: #e6e7f3 !important;
        // color: $primary !important;
  
        // svg {
        //   fill: #e6e7f3 !important;
        // } 
      }
  
      svg {
        width: 1.1rem;
        height: 1.1rem;
        fill: transparent !important;
      }

      .drop-down-arrow{
        flex: 1;
        display: inline-flex;
        justify-content: flex-end;
        padding-right: 0.4rem;

        .arrow-svg{
          width: 1rem !important;
          height: auto;
          transition: transform 0.3s ease-in-out;
        }
      }
    }
  
    // dropdown section item
    .dropdown-menu-container{
      padding-right: 0px;
      margin-right: 0px;
      margin-left: 1rem !important;
      padding-left: 0.2rem !important;
      margin-top: 5px !important;

      li a {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          border-radius: 7px;
          color: #000 !important;
          margin-right: 23px;
          margin-left: 14px;
          margin-bottom: 5px;
          padding: 0.3em 0.7em !important;
          font-size: 0.9rem;
  
          &:hover,
          &.is-active {
             background-color: #e6e7f3 !important;
            // color: $primary !important;
          }
       }

       span.tag {
        font-size: 0.75rem;
        margin-left: 0rem;
      }
    }

    a.coming-soon {
      cursor: default !important;
      pointer-events: none !important;
  
      &:hover {
        background: transparent !important;
        color: $black !important;
      }
    }
  
    a.upgrade {
      word-wrap: break-word;
    }
  }
  
  // upgrade, coming soon text css
  .subcategory-menu.autoArticles {
    .category {
      display: flex;
      flex-direction: row;
      align-items:flex-start;
      justify-content: space-between;

      .svg-div{
        margin-left: 0.79rem;

      }

      svg{
        margin-top: 2px;
      }
    }
  
    a.upgrade {
      word-wrap: break-word;
      display: flex !important;
      flex-direction: row !important;
      justify-content: space-between !important;
    }
  
    // span span {
    //   display: none;
    // }
  }

  .upgrade-menu{
      background-color: #edfcfd !important;
      color: #117c83 !important;
      align-items: center;
      display: inline-flex;
      font-size: .65rem;
      height: 2em;
      justify-content: center;
      line-height: 1.5;
      padding-left: .75em;
      padding-right: .75em;
      white-space: nowrap;
      border-radius: 9999px;
    }
  
  .deals-card {
    background: #E2F1FF;
    border-radius: 0.75rem;
    padding: 1rem;
    margin: 1rem;
    position: relative;
    min-height: 6.25rem;
    max-height: 6.25rem;
    cursor: pointer;
  
    .content {
      color: $primary;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      height: 100%;
  
      h3.title {
        color: $primary !important;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.1rem;
        margin: 0;
      }
  
      .subtitle {
        color: $primary !important;
        font-size: .8125rem;
        font-weight: 400;
        margin: 0 !important;
        line-height: 1.1rem;
      }
  
      .code {
        color: $primary !important;
        font-size: .8125rem;
        font-weight: 400;
        line-height: 1.1rem;
      }
    }
  }
  
  .menu-list {
    margin-left: .5rem;
  
    .dropdown {
      width: 100% !important;
  
      .dropdown-trigger {
        button {
          // width: 96.4%;
          // margin: auto;
          text-align: left;
          padding: 0.325rem;
          // border: 1px solid #B3B3B3;
          border: none;
          background-color: transparent;
          color: $black;
          font-size: 1rem;
          font-weight: 400;
          line-height: 1.5rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-radius: 10px;

          &:focus{
            box-shadow: none;
          }
  
          &:hover {
            background-color: #e6e7f3;
          }
  
          img {
            width: 1.75rem;
            height: 1.75rem;
            border-radius: 50%;
          }
  
          // down arrow
          svg {
            width: .875rem;
            height: .875rem;
          }

          @media (min-width:768px) {
            .truncate-charcter{
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              max-width: 179px;
            }
          }
        }
      }
  
      .dropdown-menu {
        width: 100%;
  
        .dropdown-content {
          width: 15rem;
          position: fixed;
          top: 4.7rem;
          left: 18.3rem;
          border: 1px solid #e6e7f3;
          border-radius: 8px;
          padding: 4px;
          // margin: auto;
          margin-top: 0rem;
          overflow-x: hidden;
          max-height: 512px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);

            @include until($tablet) {
              position: static;
              width: auto;
              padding: 0;
              margin-right: 8px;
          }

        }

        .profile-content{
          bottom: 1% !important;
          top: auto !important;
          padding: 0;

             @include until($tablet) {
              position: fixed;
              left: 9px;
              width: 95%;
              bottom: 66px !important;
           }
        }
  
        .dropdown-item {
          padding: 0.5rem;
          font-size: 1rem;
          font-weight: 400;
          line-height: 1.5rem;
          color: $black;
          display: flex;
          align-items: center;
  
          &:hover {
            background-color: #fafafd;
          }
  
          img {
            width: 1.75rem;
            height: 1.75rem;
            border-radius: 50%;
          }
  
          .sidebar-add-website {
            display: flex;
            align-items: center;
            border-radius: 7px;
  
            &:hover {
              cursor: pointer;
  
            }
  
          }

          .profile-section{
            display: flex;
            align-items: center;
            padding: 0.4rem;
            font-size: 0.9rem;
            color: #52516e;
            cursor: pointer;

            &:hover {
              background-color: #fafafd;
            }
          }
  
        }
      }
    }
  }

  &.collapsed .menu-list{
    margin-left: 1.2rem !important;
    padding-left: 0.2rem !important;

    .dropdown{
      width: fit-content !important;
    }
  } 

  
  &.collapsed {
    .sidebar-header {
      min-width: 5.5rem !important;
      max-width: 5.5rem !important;
    }
  
    .sidebar-items {
      min-width: 5.5rem !important;
      max-width: 5.5rem !important;
      overflow-x: hidden;
    }
  
    .sidebar-footer {
      width: 5rem !important;

      @media (max-height: 1070px ) {
        width:5.5rem !important;
        overflow-x: hidden;
        overflow-y: scroll;
        scrollbar-color: transparent transparent;
        scrollbar-width: thin;
      }
    }
    
    .uncollapsed-tag {
      display: none;
    }
  
    .deals-card {
      display: none;
    }
  
    .menu-list .dropdown .dropdown-trigger button {
      width: 100% !important;
  
      svg {
        display: none;
      }
    }
    // Note: collapsed Item become center 
    // .subcategory-menu a svg {
    //   margin: auto;
    // }
  
    // collapsed Upgrade text
    .subcategory-menu.autoArticles {
      .category {
        display: flex;
        flex-direction: row;
        // align-items: center;
        justify-content: space-between;
      }
  
      a.upgrade {
        word-wrap: break-word;
        display: flex !important;
        flex-direction: column !important;
        justify-content: space-between !important;
        justify-self: flex-start !important;
      }
    }
  
  
  }

  //dropdown css 
  .subcategory-product {
    .product-dropdown-conatiner{
      margin-left: 2.7rem;
      width: fit-content;

      .dropdown-content{
        box-shadow: none;
        background: none;
        display: flex;
        flex-direction: column;
      }
    }
  }

  &.collapsed section .subcategory-product{
    .product-dropdown-conatiner{
      display: inline;
      position: fixed;
      width: max-content;
      background-color: $white;
      left: 2.3rem;
      z-index: 6;
    }
  }
}

