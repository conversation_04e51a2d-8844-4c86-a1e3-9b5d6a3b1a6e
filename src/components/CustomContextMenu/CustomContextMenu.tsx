import { MouseEvent, ReactNode, useEffect, useState } from 'react';

export interface CustomContextkMenuProps {
  children: ReactNode;
  url: string;
  normalClick: () => void
  CtrlOrMetaClick: () => void
}

export default function CustomContextMenu(props: CustomContextkMenuProps) {
  const [showMenu, setShowMenu] = useState(false);

  const handleContextMenu = (e: MouseEvent<HTMLDivElement>) => {
    e.preventDefault();
    setShowMenu(true);
  };

  const handleClick = () => {
    setShowMenu(false);
  };

  const handleCustomOptionClick = () => {
    window.open(props.url, '_blank');
    setShowMenu(false);
  };

  useEffect(() => {
    document.addEventListener('click', handleClick);
    return () => {
      document.removeEventListener('click', handleClick);
    };
  }, []);

  return (
    <div
      onClick={(event) => {
        if (event?.ctrlKey || event?.metaKey) {
          props.CtrlOrMetaClick();
        } else if (!showMenu) {
          props.normalClick();
        }
      }}
      onContextMenu={handleContextMenu} style={{ display: 'inline-block' }}>
      {props.children}
      {showMenu && (
        <div
          style={{
            position: 'absolute',
            backgroundColor: 'white',
            border: '1px solid #ccc',
            boxShadow: '0px 0px 10px rgba(0, 0, 0, 0.1)',
            zIndex: 1000,
            padding: '5px'
          }}
        >
          <div
            onClick={handleCustomOptionClick}
            style={{ padding: '8px 12px', cursor: 'pointer' }}
          >
            Open in New Tab
          </div>
        </div>
      )}
    </div>
  );
}
