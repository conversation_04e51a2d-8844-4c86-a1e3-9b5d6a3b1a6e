import React from 'react';
import AbunLoader from '../AbunLoader/AbunLoader';

const LoadingFallback: React.FC = () => {
  return (
    <div className="loading-fallback-container" style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      width: '100%',
      position: 'fixed',
      top: 0,
      left: 0,
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      zIndex: 9999
    }}>
      <AbunLoader show={true} height="20vh" />
    </div>
  );
};

export default LoadingFallback;
