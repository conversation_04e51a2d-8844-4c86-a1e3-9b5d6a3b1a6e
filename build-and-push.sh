#!/bin/zsh

GITLAB_CONTAINER_URL='registry.gitlab.com/aminmemon/abun-react-frontend'
IMAGE_NAME=''
DOCKERFILE=''

if [[ "$1" == '--staging' ]]
then
    IMAGE_NAME='abun-react-frontend-staging'
    DOCKERFILE='Dockerfile.staging'
elif [[ "$1" == '--prod' ]]
then
    IMAGE_NAME='abun-react-frontend-prod'
    DOCKERFILE='Dockerfile.prod'
else
    echo "Please provide valid argument --dev, --staging or --prod depending on the environment you want to build images for."
    exit 1
fi

read -r "VERSION?Enter Version Number (ex. v1.0.0): "
printf "\n"

IMAGE_URL="$GITLAB_CONTAINER_URL"/"$IMAGE_NAME":"$VERSION"

printf "Proceeding to build and push image %s\n" "$IMAGE_URL"

# ------ remove existing image - if any
docker image rm "$IMAGE_URL"

# ------ build the image
docker buildx build --platform linux/amd64 -f "$DOCKERFILE" -t "$IMAGE_URL" .

# ------ push the image
docker push "$IMAGE_URL"
