const fs = require('fs');
const path = require('path');
const sass = require('sass');
const { exec } = require('child_process');
const os = require('os');
const util = require('util');

// Convert exec to promise-based
const execPromise = util.promisify(exec);

// Display help message
function showHelp() {
  console.log(`
SCSS Optimization Script

Usage: node optimize-all-scss.js [options]

Options:
  --dir=<path>            Directory to search for SCSS files (default: src)
  --concurrency=<number>  Number of files to process concurrently (default: auto)
  --verbose               Show detailed output
  --exclude=<dirs>        Additional directories to exclude, comma-separated
  --help                  Show this help message

Examples:
  node optimize-all-scss.js --dir=src/components
  node optimize-all-scss.js --concurrency=4 --verbose
  node optimize-all-scss.js --exclude=vendor,temp
`);
  process.exit(0);
}

// Parse command line arguments
function parseArgs() {
  const args = {
    directory: 'src',
    excludeDirs: ['node_modules', 'build', 'public'],
    concurrency: 0,
    verbose: false,
    help: false
  };

  // Process command line arguments
  process.argv.slice(2).forEach(arg => {
    if (arg.startsWith('--dir=')) {
      args.directory = arg.split('=')[1];
    } else if (arg.startsWith('--concurrency=')) {
      args.concurrency = parseInt(arg.split('=')[1], 10);
    } else if (arg === '--verbose') {
      args.verbose = true;
    } else if (arg.startsWith('--exclude=')) {
      const excludes = arg.split('=')[1].split(',');
      args.excludeDirs = [...args.excludeDirs, ...excludes];
    } else if (arg === '--help' || arg === '-h') {
      args.help = true;
    }
  });

  return args;
}

// Configuration
const args = parseArgs();

// Show help if requested
if (args.help) {
  showHelp();
}

const srcDir = args.directory;
const excludeDirs = args.excludeDirs;
const VERBOSE = args.verbose;

// Determine concurrency based on CPU cores (use 75% of available cores, minimum 2)
const cpuCount = os.cpus().length;
const MAX_CONCURRENT = Math.max(2, Math.floor(cpuCount * 0.75));

// Use provided concurrency or default to calculated value
const CONCURRENCY = args.concurrency > 0 ? args.concurrency : MAX_CONCURRENT;

console.log(`\nSCSS Optimization Script`);
console.log(`Directory: ${srcDir}`);
console.log(`Excluded directories: ${excludeDirs.join(', ')}`);
console.log(`Using concurrency: ${CONCURRENCY} (max ${MAX_CONCURRENT} based on CPU cores)`);
console.log(`Verbose mode: ${VERBOSE ? 'enabled' : 'disabled'}`);


// Function to find all SCSS files
function findScssFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && !excludeDirs.includes(file)) {
      findScssFiles(filePath, fileList);
    } else if (file.endsWith('.scss') && !file.startsWith('_')) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Process a single SCSS file asynchronously
async function processScssFile(scssFile) {
  const outputFile = scssFile.replace('.scss', '.min.css');
  const tempCssFile = outputFile + '.temp';
  const startTime = Date.now();

  try {
    if (VERBOSE) console.log(`Compiling ${scssFile}...`);

    // Compile with Dart Sass
    const result = sass.compile(scssFile, {
      style: 'compressed', // Use compressed style for initial minification
      sourceMap: false,    // Disable source maps for production
      loadPaths: ['node_modules'], // Add node_modules to the load path for imports
    });

    // Write the compiled CSS to a temporary file
    fs.writeFileSync(tempCssFile, result.css);

    if (VERBOSE) console.log(`Optimizing ${scssFile} with PostCSS...`);

    // Process with PostCSS for further optimization
    await execPromise(`npx postcss ${tempCssFile} --use autoprefixer cssnano --no-map -o ${outputFile}`);

    // Clean up temporary file
    fs.unlinkSync(tempCssFile);

    // Get final file size
    const finalSize = fs.statSync(outputFile).size;
    const processingTime = ((Date.now() - startTime) / 1000).toFixed(2);

    return {
      file: scssFile,
      success: true,
      initialSize: result.css.length,
      finalSize,
      processingTime
    };
  } catch (error) {
    const errorMsg = `Error processing ${scssFile}: ${error.message}`;
    if (VERBOSE) console.error(errorMsg);

    // Try to clean up temp file if it exists
    try {
      if (fs.existsSync(tempCssFile)) {
        fs.unlinkSync(tempCssFile);
      }
    } catch (cleanupError) {
      // Ignore cleanup errors
    }

    return {
      file: scssFile,
      success: false,
      error: error.message,
      processingTime: ((Date.now() - startTime) / 1000).toFixed(2)
    };
  }
}

// Process files in batches with progress tracking
async function processBatch(files, batchSize) {
  const results = [];
  const totalFiles = files.length;
  let processedCount = 0;
  let successfulFiles = 0;
  let totalInitialSize = 0;
  let totalFinalSize = 0;

  // Process files in batches
  for (let i = 0; i < files.length; i += batchSize) {
    const batch = files.slice(i, i + batchSize);
    const startTime = Date.now();
    const batchNum = Math.floor(i / batchSize) + 1;
    const totalBatches = Math.ceil(files.length / batchSize);

    // Log batch start
    console.log(`\nProcessing batch ${batchNum}/${totalBatches} (${batch.length} files)...`);

    // Process batch concurrently
    const batchPromises = batch.map(async file => {
      try {
        const result = await processScssFile(file);
        processedCount++;

        if (result.success) {
          successfulFiles++;
          totalInitialSize += result.initialSize;
          totalFinalSize += result.finalSize;

          // Calculate size reduction percentage
          const reduction = ((1 - result.finalSize / result.initialSize) * 100).toFixed(1);

          // Format output
          const percent = Math.round((processedCount / totalFiles) * 100);
          const sizeInfo = `${(result.initialSize / 1024).toFixed(2)}KB → ${(result.finalSize / 1024).toFixed(2)}KB (-${reduction}%)`;
          const timeInfo = VERBOSE ? ` in ${result.processingTime}s` : '';

          console.log(`[${percent}%] ✅ ${file} ${sizeInfo}${timeInfo}`);
        } else {
          console.log(`[${Math.round((processedCount / totalFiles) * 100)}%] ❌ ${file} (failed: ${result.error})`);
        }

        return result;
      } catch (error) {
        processedCount++;
        console.log(`[${Math.round((processedCount / totalFiles) * 100)}%] ❌ ${file} (unexpected error)`);
        return {
          file,
          success: false,
          error: error.message || 'Unknown error'
        };
      }
    });

    // Wait for all files in this batch to complete
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    // Log batch completion time
    const batchTime = ((Date.now() - startTime) / 1000).toFixed(2);
    const avgTimePerFile = (batchTime / batch.length).toFixed(2);
    const batchSuccessCount = batchResults.filter(r => r.success).length;

    console.log(`Batch ${batchNum}/${totalBatches} completed in ${batchTime}s (avg: ${avgTimePerFile}s per file, ${batchSuccessCount}/${batch.length} successful)`);
  }

  return {
    results,
    stats: {
      totalFiles,
      successfulFiles,
      totalInitialSize,
      totalFinalSize
    }
  };
}

// Main function
async function main() {
  console.log('Finding all SCSS files...');
  const scssFiles = findScssFiles(srcDir);
  console.log(`Found ${scssFiles.length} SCSS files to process.`);
  console.log(`Processing with concurrency: ${CONCURRENCY}`);

  const startTime = Date.now();

  try {
    // Process all files with the specified concurrency
    const { results, stats } = await processBatch(scssFiles, CONCURRENCY);

    // Calculate statistics
    const successCount = results.filter(r => r.success).length;
    const totalTime = ((Date.now() - startTime) / 1000).toFixed(2);

    // Calculate size reduction
    const { totalInitialSize, totalFinalSize } = stats;
    const reduction = totalInitialSize > 0 ? ((1 - totalFinalSize / totalInitialSize) * 100).toFixed(2) : 0;

    // Calculate average processing time
    const avgTimePerFile = results.length > 0 ?
      (results.reduce((sum, r) => sum + parseFloat(r.processingTime || '0'), 0) / results.length).toFixed(2) : 0;

    console.log('\n=== Summary ===');
    console.log(`Total time: ${totalTime} seconds (avg: ${avgTimePerFile}s per file)`);
    console.log(`Successfully processed: ${successCount}/${scssFiles.length} files (${Math.round(successCount / scssFiles.length * 100)}%)`);
    console.log(`Total size reduction: ${(totalInitialSize / 1024).toFixed(2)}KB → ${(totalFinalSize / 1024).toFixed(2)}KB (${reduction}%)`);
    console.log(`Average file size: ${(totalInitialSize / successCount / 1024).toFixed(2)}KB → ${(totalFinalSize / successCount / 1024).toFixed(2)}KB`);

    if (successCount < scssFiles.length) {
      const failedResults = results.filter(r => !r.success);
      console.log('\nFailed files:');
      failedResults.forEach(r => {
        console.log(`- ${r.file}: ${r.error}`);
      });

      if (failedResults.length > 5) {
        console.log(`\nShowing ${failedResults.length} failed files. Run with --verbose for more details.`);
      }
    }

    console.log('\nTo use these optimized CSS files:');
    console.log('1. Import them directly in your HTML or');
    console.log('2. Import them in your JavaScript component files');
  } catch (error) {
    console.error('Error during processing:', error);
    process.exit(1);
  }
}

// Run the main function
main().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});
